// static/js/loader.js (<PERSON> ve <PERSON>llen<PERSON>)

(function() {
    const loaderScript = document.getElementById('livechatai-loader-script');
    if (!loaderScript) return;

    const hotelId = loaderScript.getAttribute('data-hotel-id');
    const serverUrl = 'https://resepsiyonapi.rehberim360.com';

    if (!hotelId) {
        console.error('LiveChatAI: Hotel ID (data-hotel-id) is missing.');
        return;
    }

    let cssLoaded = false;
    let socketIoLoaded = false;

    // Başlatıcı fonksiyon: Tüm bağımlılıklar yüklendiğinde çalışır
    function launchWidget() {
        if (cssLoaded && socketIoLoaded) {
            
            // 1. IMPORTMAP'İ OLUŞTUR
            const importmap = document.createElement('script');
            importmap.type = 'importmap';
            importmap.textContent = JSON.stringify({
                imports: {
                    "three": `${serverUrl}/static/js/libs/three.module.js`,
                    "three/addons/": `${serverUrl}/static/js/libs/`
                }
            });
            if (!document.querySelector('script[type="importmap"]')) {
                document.head.appendChild(importmap);
            }

            // 2. WIDGET AYARLARINI HAZIRLA
            window.myChatWidgetSettings = {
                hotelIdentifier: hotelId,
                backendUrl: serverUrl
            };

            // 3. ASIL WIDGET'I YÜKLE
            const widgetScript = document.createElement('script');
            widgetScript.type = 'module';
            // DÜZELTME: Artık orijinal dosya yerine sıkıştırılmış ve korunmuş olan .min.js dosyasını yüklüyoruz.
            widgetScript.src = `${serverUrl}/static/js/widget.js`; 
            document.body.appendChild(widgetScript);

            console.log("LiveChatAI: Widget başlatıldı! (Korunmuş mod)");
        }
    }

    // CSS'i yükle
    const css = document.createElement('link');
    css.rel = 'stylesheet';
    css.href = `${serverUrl}/static/css/my-chat-widget.css`;
    css.onload = () => {
        cssLoaded = true;
        console.log("LiveChatAI: CSS yüklendi.");
        launchWidget();
    };
    css.onerror = () => {
        console.error("LiveChatAI: CSS yüklenemedi.");
    }
    document.head.appendChild(css);


    // Socket.IO'yu yükle
    if (typeof io === 'undefined') {
        const socketioScript = document.createElement('script');
        socketioScript.src = 'https://cdn.socket.io/4.7.5/socket.io.min.js';
        socketioScript.onload = () => {
            socketIoLoaded = true;
            console.log("LiveChatAI: Socket.IO yüklendi.");
            launchWidget();
        };
        socketioScript.onerror = () => {
             console.error("LiveChatAI: Socket.IO yüklenemedi.");
        }
        document.head.appendChild(socketioScript);
    } else {
        socketIoLoaded = true;
        launchWidget();
    }

})();