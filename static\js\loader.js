// static/js/loader.js (Hata Düzeltilmiş Versiyon)

(function() {
    'use strict';

    console.log('LiveChatAI: Loader başlatılıyor...');

    const loaderScript = document.getElementById('livechatai-loader-script');
    if (!loaderScript) {
        console.error('LiveChatAI: Loader script elementi bulunamadı.');
        return;
    }

    const hotelId = loaderScript.getAttribute('data-hotel-id');

    // Server URL'yi script src'den otomatik çıkar
    const scriptSrc = loaderScript.src;
    const serverUrl = scriptSrc.replace(/\/+static\/js\/loader\.js.*$/, ''); // Çift slash'ları temizle

    console.log('LiveChatAI: Server URL:', serverUrl);
    console.log('LiveChatAI: Hotel ID:', hotelId);

    if (!hotelId) {
        console.error('LiveChatAI: Hotel ID (data-hotel-id) eksik.');
        return;
    }

    let cssLoaded = false;
    let socketIoLoaded = false;
    let widgetLaunched = false;

    // Başlatıcı fonksiyon: Tüm bağımlılıklar yüklendiğinde çalışır
    function launchWidget() {
        if (cssLoaded && socketIoLoaded && !widgetLaunched) {
            widgetLaunched = true;

            console.log('LiveChatAI: Widget başlatılıyor...');

            try {
                // 1. IMPORTMAP'İ OLUŞTUR
                const existingImportMap = document.querySelector('script[type="importmap"]');
                if (!existingImportMap) {
                    const importmap = document.createElement('script');
                    importmap.type = 'importmap';
                    importmap.textContent = JSON.stringify({
                        imports: {
                            "three": `${serverUrl}/static/js/libs/three.module.js`,
                            "three/addons/": `${serverUrl}/static/js/libs/`
                        }
                    });
                    document.head.appendChild(importmap);
                    console.log('LiveChatAI: Import map oluşturuldu.');
                }

                // 2. WIDGET AYARLARINI HAZIRLA
                window.myChatWidgetSettings = {
                    hotelIdentifier: hotelId,
                    backendUrl: serverUrl
                };
                console.log('LiveChatAI: Widget ayarları hazırlandı.');

                // 3. ASIL WIDGET'I YÜKLE
                const widgetScript = document.createElement('script');
                widgetScript.type = 'module';
                widgetScript.src = `${serverUrl}/static/js/widget.js`;

                widgetScript.onload = () => {
                    console.log('LiveChatAI: Widget script yüklendi.');
                };

                widgetScript.onerror = (error) => {
                    console.error('LiveChatAI: Widget script yüklenemedi:', error);
                    widgetLaunched = false; // Tekrar deneme için
                };

                document.body.appendChild(widgetScript);
                console.log('LiveChatAI: Widget script eklendi.');

            } catch (error) {
                console.error('LiveChatAI: Widget başlatma hatası:', error);
                widgetLaunched = false;
            }
        }
    }

    // CSS'i yükle
    function loadCSS() {
        // Zaten yüklenmiş mi kontrol et
        const existingCSS = document.querySelector(`link[href*="my-chat-widget.css"]`);
        if (existingCSS) {
            cssLoaded = true;
            console.log('LiveChatAI: CSS zaten yüklü.');
            launchWidget();
            return;
        }

        const css = document.createElement('link');
        css.rel = 'stylesheet';
        css.href = `${serverUrl}/static/css/my-chat-widget.css`;
        css.onload = () => {
            cssLoaded = true;
            console.log('LiveChatAI: CSS başarıyla yüklendi.');
            launchWidget();
        };
        css.onerror = (error) => {
            console.error('LiveChatAI: CSS yüklenemedi:', error);
            console.error('LiveChatAI: CSS URL:', css.href);
        };
        document.head.appendChild(css);
        console.log('LiveChatAI: CSS yükleniyor...');
    }

    // Socket.IO'yu yükle
    function loadSocketIO() {
        if (typeof window.io !== 'undefined') {
            socketIoLoaded = true;
            console.log('LiveChatAI: Socket.IO zaten yüklü.');
            launchWidget();
            return;
        }

        const socketioScript = document.createElement('script');
        socketioScript.src = 'https://cdn.socket.io/4.7.5/socket.io.min.js';
        socketioScript.onload = () => {
            socketIoLoaded = true;
            console.log('LiveChatAI: Socket.IO başarıyla yüklendi.');
            launchWidget();
        };
        socketioScript.onerror = (error) => {
            console.error('LiveChatAI: Socket.IO yüklenemedi:', error);
            console.error('LiveChatAI: Socket.IO URL:', socketioScript.src);
        };
        document.head.appendChild(socketioScript);
        console.log('LiveChatAI: Socket.IO yükleniyor...');
    }

    // Yükleme işlemlerini başlat
    loadCSS();
    loadSocketIO();

})();