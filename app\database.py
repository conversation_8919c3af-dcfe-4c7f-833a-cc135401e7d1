# app/database.py (Tam ve Güncellenmiş Kod - hotels tablosu eklendi)

import sqlite3
import json
from datetime import datetime
from app import app_logger

# config modülünden veritabanı dosyası adını ve statü kodlarını içe aktar
from .config import DATABASE_FILE, STATUS_AI_PENDING_HUMAN


def get_db_connection():
    """Veritabanına yeni bir bağlantı oluşturur ve döndürür."""
    conn = sqlite3.connect(DATABASE_FILE, timeout=10)
    conn.row_factory = sqlite3.Row
    return conn

def init_db():
    """Veritabanı şemasını başlatır veya günceller. Uygulama ilk çalıştığında çağrılır."""
    conn = get_db_connection()
    cursor = conn.cursor()

    # /// YENİ TABLO: Otel ve Entegrasyon Bilgileri İçin ///
    # Bu tablo, her bir otel<PERSON>ı<PERSON>ırmasını, bağlantılarını ve şifrelenmiş token'larını tutar.
    # hotel_config_json, eskiden hotel_configs/*.json dosyalarında tutulan veriyi içerecek.
      # --- hotels Tablosu ---
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS hotels (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            hotel_id_key TEXT UNIQUE NOT NULL,
            hotel_name TEXT NOT NULL,
            
            -- Google Entegrasyon Bilgileri
            gmb_account_id TEXT,
            gmb_location_id TEXT,
            google_refresh_token_encrypted BLOB,
            google_auth_email TEXT,
            is_google_connected INTEGER DEFAULT 0,
            
            -- /// DÜZELTİLMİŞ FACEBOOK ALANLARI ///
            is_facebook_connected INTEGER DEFAULT 0,
            facebook_page_id TEXT,
            facebook_page_name TEXT,
            facebook_page_token_encrypted BLOB,

            -- Otelin ana yapılandırması
            hotel_config_json TEXT
        )
    ''')
    
    # hotels tablosuna yeni sütunların varlığını kontrol et ve yoksa ekle
    hotels_table_info = cursor.execute("PRAGMA table_info(hotels)").fetchall()
    hotels_columns = [col[1] for col in hotels_table_info]
    fb_columns_to_add = {
        'is_facebook_connected': 'INTEGER DEFAULT 0',
        'facebook_page_id': 'TEXT',
        'facebook_page_name': 'TEXT',
        'facebook_page_token_encrypted': 'BLOB'
    }
    for col, col_type in fb_columns_to_add.items():
        if col not in hotels_columns:
            print(f"'{col}' sütunu 'hotels' tablosuna ekleniyor...")
            cursor.execute(f"ALTER TABLE hotels ADD COLUMN {col} {col_type}")

    # 'reservations' tablosu... (değişiklik yok)
    cursor.execute(f'''
        CREATE TABLE IF NOT EXISTS reservations (
            id INTEGER PRIMARY KEY AUTOINCREMENT, reservation_id TEXT UNIQUE NOT NULL,
            room_type TEXT NOT NULL, check_in_date TEXT NOT NULL, check_out_date TEXT NOT NULL,
            guest_name TEXT NOT NULL, guest_surname TEXT NOT NULL, guest_email TEXT,
            guest_phone TEXT NOT NULL, num_adults INTEGER NOT NULL, num_children INTEGER DEFAULT 0,
            price_per_night_at_booking TEXT, num_nights INTEGER, total_price_at_booking TEXT,
            hotel_name TEXT, reservation_time TEXT NOT NULL, 
            status TEXT DEFAULT '{STATUS_AI_PENDING_HUMAN}',
            session_id_at_booking TEXT NULLABLE, last_status_update_time TEXT NULLABLE,
            confirmation_agent_id TEXT NULLABLE, cancellation_reason TEXT NULLABLE,
            special_requests TEXT NULLABLE
        )
    ''')

    # --- GÜVENLİ SÜTUN EKLEME KODU (REZERVASYONLAR İÇİN) ---
    reservations_table_info = cursor.execute("PRAGMA table_info(reservations)").fetchall()
    reservations_columns = [col[1] for col in reservations_table_info]
    
    if 'hotel_id_key' not in reservations_columns:
        app_logger.info("Veritabanı güncellemesi: 'hotel_id_key' sütunu 'reservations' tablosuna ekleniyor...")
        cursor.execute("ALTER TABLE reservations ADD COLUMN hotel_id_key TEXT")
    # =======================================================
    
    # 'chat_history' tablosu... (değişiklik yok)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS chat_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id TEXT NOT NULL,
            sender TEXT NOT NULL,
            message TEXT NOT NULL,
            timestamp TEXT NOT NULL,
            tool_name TEXT NULLABLE,
            tool_args TEXT NULLABLE,
            tool_response TEXT NULLABLE,
            source TEXT DEFAULT 'unknown',
            hotel_id_key TEXT -- <<< YENİ SÜTUN
        )
    ''')

    # 'room_types_inventory' tablosu... (değişiklik yok)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS room_types_inventory (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            hotel_config_key TEXT NOT NULL, room_type_name TEXT NOT NULL,
            total_quantity INTEGER NOT NULL DEFAULT 0, last_updated TEXT,
            UNIQUE (hotel_config_key, room_type_name)
        )
    ''')
    
    # 'dashboard_analysis_reports' tablosu... (değişiklik yok)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS dashboard_analysis_reports (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            report_type TEXT NOT NULL, 
            start_date TEXT NOT NULL,
            end_date TEXT NOT NULL,
            generated_at TEXT NOT NULL,
            report_data_json TEXT NOT NULL, 
            report_summary_text TEXT, 
            hotel_config_key TEXT 
        )
    ''')

    # 'google_reviews' tablosu... (değişiklik yok)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS google_reviews (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            review_id TEXT UNIQUE NOT NULL,
            reviewer_name TEXT,
            reviewer_photo_url TEXT,
            star_rating INTEGER NOT NULL,
            comment TEXT,
            create_time TEXT NOT NULL,
            update_time TEXT NOT NULL,
            reply_comment TEXT,
            reply_update_time TEXT,
            fetched_at TEXT NOT NULL,
            is_replied INTEGER DEFAULT 0,
            sentiment_analysis_json TEXT
        )
    ''')

    # Facebook Sayfa Gönderilerini ve Yorumlarını saklamak için
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS facebook_comments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            comment_id TEXT UNIQUE NOT NULL,    -- Facebook'tan gelen eşsiz yorum ID'si
            post_id TEXT NOT NULL,              -- Yorumun yapıldığı gönderinin ID'si
            sender_id TEXT NOT NULL,
            sender_name TEXT,
            message TEXT,
            created_time TEXT NOT NULL,
            -- Yanıt bilgileri
            is_replied INTEGER DEFAULT 0,
            reply_message TEXT,
            replied_at TEXT,
            -- Bizim ek verilerimiz
            hotel_id_key TEXT NOT NULL,         -- Hangi otele ait olduğu
            fetched_at TEXT NOT NULL
        )
    ''')

    # Facebook Messenger Konuşmalarını saklamak için
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS facebook_messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            message_id TEXT UNIQUE NOT NULL,    -- Facebook'tan gelen eşsiz mesaj ID'si
            conversation_id TEXT NOT NULL,      -- İki kişi arasındaki konuşmanın ID'si
            sender_id TEXT NOT NULL,            -- Mesajı gönderenin ID'si (kullanıcı veya sayfa)
            recipient_id TEXT NOT NULL,         -- Mesajı alanın ID'si
            message_text TEXT,
            created_time TEXT NOT NULL,
            -- Bizim ek verilerimiz
            hotel_id_key TEXT NOT NULL,
            is_from_user INTEGER,               -- 1: Kullanıcıdan, 0: Sayfadan (bizim yanıtımız)
            fetched_at TEXT NOT NULL
        )
    ''')

    # Demo hotel'i ekle (yoksa)
    cursor.execute("SELECT COUNT(*) FROM hotels WHERE hotel_id_key = ?", ('myplushotelataşehir_f40e4c3f',))
    if cursor.fetchone()[0] == 0:
        demo_hotel_config = {
            "hotelId": "myplushotelataşehir_f40e4c3f",
            "hotelName": "MyPlus Hotel Ataşehir",
            "hotelCity": "İstanbul",
            "hotelDescription": "Modern ve konforlu konaklama deneyimi sunan butik otel.",
            "hotelAddress": "Ataşehir, İstanbul",
            "hotelPhone": "+90 216 123 45 67",
            "hotelEmail": "<EMAIL>",
            "hotelCheckInTime": "14:00",
            "hotelCheckOutTime": "12:00",
            "aiResponseDelaySeconds": 2.5,
            "hotelPolicy": "Misafirlerimizin konforunu ön planda tutuyoruz.",
            "hotelAmenities": "WiFi, Spa, Fitness, Restaurant, Bar, Parking, Pool",
            "paymentMethods": "Nakit, Kredi Kartı, Havale",
            "nearbyAttractions": "Zorlu Center, Vadistanbul, Optimum Outlet",
            "contactHours": "24/7",
            "hotelLatitude": "41.0082",
            "hotelLongitude": "28.9784",
            "systemMessage": "Sen MyPlus Hotel Ataşehir'in AI asistanısın. Misafirlere yardımcı ol ve rezervasyon al.",
            "roomTypes": [
                {
                    "name": "Standart Oda",
                    "capacity": 2,
                    "pricePerNight": 500,
                    "features": "Klima, WiFi, Minibar, TV"
                },
                {
                    "name": "Deluxe Oda",
                    "capacity": 3,
                    "pricePerNight": 750,
                    "features": "Klima, WiFi, Minibar, TV, Balkon"
                },
                {
                    "name": "Suite",
                    "capacity": 4,
                    "pricePerNight": 1200,
                    "features": "Klima, WiFi, Minibar, TV, Balkon, Jakuzi"
                }
            ]
        }

        import json
        cursor.execute("""
            INSERT INTO hotels
            (hotel_id_key, hotel_name, hotel_config_json, is_google_connected)
            VALUES (?, ?, ?, 0)
        """, (
            'myplushotelataşehir_f40e4c3f',
            'MyPlus Hotel Ataşehir',
            json.dumps(demo_hotel_config, ensure_ascii=False)
        ))
        print("Demo hotel 'MyPlus Hotel Ataşehir' database'e eklendi.")

    conn.commit()
    conn.close()
    print("Veritabanı başlatıldı/kontrol edildi. (hotels tablosu dahil)")

def save_chat_message_to_db(session_id: str, sender: str, message: str, socketio_instance,
                            hotel_id_key: str, # <-- YENİ ZORUNLU PARAMETRE
                            tool_name: str = None, tool_args: dict = None, tool_response: dict = None,
                            source: str = 'unknown'):
    """
    Sohbet mesajını, hangi otele ait olduğu bilgisiyle birlikte veritabanına kaydeder.

    Bu fonksiyon, veri izolasyonunun sağlanmasında kritik bir rol oynar. Her mesaj,
    ilişkili olduğu 'hotel_id_key' ile etiketlenir. Bu, gelecekteki analizlerin
    ve raporlamaların otel bazında doğru bir şekilde yapılmasını sağlar.

    Ayrıca, yeni bir mesaj kaydedildiğinde, bu durumu 'live_chat_message'
    olayıyla anlık olarak tüm istemcilere (örn: Unity paneli) duyurur.

    Args:
        session_id (str): Mesajın ait olduğu sohbetin benzersiz kimliği.
        sender (str): Mesajı gönderen ('USER', 'AI', 'SYSTEM' vb.).
        message (str): Mesajın metin içeriği.
        socketio_instance: Anlık bildirim göndermek için kullanılacak SocketIO nesnesi.
        hotel_id_key (str): Mesajın ait olduğu otelin benzersiz kimliği. Bu parametre
                             veri izolasyonu için zorunludur.
        tool_name (str, optional): Eğer bu mesaj bir AI araç çağrısı ise, aracın adı.
        tool_args (dict, optional): Aracın aldığı argümanlar.
        tool_response (dict, optional): Aracın döndürdüğü sonuç.
        source (str, optional): Mesajın kaynağı ('web_widget', 'unity_dashboard' vb.).
    """
    conn = None
    new_db_id = -1

    # Eğer hotel_id_key sağlanmadıysa, bu bir programlama hatasıdır.
    # Veri bütünlüğünü korumak için kaydı engelle ve bir uyarı logla.
    if not hotel_id_key:
        app_logger.error(f"save_chat_message_to_db çağrısında 'hotel_id_key' eksik! Mesaj (Session: {session_id[:8]}) kaydedilemedi.")
        return

    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        timestamp_iso = datetime.now().isoformat(timespec='seconds')
        
        # Python dict nesnelerini veritabanında saklamak için JSON string'e çevir.
        tool_args_json = json.dumps(tool_args) if tool_args else None
        tool_response_json = json.dumps(tool_response, default=str) if tool_response else None

        # SQL sorgusunu ve parametre listesini yeni 'hotel_id_key' sütununu içerecek şekilde güncelle.
        sql_query = """
            INSERT INTO chat_history 
            (session_id, sender, message, timestamp, tool_name, tool_args, tool_response, source, hotel_id_key) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            session_id, sender, message, timestamp_iso, 
            tool_name, tool_args_json, tool_response_json, 
            source, hotel_id_key
        )
        
        cursor.execute(sql_query, params)
        conn.commit()
        new_db_id = cursor.lastrowid
        app_logger.info(f"DB Kayıt: Mesaj (DB ID: {new_db_id}, Session: {session_id[:8]}) '{hotel_id_key}' oteli için kaydedildi.")

        # Canlı sohbet izleme paneline anlık bildirim gönder.
        # Sadece web widget'tan gelen mesajları canlı izleyiciye gönderiyoruz.
        if new_db_id > 0 and source == 'web_widget':
            live_message_data = {
                "db_id": new_db_id,
                "session_id": session_id,
                "sender": sender,
                "message": message,
                "timestamp": timestamp_iso,
                "source": source
                # hotel_id_key'i de gönderebiliriz, gelecekte faydalı olabilir.
            }
            socketio_instance.emit('live_chat_message', live_message_data)
            app_logger.info(f"SOCKET_EMIT: 'live_chat_message' olayı gönderildi. (Session: {session_id[:8]})")
        
    except sqlite3.Error:
        app_logger.exception("save_chat_message_to_db içinde veritabanı hatası:")
    except Exception:
        app_logger.exception("save_chat_message_to_db içinde genel/SocketIO hatası:")
    finally:
        if conn:
            conn.close()