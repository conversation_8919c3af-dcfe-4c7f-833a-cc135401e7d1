# app/routes/socketio_handlers.py (<PERSON><PERSON>, Stateless ve <PERSON><PERSON><PERSON>)

import uuid
import time
import base64
import json
import sqlite3
from flask import request
from flask_socketio import emit, join_room
from datetime import datetime, timezone
import traceback
from app import app_logger

# --- DÜZELTME: Artık in-memory session objelerine ihtiyacımız yok ---
# <PERSON><PERSON><PERSON><PERSON>: socketio nesnesi, redis_client ve config/db fonksiyonları.
from app import socketio, redis_client
from app.config import MODEL_NAME
from app.database import get_db_connection, save_chat_message_to_db
from app.ai.prompts import generate_system_instruction_for_chat
from app.ai.tools import GEMINI_TOOLS_CONFIG
from app.ai.core import handle_gemini_response_and_tool_calls
import google.generativeai as genai
from app.stt_tts_service import synthesize_text_to_speech
from app.utils import split_text_into_sentences
from app.utils import (
    broadcast_active_chats_update, 
    save_session_to_redis, 
    get_session_from_redis, 
    delete_session_from_redis
)

# --- MİMARİ DEĞİŞİKLİK: Sid -> SessionId Eşlemesi ---
# Bir istemci disconnect olduğunda, onun socket.io SID'sinden bizim uygulama session_id'mizi
# bulabilmek için bu eşleşmeyi de Redis'te tutacağız.
# Önek farklı olsun ki normal session'larla karışmasın.
SID_TO_SESSION_PREFIX = "sid_map:"
SID_MAP_TTL = 3 * 60 * 60 + 60 # Session'dan biraz daha uzun ömürlü olsun

def map_sid_to_session(sid: str, session_id: str):
    if not redis_client: return
    redis_client.setex(f"{SID_TO_SESSION_PREFIX}{sid}", SID_MAP_TTL, session_id)

def get_session_from_sid(sid: str) -> str | None:
    if not redis_client: return None
    return redis_client.get(f"{SID_TO_SESSION_PREFIX}{sid}")

def unmap_sid(sid: str):
    if not redis_client: return
    redis_client.delete(f"{SID_TO_SESSION_PREFIX}{sid}")
# -------------------------------------------------------------


@socketio.on('connect')
def handle_socket_connect_event():
    # Sadece bağlanan istemcinin odasına katılmasını sağlayalım.
    # Bu, 'resume' veya 'start' sonrası spesifik emit'ler için önemlidir.
    join_room(request.sid)
    app_logger.info(f"WS_CONNECT: İstemci bağlandı ve kendi odasına katıldı - SID: {request.sid}")

@socketio.on('disconnect')
def handle_socket_disconnect_event(*args, **kwargs):
    """
    ENDÜSTRİ STANDARDI DISCONNECT MANTIĞI:
    Kullanıcı bağlantısı koptuğunda, oturumu 'passive' olarak işaretler ve
    listeden düşmesini sağlar, ancak veriyi bir süre daha saklar.
    """
    disconnected_sid = request.sid
    app_logger.info(f"--- [DISCONNECT] handle_socket_disconnect_event --- SID: {disconnected_sid[:6]}")

    session_id = get_session_from_sid(disconnected_sid)

    if session_id:
        # Oturumun mevcut verisini Redis'ten al
        session_data = get_session_from_redis(session_id)
        if session_data:
            # Durumunu 'passive' olarak değiştir
            session_data['presence'] = 'passive'
            # Güncellenmiş veriyi aynı TTL ile tekrar Redis'e kaydet
            save_session_to_redis(session_id, session_data)
            app_logger.info(f"[DISCONNECT] Oturum {session_id[:8]} 'passive' olarak işaretlendi.")
            
            # Liste değiştiği için, herkese güncel listeyi gönder.
            broadcast_active_chats_update()
        
        # Artık geçersiz olan SID eşleşmesini temizle
        unmap_sid(disconnected_sid)
    else:
        app_logger.info(f"[DISCONNECT] Yönetilen bir oturumla ilişkili olmayan bir SID ayrıldı: {disconnected_sid[:6]}")


@socketio.on('start_chat_from_widget')
def handle_start_chat_from_widget_event(data):
    client_sid = request.sid
    app_logger.info(f"--- [START] handle_start_chat_from_widget --- SID: {client_sid[:6]}")
    conn = None
    try:
        hotel_identifier = data.get('hotel_identifier')
        if not hotel_identifier:
            app_logger.warning("[START-FAIL] Otel ID eksik.")
            emit('error_message', {'message': "'hotel_identifier' alanı zorunludur."}, room=client_sid)
            return
            
        app_logger.info(f"[START-1] Otel ID alındı: {hotel_identifier}")
        
        conn = get_db_connection()
        hotel_row = conn.execute("SELECT hotel_config_json, hotel_name FROM hotels WHERE hotel_id_key = ?", (hotel_identifier,)).fetchone()
        
        if not hotel_row or not hotel_row['hotel_config_json']:
            app_logger.error(f"[START-FAIL] DB'de otel bulunamadı: {hotel_identifier}")
            emit('initial_chat_data', {'error': f"'{hotel_identifier}' için otel yapılandırması bulunamadı."}, room=client_sid)
            return
        
        app_logger.info(f"[START-2] DB'den otel config çekildi.")
        config_data = json.loads(hotel_row['hotel_config_json'])
        hotel_name = hotel_row['hotel_name'] or "Canlı Destek"
        session_id = str(uuid.uuid4())

        app_logger.info(f"[START-3] Redis'e kaydetme başlıyor. Session ID: {session_id[:8]}")
        session_data_for_redis = {
            'config_data': config_data, 'source': 'web_widget',
            'hotel_config_key': hotel_identifier, 'hotelName': hotel_name,
            'start_time': datetime.now(timezone.utc).isoformat(),
            'last_activity_time': time.time(),
            'presence': 'active' # Yeni sohbet her zaman aktif başlar
        }
        # Redis'e kaydet, yoksa memory'de tut
        try:
            save_session_to_redis(session_id, session_data_for_redis)
            app_logger.info(f"[START-4] Redis'e kayıt tamamlandı.")
        except Exception as e:
            # Redis yoksa, memory'de tut (fallback)
            from app import active_chat_sessions, session_lock
            with session_lock:
                active_chat_sessions[session_id] = session_data_for_redis
            app_logger.info(f"[START-4] Memory fallback'e kayıt tamamlandı.")

        map_sid_to_session(client_sid, session_id)

        initial_ai_msg = f"Merhaba! Ben {hotel_name} destek asistanınız. Size nasıl yardımcı olabilirim?"
        
        # === ANA DÜZELTME BURADA ===
        # save_chat_message_to_db çağrısına zorunlu 'hotel_id_key' parametresini ekliyoruz.
        save_chat_message_to_db(
            session_id=session_id, 
            sender="AI", 
            message=initial_ai_msg,
            socketio_instance=socketio, 
            source='web_widget',
            hotel_id_key=hotel_identifier # <<< EKSİK OLAN KRİTİK PARAMETRE
        )
        # ============================
        app_logger.info(f"[START-5] İlk mesaj DB'ye kaydedildi.")
        
        emit('initial_chat_data', {
            'session_id': session_id, 'initial_message': initial_ai_msg, 
            'hotel_name': hotel_name, 'timestamp': session_data_for_redis['start_time']
        }, room=client_sid)
        app_logger.info(f"[START-6] 'initial_chat_data' istemciye gönderildi.")

        broadcast_active_chats_update()
        app_logger.info(f"[START-7] Aktif sohbet listesi yayınlandı.")
        app_logger.info(f"--- [SUCCESS] handle_start_chat_from_widget ---")

    except Exception:
        # === LOGLAMA HATASI DÜZELTMESİ ===
        # Doğru kullanım app_logger.exception() şeklindedir.
        app_logger.exception("--- [CRITICAL ERROR] in handle_start_chat_from_widget ---")
        # ==================================
        emit('error_message', {'message': f"Sohbet başlatılırken sunucuda beklenmedik bir hata oluştu."}, room=client_sid)
    finally:
        if conn:
            conn.close()
        app_logger.info(f"--- [END] handle_start_chat_from_widget ---")


@socketio.on('resume_chat_from_widget')
def handle_resume_chat_from_widget_event(data):
    """
    Kullanıcının bir önceki oturumuna devam etme isteğini işler.

    İşlem Adımları:
    1.  İstekten 'session_id'yi alır.
    2.  Redis'ten bu oturumun verisini arar.
    3.  Eğer oturum bulunursa:
        a. Oturumun durumunu tekrar 'active' olarak günceller.
        b. Veritabanından sohbet geçmişini çeker.
        c. İstemciye 'chat_resumed_successfully' olayı ile geçmişi gönderir.
        d. 'broadcast_active_chats_update' ile oturumun Unity panelinde tekrar görünmesini sağlar.
    4.  Eğer oturum bulunamazsa (süresi dolmuşsa):
        a. İstemciye 'resume_failed_start_new' olayını göndererek yeni bir sohbet başlatmasını söyler.
    """
    client_sid = request.sid
    app_logger.info(f"--- [RESUME] handle_resume_chat_from_widget --- SID: {client_sid[:6]}")
    conn = None
    try:
        session_id = data.get('session_id')
        if not session_id:
            app_logger.warning("[RESUME-FAIL] İstekte Session ID gelmedi.")
            emit('resume_failed_start_new', {'reason': 'No session ID provided.'}, room=client_sid)
            return

        app_logger.info(f"[RESUME-1] Session ID alındı: {session_id[:8]}. Redis kontrol ediliyor...")
        redis_session_data = get_session_from_redis(session_id)

        if redis_session_data:
            app_logger.info(f"[RESUME-2] Redis'te oturum bulundu. Durumu 'active' olarak güncelleniyor...")
            
            # Oturumu tekrar 'active' yap ve Redis'e kaydet.
            redis_session_data['presence'] = 'active'
            save_session_to_redis(session_id, redis_session_data)
            
            # Yeni SID'yi bu aktif oturumla eşleştir.
            map_sid_to_session(client_sid, session_id)
            
            try:
                conn = get_db_connection()
                db_chat_history = [dict(row) for row in conn.execute(
                    "SELECT sender, message, timestamp FROM chat_history WHERE session_id = ? AND sender IN ('USER', 'AI') ORDER BY id ASC", 
                    (session_id,)
                )]
                app_logger.info(f"[RESUME-3] DB'den {len(db_chat_history)} mesaj çekildi. İstemciye gönderiliyor...")
            except sqlite3.Error:
                app_logger.exception(f"[RESUME-FAIL] Veritabanından geçmiş okunurken hata. Session: {session_id[:8]}")
                emit('error_message', {'message': f'Sohbet geçmişi yüklenemedi: DB Hatası.'}, room=client_sid)
                return

            payload = {
                'session_id': session_id,
                'hotel_name': redis_session_data.get('hotelName', 'Canlı Destek'),
                'chat_history': db_chat_history
            }
            emit('chat_resumed_successfully', payload, room=client_sid)
            app_logger.info(f"[RESUME-4] 'chat_resumed_successfully' istemciye gönderildi.")
            
            # Oturum tekrar aktif olduğu için, herkese güncel listeyi gönder.
            broadcast_active_chats_update()
            app_logger.info(f"[RESUME-5] Aktif sohbet listesi yayınlandı.")
            app_logger.info(f"--- [SUCCESS] handle_resume_chat_from_widget ---")
        else:
            # Oturum Redis'te bulunamadı.
            app_logger.warning(f"[RESUME-FAIL] Redis'te oturum bulunamadı: {session_id[:8]}")
            emit('resume_failed_start_new', {'reason': 'Session not found or expired in Redis.'}, room=client_sid)
            app_logger.info(f"--- [FAIL] handle_resume_chat_from_widget ---")

    except Exception:
        # Bu blok, Redis bağlantı hatası gibi daha genel sorunları yakalar.
        app_logger.exception("--- [CRITICAL ERROR] in handle_resume_chat_from_widget ---")
        emit('error_message', {'message': f'Oturum devam ettirilirken beklenmedik bir sunucu hatası oluştu.'}, room=client_sid)
    finally:
        if conn:
            conn.close()
        app_logger.info(f"--- [END] handle_resume_chat_from_widget ---")



@socketio.on('user_message_to_server')
def handle_user_message_from_widget_event(data):
    """
    Kullanıcıdan (web widget'tan) gelen bir mesajı işler. Bu fonksiyon, "stateless"
    mimarinin kalbidir. Her mesaj geldiğinde, sohbet oturumunu sıfırdan yeniden
    inşa eder (rehydrate) ve yapay zekaya gönderir.

    İşlem Adımları:
    1.  Gelen isteği doğrular (session_id, message var mı?).
    2.  Oturumun geçerliliğini tek doğruluk kaynağımız olan Redis'ten kontrol eder.
    3.  Eğer oturum geçerliyse, tüm sohbet geçmişini veritabanından çeker.
    4.  Çekilen veritabanı geçmişini, Gemini AI'ın anlayacağı bir formata dönüştürür.
        Bu, AI'ın önceki konuşmaları ve araç çağırma adımlarını hatırlamasını sağlar.
    5.  Gemini AI modelini, bu yeniden oluşturulmuş geçmiş ile başlatır.
    6.  Kullanıcının yeni mesajını veritabanına kaydeder.
    7.  AI yanıtını işlemesi için asenkron bir arka plan görevi (background task) başlatır.
        Bu, ana sunucu thread'inin kilitlenmesini önler.
    """

    # --- 1. Gelen İsteği ve Parametreleri Alma ---
    user_message = data.get('message')
    session_id = data.get('session_id')
    mode = data.get('mode', 'text')  # 'text' veya 'avatar'
    client_sid = request.sid

    # Gerekli parametreler eksikse, istemciye hata gönder ve işlemi sonlandır.
    if not all([user_message, session_id]):
        emit('error_message', {'message': "Mesaj veya session_id eksik."}, room=client_sid)
        app_logger.info(f"UYARI (user_message): Eksik parametreler. SID: {client_sid}")
        return

    # --- 2. Oturum Yeniden İnşa (Rehydration) ---
    
    # Adım 2a: Oturumun meta verisini Redis'ten al. Eğer yoksa, SQLite fallback dene.
    session_data_from_redis = get_session_from_redis(session_id)
    if not session_data_from_redis:
        # Redis yoksa, SQLite'tan session bilgisini çekmeye çalış
        try:
            from app.database import get_db_connection
            conn = get_db_connection()
            cursor = conn.cursor()

            # Son 1 saat içindeki chat history'den hotel_id_key'i bul
            cursor.execute("""
                SELECT hotel_id_key, COUNT(*) as msg_count
                FROM chat_history
                WHERE session_id = ?
                AND datetime(timestamp) > datetime('now', '-1 hour')
                GROUP BY hotel_id_key
                ORDER BY msg_count DESC
                LIMIT 1
            """, (session_id,))

            result = cursor.fetchone()
            conn.close()

            if result:
                hotel_id_key = result[0]
                app_logger.info(f"SQLITE_FALLBACK (user_message): Session {session_id[:8]} SQLite'tan bulundu. Hotel: {hotel_id_key}")

                # Hotel config'i de çek
                from app.database import get_db_connection
                conn2 = get_db_connection()
                cursor2 = conn2.cursor()
                cursor2.execute("SELECT hotel_config_json FROM hotels WHERE hotel_id_key = ?", (hotel_id_key,))
                hotel_config_result = cursor2.fetchone()
                conn2.close()

                hotel_config = {}
                if hotel_config_result:
                    import json
                    hotel_config = json.loads(hotel_config_result[0])

                # Fake session data oluştur
                session_data_from_redis = {
                    'hotel_id_key': hotel_id_key,
                    'session_id': session_id,
                    'hotel_config_key': hotel_config,  # Hotel config'i ekle
                    'fallback_mode': True
                }
            else:
                app_logger.info(f"REDIS_MISS (user_message): Oturum {session_id[:8]} Redis ve SQLite'ta bulunamadı. Sonlandırılıyor.")
                emit('start_new_chat_required', {'reason': 'Session expired while sending a message.'}, room=client_sid)
                return

        except Exception as e:
            app_logger.error(f"SQLite fallback error: {e}")
            emit('start_new_chat_required', {'reason': 'Session lookup failed.'}, room=client_sid)
            return

    # === HOTEL ID KEY'İ REDIS'TEN ALIYORUZ ===
    # Bu, save_chat_message_to_db'ye göndereceğimiz anahtar olacak.
    hotel_id_key = session_data_from_redis.get('hotel_config_key')
    if not hotel_id_key:
        app_logger.error(f"KRİTİK: Redis'teki oturumda 'hotel_config_key' eksik! Session: {session_id[:8]}")
        emit('error_message', {'message': "Oturum verisi bozuk, otel kimliği bulunamadı."}, room=client_sid)
        return    
    
    # Adım 2b: Oturum geçerliyse, veritabanından tüm sohbet geçmişini çek.
    # Bu, AI'ın hafızasını oluşturmak için kullanılacak.
    try:
        conn = get_db_connection()
        # AI'ın hafızası için tool_call'ları da içeren tüm detayları çekiyoruz.
        db_history_rows = conn.execute(
            "SELECT sender, message, tool_name, tool_args, tool_response FROM chat_history WHERE session_id = ? ORDER BY id ASC",
            (session_id,)
        ).fetchall()
        conn.close()
    except Exception as e_db:
        app_logger.info(f"KRİTİK HATA (DB Geçmiş Çekme): {e_db}")
        emit('error_message', {'message': "Sohbet geçmişi veritabanından alınamadı."}, room=client_sid)
        return

    # Adım 2c: Veritabanı satırlarını, Gemini AI'ın `start_chat(history=...)` metodunun
    # beklediği formata dönüştür. Bu en önemli adımlardan biridir.
    gemini_history = []
    for row in db_history_rows:
        sender_upper = row['sender'].upper()
        
        if sender_upper == 'USER':
            # Kullanıcı mesajlarını 'user' rolüyle ekle.
            gemini_history.append({'role': 'user', 'parts': [{'text': row['message']}]})
        
        elif sender_upper == 'AI':
            # AI mesajlarının iki kısmı olabilir: önce araç çağrısı, sonra metin yanıtı.
            
            # Eğer bu AI mesajı bir araç çağrısıyla sonuçlandıysa, bu adımı geçmişe ekle.
            if row['tool_name'] and row['tool_args']:
                try:
                    # Modelin fonksiyon çağrısı yaptığı adımı ekle.
                    gemini_history.append({'role': 'model', 'parts': [
                        {'function_call': {'name': row['tool_name'], 'args': json.loads(row['tool_args'])}}
                    ]})
                    
                    # Aracın çalışıp bir sonuç döndürdüğü adımı ekle.
                    if row['tool_response']:
                         gemini_history.append({'role': 'tool', 'parts': [
                            {'function_response': {'name': row['tool_name'], 'response': json.loads(row['tool_response'])}}
                        ]})
                except (json.JSONDecodeError, TypeError) as e_json:
                    # Geçmişteki bir JSON bozuksa, bunu logla ama programın çökmesini engelle.
                    app_logger.info(f"UYARI: Geçmişteki tool_call parse edilemedi. Session: {session_id}, Hata: {e_json}")
                    pass # Hatalı adımı atla ve devam et.

            # Son olarak, AI'ın kullanıcıya verdiği metin yanıtını ekle.
            # Bu, ya normal bir yanıttır ya da araç çağrısından sonraki özet yanıttır.
            if row['message']:
                gemini_history.append({'role': 'model', 'parts': [{'text': row['message']}]})

    # Adım 2d: Gerekli bilgilerle Gemini nesnesini SIFIRDAN oluştur ve hafızasını yükle.
    config_data = session_data_from_redis.get('config_data', {})
    system_instruction = generate_system_instruction_for_chat(config_data, source_type="Web Canlı Destek")
    
    try:
        model = genai.GenerativeModel(MODEL_NAME, system_instruction=system_instruction, tools=GEMINI_TOOLS_CONFIG)
        # Gemini sohbet nesnesini, veritabanından çektiğimiz ve formatladığımız geçmiş ile başlat.
        chat_session_obj = model.start_chat(history=gemini_history)
        app_logger.info(f"BİLGİ (Rehydration): Session {session_id[:8]} için Gemini nesnesi {len(gemini_history)} adımlık geçmişle yeniden oluşturuldu.")
    except Exception as e_gemini:
        app_logger.info(f"KRİTİK HATA (Gemini Rehydration): {e_gemini}")
        emit('error_message', {'message': "Yapay zeka motoru başlatılamadı."}, room=client_sid)
        return
        
    # --- 3. Yeni Mesajı İşleme ---
    
    # Kullanıcının az önce gönderdiği yeni mesajı veritabanına kaydet.
    # Bu, bir sonraki istekte geçmişin tam olmasını sağlar.
    save_chat_message_to_db(
        session_id=session_id, 
        sender="USER", 
        message=user_message,
        socketio_instance=socketio,
        source='web_widget',
        hotel_id_key=hotel_id_key # <<< EKSİK OLAN KRİTİK PARAMETRE
    )
    
    # --- 4. AI Yanıtı için Arka Plan Görevini Başlatma ---
    
    # AI'dan yanıt almak zaman alabileceği için (I/O işlemi), bunu ana thread'i
    # bloklamayan bir arka plan görevinde yapıyoruz.
    # Bu, sunucunun aynı anda başka isteklere de yanıt vermeye devam etmesini sağlar.
    socketio.start_background_task(
        target=process_gemini_response_for_websocket, 
        session_id=session_id, 
        user_message=user_message,
        config_data=config_data, 
        chat_session_obj=chat_session_obj,
        websocket_sid=client_sid,
        mode=mode,
        hotel_id_key=hotel_id_key 
    )

# Bu fonksiyon artık değişmeden kalabilir, çünkü zaten stateless çalışıyor.
# Sadece parametre olarak aldığı 'chat_session_obj'yi kullanır.
def process_gemini_response_for_websocket(session_id: str, user_message: str,
                                          config_data: dict, chat_session_obj, websocket_sid: str, mode: str, hotel_id_key: str):
    # ... Bu fonksiyonun içeriği öncekiyle aynı, değişiklik gerekmiyor ...
    socketio.emit('ai_typing_started', {'session_id': session_id}, room=websocket_sid)
    ai_full_text_response, special_data_for_widget = handle_gemini_response_and_tool_calls(
        chat_session_obj=chat_session_obj, user_message=user_message,
        session_config=config_data, session_id=session_id, source='web_widget', hotel_id_key=hotel_id_key
    )
    if not ai_full_text_response or not ai_full_text_response.strip():
        socketio.emit('ai_typing_finished', {'session_id': session_id}, room=websocket_sid)
        return
    message_group_id = str(uuid.uuid4())
    initial_text_payload = {
        'session_id': session_id,
        'message_id': message_group_id,
        'is_full_text': True,
        'message': ai_full_text_response,
        'timestamp': datetime.now().isoformat(),
        'message_data': special_data_for_widget
    }
    socketio.emit('ai_response_to_widget', initial_text_payload, room=websocket_sid)
    socketio.emit('ai_typing_finished', {'session_id': session_id}, room=websocket_sid)
    if mode == 'avatar':
        sentence_chunks = split_text_into_sentences(ai_full_text_response)
        for i, sentence in enumerate(sentence_chunks):
            if not sentence.strip(): continue
            tts_dict = synthesize_text_to_speech(sentence)
            if tts_dict and tts_dict.get("audio_content"):
                audio_b64 = base64.b64encode(tts_dict["audio_content"]).decode('utf-8')
                audio_chunk_payload = {
                    'session_id': session_id, 'message_id': message_group_id, 'chunk_index': i,
                    'audio_content_b64': audio_b64, 'word_timings': tts_dict.get("visemes", [])
                }
                socketio.emit('audio_chunk_to_widget', audio_chunk_payload, room=websocket_sid)
            socketio.sleep(0.1)
    socketio.emit('ai_stream_finished', {'session_id': session_id, 'message_id': message_group_id}, room=websocket_sid)

@socketio.on('admin_message')
def handle_admin_message(data):
    """
    Admin panelinden gelen mesajları işler ve chat'e ekler
    """
    session_id = data.get('session_id')
    message = data.get('message')
    sender = data.get('sender', 'ADMIN')

    if not session_id or not message:
        app_logger.warning("Admin message: session_id veya message eksik")
        return

    # Redis'ten session bilgilerini al
    session_data = get_session_from_redis(session_id)
    if not session_data:
        app_logger.warning(f"Admin message: Session {session_id[:8]} bulunamadı")
        return

    hotel_id_key = session_data.get('hotel_config_key')

    try:
        # Mesajı veritabanına kaydet
        save_chat_message_to_db(
            session_id=session_id,
            sender=sender,
            message=message,
            socketio_instance=socketio,
            source='admin_panel',
            hotel_id_key=hotel_id_key
        )

        # Mesajı web widget'a gönder
        socketio.emit('admin_message_to_widget', {
            'session_id': session_id,
            'message': message,
            'sender': sender,
            'timestamp': datetime.now().isoformat()
        }, broadcast=True)

        # Admin paneline onay gönder
        socketio.emit('admin_message_sent', {
            'session_id': session_id,
            'message': message,
            'timestamp': datetime.now().isoformat()
        }, room=request.sid)

        app_logger.info(f"Admin message sent to session {session_id[:8]}: {message[:50]}...")

    except Exception as e:
        app_logger.exception(f"Error handling admin message for session {session_id}")
        socketio.emit('admin_message_error', {
            'session_id': session_id,
            'error': str(e)
        }, room=request.sid)