import { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Avatar,
  Chip,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  TextField,
  IconButton,
  Badge,
  Paper,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  Collapse
} from '@mui/material';
import {
  Chat as ChatIcon,
  Person as PersonIcon,
  SmartToy as BotIcon,
  Send as SendIcon,
  Refresh as RefreshIcon,
  History as HistoryIcon,
  LiveTv as LiveIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  RadioButtonChecked as OnlineIcon,
  RadioButtonUnchecked as OfflineIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { useApi } from '../contexts/ApiContext';
import io from 'socket.io-client';

const ChatManagementNew = () => {
  const { getActiveChatSessions, getChatHistory, getHistoricalChatSessions, sendAdminMessage, currentHotelId } = useApi();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Live Chat States
  const [activeSessions, setActiveSessions] = useState([]);
  const [selectedActiveSession, setSelectedActiveSession] = useState(null);
  const [activeChatHistory, setActiveChatHistory] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [liveMessages, setLiveMessages] = useState([]);
  
  // Historical Chat States
  const [historicalSessions, setHistoricalSessions] = useState([]);
  const [selectedHistoricalSession, setSelectedHistoricalSession] = useState(null);
  const [historicalChatHistory, setHistoricalChatHistory] = useState([]);
  
  // UI States
  const [expandedSession, setExpandedSession] = useState(null);
  
  // Socket connection
  const [socket, setSocket] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const messagesEndRef = useRef(null);
  const liveMessagesEndRef = useRef(null);

  // Socket.IO bağlantısı
  useEffect(() => {
    const newSocket = io('https://resepsiyonapi.rehberim360.com', {
      transports: ['websocket', 'polling']
    });

    newSocket.on('connect', () => {
      console.log('Socket.IO bağlantısı kuruldu');
      setConnectionStatus('connected');
    });

    newSocket.on('disconnect', () => {
      console.log('Socket.IO bağlantısı kesildi');
      setConnectionStatus('disconnected');
    });

    // Canlı mesaj dinleme
    newSocket.on('live_chat_message', (data) => {
      console.log('Canlı mesaj alındı:', data);
      setLiveMessages(prev => [...prev, {
        ...data,
        timestamp: new Date(data.timestamp).toLocaleTimeString('tr-TR')
      }]);
      
      // Eğer seçili session'a ait mesajsa, chat history'yi güncelle
      if (selectedActiveSession && data.session_id === selectedActiveSession.sessionId) {
        setActiveChatHistory(prev => [...prev, {
          sender: data.sender,
          message: data.message,
          timestamp: data.timestamp
        }]);
      }
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, [selectedActiveSession]);

  // Aktif chat oturumlarını çek
  const fetchActiveSessions = useCallback(async () => {
    try {
      setLoading(true);
      const activeChatsData = await getActiveChatSessions(currentHotelId);
      
      const formattedSessions = activeChatsData.active_chats?.map(session => ({
        id: session.session_id,
        sessionId: session.session_id,
        guestName: session.guest_name || 'Anonim Misafir',
        hotelName: session.hotel_name,
        startTime: new Date(session.start_time).toLocaleTimeString('tr-TR'),
        startDate: new Date(session.start_time).toLocaleDateString('tr-TR'),
        status: 'active',
        source: session.source,
        lastActivity: new Date(session.start_time),
        messageCount: 0
      })) || [];

      setActiveSessions(formattedSessions);
      setError(null);
    } catch (err) {
      console.error('Aktif oturumlar yüklenirken hata:', err);
      setError('Aktif chat oturumları yüklenirken hata oluştu.');
    } finally {
      setLoading(false);
    }
  }, [getActiveChatSessions, currentHotelId]);

  // Geçmiş chat oturumlarını çek
  const fetchHistoricalSessions = useCallback(async () => {
    try {
      const historicalData = await getHistoricalChatSessions(currentHotelId);
      setHistoricalSessions(historicalData.historical_sessions || []);
    } catch (err) {
      console.error('Geçmiş oturumlar yüklenirken hata:', err);
      setError('Geçmiş chat oturumları yüklenirken hata oluştu.');
    }
  }, [getHistoricalChatSessions, currentHotelId]);

  // Chat geçmişini çek
  const fetchChatHistory = useCallback(async (sessionId, isHistorical = false) => {
    try {
      const historyData = await getChatHistory(sessionId);
      
      const formattedHistory = historyData.messages?.map(msg => ({
        sender: msg.sender,
        message: msg.message,
        timestamp: msg.timestamp
      })) || [];

      if (isHistorical) {
        setHistoricalChatHistory(formattedHistory);
      } else {
        setActiveChatHistory(formattedHistory);
      }
    } catch (err) {
      console.error('Chat geçmişi yüklenirken hata:', err);
      setError('Chat geçmişi yüklenirken hata oluştu.');
    }
  }, [getChatHistory]);

  // Mesaj gönder
  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedActiveSession) return;

    try {
      await sendAdminMessage(selectedActiveSession.sessionId, newMessage);

      // Local olarak mesajı ekle
      setActiveChatHistory(prev => [...prev, {
        sender: 'ADMIN',
        message: newMessage,
        timestamp: new Date().toISOString()
      }]);

      setNewMessage('');
    } catch (err) {
      console.error('Mesaj gönderme hatası:', err);
      setError('Mesaj gönderilemedi. Lütfen tekrar deneyin.');
    }
  };

  // Canlı dinlemeyi başlat/durdur
  const toggleLiveListening = () => {
    setIsListening(!isListening);
    if (!isListening) {
      setLiveMessages([]);
    }
  };

  // Session seçme
  const handleSessionSelect = (session, isHistorical = false) => {
    if (isHistorical) {
      setSelectedHistoricalSession(session);
      setExpandedSession(session.id);
      fetchChatHistory(session.sessionId, true);
    } else {
      setSelectedActiveSession(session);
      setExpandedSession(session.id);
      fetchChatHistory(session.sessionId, false);
    }
  };

  // Scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [activeChatHistory, historicalChatHistory]);

  useEffect(() => {
    liveMessagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [liveMessages]);

  // Initial data fetch
  useEffect(() => {
    fetchActiveSessions();
    fetchHistoricalSessions();
    
    // Her 30 saniyede bir aktif oturumları güncelle
    const interval = setInterval(fetchActiveSessions, 30000);
    return () => clearInterval(interval);
  }, [fetchActiveSessions, fetchHistoricalSessions]);

  const getStatusChip = (status) => {
    const statusMap = {
      'active': { label: 'Aktif', color: 'success', icon: <OnlineIcon /> },
      'completed': { label: 'Tamamlandı', color: 'info', icon: <CheckCircleIcon /> },
      'abandoned': { label: 'Terk Edildi', color: 'error', icon: <WarningIcon /> }
    };
    
    const statusInfo = statusMap[status] || { label: 'Bilinmiyor', color: 'default' };
    return (
      <Chip
        label={statusInfo.label}
        color={statusInfo.color}
        size="small"
        icon={statusInfo.icon}
      />
    );
  };

  const getOutcomeChip = (outcome) => {
    const outcomeMap = {
      'reservation_made': { label: 'Rezervasyon Yapıldı', color: 'success' },
      'information_provided': { label: 'Bilgi Verildi', color: 'info' },
      'abandoned': { label: 'Terk Edildi', color: 'error' }
    };
    
    const outcomeInfo = outcomeMap[outcome] || { label: 'Bilinmiyor', color: 'default' };
    return (
      <Chip
        label={outcomeInfo.label}
        color={outcomeInfo.color}
        size="small"
        variant="outlined"
      />
    );
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={4}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box display="flex" alignItems="center">
            <Avatar sx={{ bgcolor: 'primary.main', mr: 2, width: 56, height: 56 }}>
              <ChatIcon />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight="600">
                Chat Yönetimi
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Canlı chat oturumları ve geçmiş konuşmaları yönetin
              </Typography>
            </Box>
          </Box>
          
          <Box display="flex" gap={2} alignItems="center">
            {/* Bağlantı Durumu */}
            <Chip
              label={connectionStatus === 'connected' ? 'Bağlı' : 'Bağlantı Kesildi'}
              color={connectionStatus === 'connected' ? 'success' : 'error'}
              icon={connectionStatus === 'connected' ? <OnlineIcon /> : <OfflineIcon />}
            />
            
            {/* Canlı Dinleme */}
            <FormControlLabel
              control={
                <Switch
                  checked={isListening}
                  onChange={toggleLiveListening}
                  color="primary"
                />
              }
              label="Canlı Dinleme"
            />
            
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchActiveSessions}
            >
              Yenile
            </Button>
          </Box>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Tab Navigation */}
      <Paper sx={{ mb: 3 }}>
        <Tabs 
          value={tabValue} 
          onChange={(e, newValue) => setTabValue(newValue)}
          variant="fullWidth"
        >
          <Tab 
            label={
              <Box display="flex" alignItems="center" gap={1}>
                <LiveIcon />
                <span>Canlı Chat Oturumları</span>
                <Badge badgeContent={activeSessions.length} color="primary" />
              </Box>
            } 
          />
          <Tab 
            label={
              <Box display="flex" alignItems="center" gap={1}>
                <HistoryIcon />
                <span>Geçmiş Konuşmalar</span>
                <Badge badgeContent={historicalSessions.length} color="secondary" />
              </Box>
            } 
          />
          <Tab 
            label={
              <Box display="flex" alignItems="center" gap={1}>
                <NotificationsIcon />
                <span>Canlı İzleme</span>
                <Badge badgeContent={liveMessages.length} color="error" />
              </Box>
            } 
          />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      {tabValue === 0 && (
        <Box>
          <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
            🟢 Aktif Chat Oturumları ({activeSessions.length})
          </Typography>

          {activeSessions.length === 0 ? (
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 8 }}>
                <ChatIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" mb={1}>
                  Aktif Chat Oturumu Yok
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Şu anda aktif chat oturumu bulunmuyor
                </Typography>
              </CardContent>
            </Card>
          ) : (
            <Box>
              {activeSessions.map((session) => (
                <Card key={session.id} sx={{ mb: 2 }}>
                  <CardContent sx={{ p: 2 }}>
                    {/* Session Header */}
                    <Box
                      display="flex"
                      justifyContent="space-between"
                      alignItems="center"
                      sx={{ cursor: 'pointer' }}
                      onClick={() => handleSessionSelect(session, false)}
                    >
                      <Box display="flex" alignItems="center">
                        <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                          <PersonIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="h6" fontWeight="600">
                            {session.guestName}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Başlangıç: {session.startDate} {session.startTime} • Kaynak: {session.source}
                          </Typography>
                        </Box>
                      </Box>
                      <Box display="flex" alignItems="center" gap={1}>
                        {getStatusChip(session.status)}
                        <IconButton>
                          {expandedSession === session.id ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                        </IconButton>
                      </Box>
                    </Box>

                    {/* Chat Preview */}
                    <Collapse in={expandedSession === session.id}>
                      <Divider sx={{ my: 2 }} />

                      {/* Chat Messages */}
                      <Box sx={{ height: 400, overflow: 'auto', border: '1px solid', borderColor: 'divider', borderRadius: 1, p: 2, mb: 2 }}>
                        {activeChatHistory.map((message, index) => (
                          <Box
                            key={index}
                            display="flex"
                            justifyContent={message.sender === 'USER' ? 'flex-end' : 'flex-start'}
                            mb={2}
                          >
                            <Box
                              sx={{
                                maxWidth: '70%',
                                p: 2,
                                borderRadius: 2,
                                backgroundColor:
                                  message.sender === 'USER' ? 'primary.main' :
                                  message.sender === 'AI' ? 'grey.100' : 'warning.light',
                                color: message.sender === 'USER' ? 'white' : 'text.primary'
                              }}
                            >
                              <Box display="flex" alignItems="center" gap={1} mb={1}>
                                {message.sender === 'USER' ? <PersonIcon sx={{ fontSize: 16 }} /> :
                                 message.sender === 'AI' ? <BotIcon sx={{ fontSize: 16 }} /> :
                                 <SettingsIcon sx={{ fontSize: 16 }} />}
                                <Typography variant="caption" fontWeight="600">
                                  {message.sender === 'USER' ? session.guestName :
                                   message.sender === 'AI' ? 'AI Asistan' : 'Yönetici'}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {new Date(message.timestamp).toLocaleTimeString('tr-TR')}
                                </Typography>
                              </Box>
                              <Typography variant="body2">
                                {message.message}
                              </Typography>
                            </Box>
                          </Box>
                        ))}
                        <div ref={messagesEndRef} />
                      </Box>

                      {/* Message Input */}
                      <Box display="flex" gap={1}>
                        <TextField
                          fullWidth
                          placeholder="Misafirle konuşmak için mesaj yazın..."
                          value={newMessage}
                          onChange={(e) => setNewMessage(e.target.value)}
                          onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                          size="small"
                        />
                        <IconButton
                          color="primary"
                          onClick={handleSendMessage}
                          disabled={!newMessage.trim()}
                        >
                          <SendIcon />
                        </IconButton>
                      </Box>
                    </Collapse>
                  </CardContent>
                </Card>
              ))}
            </Box>
          )}
        </Box>
      )}

      {tabValue === 1 && (
        <Box>
          <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
            📚 Geçmiş Konuşmalar ({historicalSessions.length})
          </Typography>

          {historicalSessions.length === 0 ? (
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 8 }}>
                <HistoryIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" mb={1}>
                  Geçmiş Konuşma Yok
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Henüz tamamlanmış chat oturumu bulunmuyor
                </Typography>
              </CardContent>
            </Card>
          ) : (
            <Box>
              {historicalSessions.map((session) => (
                <Card key={session.id} sx={{ mb: 2 }}>
                  <CardContent sx={{ p: 2 }}>
                    {/* Session Header */}
                    <Box
                      display="flex"
                      justifyContent="space-between"
                      alignItems="center"
                      sx={{ cursor: 'pointer' }}
                      onClick={() => handleSessionSelect(session, true)}
                    >
                      <Box display="flex" alignItems="center">
                        <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                          <HistoryIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="h6" fontWeight="600">
                            {session.guestName}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {session.startDate} {session.startTime} - {session.endTime} • Süre: {session.duration} • {session.messageCount} mesaj
                          </Typography>
                        </Box>
                      </Box>
                      <Box display="flex" alignItems="center" gap={1}>
                        {getStatusChip(session.status)}
                        {getOutcomeChip(session.outcome)}
                        <IconButton>
                          {expandedSession === session.id ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                        </IconButton>
                      </Box>
                    </Box>

                    {/* Chat Preview */}
                    <Collapse in={expandedSession === session.id}>
                      <Divider sx={{ my: 2 }} />

                      {/* Chat Messages */}
                      <Box sx={{ height: 400, overflow: 'auto', border: '1px solid', borderColor: 'divider', borderRadius: 1, p: 2 }}>
                        {historicalChatHistory.map((message, index) => (
                          <Box
                            key={index}
                            display="flex"
                            justifyContent={message.sender === 'USER' ? 'flex-end' : 'flex-start'}
                            mb={2}
                          >
                            <Box
                              sx={{
                                maxWidth: '70%',
                                p: 2,
                                borderRadius: 2,
                                backgroundColor:
                                  message.sender === 'USER' ? 'secondary.main' :
                                  message.sender === 'AI' ? 'grey.100' : 'warning.light',
                                color: message.sender === 'USER' ? 'white' : 'text.primary'
                              }}
                            >
                              <Box display="flex" alignItems="center" gap={1} mb={1}>
                                {message.sender === 'USER' ? <PersonIcon sx={{ fontSize: 16 }} /> :
                                 message.sender === 'AI' ? <BotIcon sx={{ fontSize: 16 }} /> :
                                 <SettingsIcon sx={{ fontSize: 16 }} />}
                                <Typography variant="caption" fontWeight="600">
                                  {message.sender === 'USER' ? session.guestName :
                                   message.sender === 'AI' ? 'AI Asistan' : 'Yönetici'}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {new Date(message.timestamp).toLocaleTimeString('tr-TR')}
                                </Typography>
                              </Box>
                              <Typography variant="body2">
                                {message.message}
                              </Typography>
                            </Box>
                          </Box>
                        ))}
                      </Box>
                    </Collapse>
                  </CardContent>
                </Card>
              ))}
            </Box>
          )}
        </Box>
      )}

      {tabValue === 2 && (
        <Box>
          <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
            🔴 Canlı Mesaj İzleme
          </Typography>

          <Card>
            <CardContent sx={{ p: 2 }}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6" fontWeight="600">
                  Canlı Mesaj Akışı
                </Typography>
                <Box display="flex" alignItems="center" gap={2}>
                  <Chip
                    label={isListening ? 'Dinleniyor' : 'Durduruldu'}
                    color={isListening ? 'success' : 'default'}
                    icon={isListening ? <OnlineIcon /> : <OfflineIcon />}
                  />
                  <Button
                    variant={isListening ? 'contained' : 'outlined'}
                    color={isListening ? 'error' : 'success'}
                    startIcon={isListening ? <OfflineIcon /> : <LiveIcon />}
                    onClick={toggleLiveListening}
                  >
                    {isListening ? 'Dinlemeyi Durdur' : 'Canlı Dinlemeyi Başlat'}
                  </Button>
                </Box>
              </Box>

              {isListening && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  Canlı dinleme aktif. Tüm chat mesajları gerçek zamanlı olarak burada görünecek.
                </Alert>
              )}

              <Box sx={{ height: 500, overflow: 'auto', border: '1px solid', borderColor: 'divider', borderRadius: 1, p: 2 }}>
                {liveMessages.map((message, index) => (
                  <Box
                    key={index}
                    sx={{
                      p: 2,
                      mb: 1,
                      borderRadius: 1,
                      backgroundColor: 'grey.50',
                      borderLeft: '4px solid',
                      borderLeftColor:
                        message.sender === 'USER' ? 'primary.main' :
                        message.sender === 'AI' ? 'success.main' : 'warning.main'
                    }}
                  >
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Box display="flex" alignItems="center" gap={1}>
                        {message.sender === 'USER' ? <PersonIcon sx={{ fontSize: 16 }} /> :
                         message.sender === 'AI' ? <BotIcon sx={{ fontSize: 16 }} /> :
                         <SettingsIcon sx={{ fontSize: 16 }} />}
                        <Typography variant="subtitle2" fontWeight="600">
                          {message.sender === 'USER' ? 'Misafir' :
                           message.sender === 'AI' ? 'AI Asistan' : 'Yönetici'}
                        </Typography>
                        <Chip
                          label={message.session_id?.substring(0, 8)}
                          size="small"
                          variant="outlined"
                        />
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        {message.timestamp}
                      </Typography>
                    </Box>
                    <Typography variant="body2">
                      {message.message}
                    </Typography>
                  </Box>
                ))}

                {liveMessages.length === 0 && (
                  <Box textAlign="center" py={8}>
                    <NotificationsIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                    <Typography variant="body2" color="text.secondary">
                      {isListening ? 'Canlı mesajlar bekleniyor...' : 'Canlı dinleme kapalı'}
                    </Typography>
                  </Box>
                )}

                <div ref={liveMessagesEndRef} />
              </Box>
            </CardContent>
          </Card>
        </Box>
      )}
    </Box>
  );
};

export default ChatManagementNew;
