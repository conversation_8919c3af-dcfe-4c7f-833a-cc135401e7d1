# app/facebook_service.py (Tam ve Eksiksiz Kod - Çoklu Otel Destekli)

import os
import facebook # pip install facebook-sdk
from datetime import datetime

# Proje içi importlar
from app.database import get_db_connection
from app.utils import decrypt_data # <PERSON>ifrelenmiş token'ı çözmek için

def get_graph_api_for_hotel(hotel_id_key: str):
    """
    <PERSON><PERSON><PERSON> bir otelin veritabanında kayıtlı, şifresi çözülmüş
    Facebook Sayfa Erişim Jetonu'nu kullanarak bir Graph API nesnesi oluşturur.
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT facebook_page_token_encrypted, facebook_page_id FROM hotels WHERE hotel_id_key = ? AND is_facebook_connected = 1", (hotel_id_key,))
    hotel_data = cursor.fetchone()
    conn.close()

    if not hotel_data or not hotel_data['facebook_page_token_encrypted']:
        raise Exception(f"Otel '{hotel_id_key}' için Facebook bağlantısı bulunamadı veya jeton eksik.")

    page_access_token = decrypt_data(hotel_data['facebook_page_token_encrypted'])
    page_id = hotel_data['facebook_page_id']
    
    graph = facebook.GraphAPI(access_token=page_access_token, version="v19.0")
    
    # page_id'yi de döndürelim, çünkü diğer fonksiyonların ihtiyacı olacak
    return graph, page_id


def sync_facebook_comments(hotel_id_key: str):
    """
    Belirli bir otelin Facebook sayfasındaki son gönderilere gelen yorumları çeker.
    """
    print(f"INFO: '{hotel_id_key}' oteli için Facebook yorumları senkronize ediliyor...")
    try:
        graph, page_id = get_graph_api_for_hotel(hotel_id_key)
        
        posts = graph.get_connections(
            id=page_id, 
            connection_name='posts',
            fields='id,comments{id,message,created_time,from}'
        )

        conn = get_db_connection()
        cursor = conn.cursor()
        new_comments_count = 0
        
        for post in posts.get('data', []):
            post_id = post.get('id')
            for comment in post.get('comments', {}).get('data', []):
                comment_id = comment.get('id')
                
                cursor.execute("SELECT id FROM facebook_comments WHERE comment_id = ?", (comment_id,))
                if cursor.fetchone(): continue

                cursor.execute("""
                    INSERT INTO facebook_comments (comment_id, post_id, sender_id, sender_name, message, created_time, hotel_id_key, fetched_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    comment_id, post_id, comment.get('from', {}).get('id'),
                    comment.get('from', {}).get('name'), comment.get('message'),
                    comment.get('created_time'), hotel_id_key, datetime.now().isoformat()
                ))
                new_comments_count += 1
        
        conn.commit()
        conn.close()
        
        message = f"Facebook yorum senkronizasyonu tamamlandı. Yeni yorum: {new_comments_count}."
        print(f"SUCCESS: {message}")
        return {"success": True, "message": message, "new_comments": new_comments_count}

    except facebook.GraphAPIError as e:
        return {"success": False, "error": f"Graph API Hatası: {e.message}"}
    except Exception as e:
        return {"success": False, "error": str(e)}


def post_reply_to_facebook_comment(hotel_id_key: str, comment_id: str, reply_text: str):
    """Belirli bir Facebook yorumuna yanıt gönderir."""
    print(f"INFO: Facebook yorumuna yanıt gönderiliyor. Yorum ID: {comment_id}")
    try:
        graph, _ = get_graph_api_for_hotel(hotel_id_key)
        graph.put_comment(object_id=comment_id, message=reply_text)
        
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("UPDATE facebook_comments SET is_replied = 1, reply_message = ?, replied_at = ? WHERE comment_id = ?",
                       (reply_text, datetime.now().isoformat(), comment_id))
        conn.commit()
        conn.close()
        
        return {"success": True, "message": "Yorum başarıyla yanıtlandı."}

    except facebook.GraphAPIError as e:
        return {"success": False, "error": f"Graph API Hatası: {e.message}"}
    except Exception as e:
        return {"success": False, "error": str(e)}

# Not: Messenger mesajlarını çekme ve yanıtlama fonksiyonları da benzer bir yapıda buraya eklenecektir.
# Şimdilik yorum yönetimiyle başlıyoruz.