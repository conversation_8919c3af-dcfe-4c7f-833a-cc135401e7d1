import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Chip,
  LinearProgress,
  Button,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  SmartToy as AIIcon,
  Chat as ChatIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Schedule as ScheduleIcon,
  Psychology as PsychologyIcon,
  Lightbulb as LightbulbIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon,
  EventNote as ReservationIcon,
  Speed as SpeedIcon,
  Analytics as AnalyticsIcon,
  SentimentSatisfied as HappyIcon,
  SentimentNeutral as NeutralIcon,
  SentimentDissatisfied as SadIcon
} from '@mui/icons-material';
import { useApi } from '../contexts/ApiContext';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const DashboardAI = () => {
  const { getAIChatAnalytics, currentHotelId } = useApi();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);

  const fetchAnalytics = useCallback(async () => {
    try {
      setRefreshing(true);
      const data = await getAIChatAnalytics(currentHotelId);
      setAnalytics(data);
      setError(null);
    } catch (err) {
      console.error('AI Analytics fetch error:', err);
      setError('AI analitik verileri yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [getAIChatAnalytics, currentHotelId]);

  useEffect(() => {
    fetchAnalytics();
    
    // Her 2 dakikada bir otomatik güncelle
    const interval = setInterval(fetchAnalytics, 2 * 60 * 1000);
    return () => clearInterval(interval);
  }, [fetchAnalytics]);

  const getSentimentIcon = (type) => {
    switch (type) {
      case 'positive': return <HappyIcon sx={{ color: '#4caf50' }} />;
      case 'neutral': return <NeutralIcon sx={{ color: '#ff9800' }} />;
      case 'negative': return <SadIcon sx={{ color: '#f44336' }} />;
      default: return <NeutralIcon />;
    }
  };

  const getInsightIcon = (type) => {
    switch (type) {
      case 'opportunity': return <LightbulbIcon sx={{ color: '#4caf50' }} />;
      case 'improvement': return <WarningIcon sx={{ color: '#ff9800' }} />;
      case 'success': return <CheckCircleIcon sx={{ color: '#4caf50' }} />;
      default: return <InfoIcon sx={{ color: '#2196f3' }} />;
    }
  };

  const getInsightColor = (priority) => {
    switch (priority) {
      case 'high': return '#f44336';
      case 'medium': return '#ff9800';
      case 'low': return '#4caf50';
      default: return '#2196f3';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={4}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box display="flex" alignItems="center">
            <Avatar sx={{ bgcolor: 'primary.main', mr: 2, width: 56, height: 56 }}>
              <AIIcon />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight="600">
                AI Resepsiyon Analitikleri
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Yapay zeka destekli müşteri hizmetleri performansı
              </Typography>
            </Box>
          </Box>
          
          <Button
            variant="outlined"
            startIcon={refreshing ? <CircularProgress size={20} /> : <RefreshIcon />}
            onClick={fetchAnalytics}
            disabled={refreshing}
          >
            {refreshing ? 'Güncelleniyor...' : 'Yenile'}
          </Button>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Ana Metrikler */}
      <Grid container spacing={3} mb={4}>
        {/* Aktif Sohbetler */}
        <Grid item xs={12} md={3}>
          <Card 
            sx={{ 
              background: 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)',
              color: 'white',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 32px rgba(33, 150, 243, 0.4)'
              }
            }}
            onClick={() => navigate('/chat')}
          >
            <CardContent sx={{ p: 3, textAlign: 'center' }}>
              <ChatIcon sx={{ fontSize: 40, mb: 2 }} />
              <Typography variant="h3" fontWeight="700" sx={{ mb: 1 }}>
                {analytics?.active_chat_sessions || 0}
              </Typography>
              <Typography variant="h6" fontWeight="600">
                Aktif Sohbetler
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                Şu anda canlı
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* AI Başarı Oranı */}
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ p: 3, textAlign: 'center' }}>
              <AIIcon sx={{ fontSize: 40, mb: 2, color: 'success.main' }} />
              <Typography variant="h3" fontWeight="700" color="success.main" sx={{ mb: 1 }}>
                %{analytics?.ai_success_rate || 0}
              </Typography>
              <Typography variant="h6" fontWeight="600">
                AI Başarı Oranı
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Başarılı yanıtlar
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Dönüşüm Oranı */}
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ p: 3, textAlign: 'center' }}>
              <ReservationIcon sx={{ fontSize: 40, mb: 2, color: 'warning.main' }} />
              <Typography variant="h3" fontWeight="700" color="warning.main" sx={{ mb: 1 }}>
                %{analytics?.ai_conversion_rate || 0}
              </Typography>
              <Typography variant="h6" fontWeight="600">
                Dönüşüm Oranı
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Chat → Rezervasyon
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Yanıt Süresi */}
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ p: 3, textAlign: 'center' }}>
              <SpeedIcon sx={{ fontSize: 40, mb: 2, color: 'info.main' }} />
              <Typography variant="h3" fontWeight="700" color="info.main" sx={{ mb: 1 }}>
                {analytics?.avg_response_time_seconds || 0}s
              </Typography>
              <Typography variant="h6" fontWeight="600">
                Ortalama Yanıt
              </Typography>
              <Typography variant="body2" color="text.secondary">
                AI yanıt süresi
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* İkinci Seviye Metrikler */}
      <Grid container spacing={3} mb={4}>
        {/* Günlük Sohbet Sayıları */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                📊 Sohbet İstatistikleri
              </Typography>
              
              <Box mb={2}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2">Bugün</Typography>
                  <Typography variant="h6" fontWeight="600" color="primary">
                    {analytics?.total_chat_sessions_today || 0}
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={(analytics?.total_chat_sessions_today || 0) / 50 * 100} 
                  sx={{ height: 6, borderRadius: 3 }}
                />
              </Box>

              <Box mb={2}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2">Bu Hafta</Typography>
                  <Typography variant="h6" fontWeight="600" color="success.main">
                    {analytics?.total_chat_sessions_week || 0}
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={(analytics?.total_chat_sessions_week || 0) / 300 * 100} 
                  color="success"
                  sx={{ height: 6, borderRadius: 3 }}
                />
              </Box>

              <Box>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2">Bu Ay</Typography>
                  <Typography variant="h6" fontWeight="600" color="warning.main">
                    {analytics?.total_chat_sessions_month || 0}
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={(analytics?.total_chat_sessions_month || 0) / 1000 * 100} 
                  color="warning"
                  sx={{ height: 6, borderRadius: 3 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Müşteri Memnuniyeti */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                😊 Müşteri Memnuniyeti
              </Typography>
              
              <Box textAlign="center" mb={3}>
                <Typography variant="h2" fontWeight="700" color="success.main">
                  %{analytics?.customer_sentiment?.positive || 0}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Pozitif Geri Bildirim
                </Typography>
              </Box>

              <Box>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Box display="flex" alignItems="center">
                    <HappyIcon sx={{ mr: 1, color: '#4caf50' }} />
                    <Typography variant="body2">Pozitif</Typography>
                  </Box>
                  <Typography variant="body2" fontWeight="600">
                    %{analytics?.customer_sentiment?.positive || 0}
                  </Typography>
                </Box>
                
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Box display="flex" alignItems="center">
                    <NeutralIcon sx={{ mr: 1, color: '#ff9800' }} />
                    <Typography variant="body2">Nötr</Typography>
                  </Box>
                  <Typography variant="body2" fontWeight="600">
                    %{analytics?.customer_sentiment?.neutral || 0}
                  </Typography>
                </Box>
                
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box display="flex" alignItems="center">
                    <SadIcon sx={{ mr: 1, color: '#f44336' }} />
                    <Typography variant="body2">Negatif</Typography>
                  </Box>
                  <Typography variant="body2" fontWeight="600">
                    %{analytics?.customer_sentiment?.negative || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Rezervasyon Metrikleri */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                🏨 Rezervasyon Metrikleri
              </Typography>
              
              <Box mb={2}>
                <Typography variant="body2" color="text.secondary" mb={1}>
                  AI'dan Gelen Rezervasyonlar
                </Typography>
                <Typography variant="h4" fontWeight="700" color="primary">
                  {analytics?.total_reservations_from_ai || 0}
                </Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box mb={2}>
                <Typography variant="body2" color="text.secondary" mb={1}>
                  Onay Bekleyen
                </Typography>
                <Typography variant="h5" fontWeight="600" color="warning.main">
                  {analytics?.pending_reservations || 0}
                </Typography>
              </Box>

              <Box>
                <Typography variant="body2" color="text.secondary" mb={1}>
                  Bugün Onaylanan
                </Typography>
                <Typography variant="h5" fontWeight="600" color="success.main">
                  {analytics?.confirmed_reservations_today || 0}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Alt Bölüm - Detaylı Analizler */}
      <Grid container spacing={3}>
        {/* En Çok Sorulan Konular */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                💭 En Çok Sorulan Konular
              </Typography>

              <List>
                {analytics?.most_asked_topics?.map((topic, index) => (
                  <ListItem key={index} sx={{ px: 0 }}>
                    <ListItemIcon>
                      <Chip
                        label={index + 1}
                        size="small"
                        color={index < 3 ? 'primary' : 'default'}
                        sx={{ minWidth: 32 }}
                      />
                    </ListItemIcon>
                    <ListItemText
                      primary={topic.topic}
                      secondary={`${topic.count} soru • %${topic.percentage} oranında`}
                    />
                    <Box>
                      <Typography variant="h6" fontWeight="600" color="primary">
                        {topic.count}
                      </Typography>
                    </Box>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* AI Öngörüleri */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                🔮 AI Öngörüleri ve Öneriler
              </Typography>

              <List>
                {analytics?.ai_insights?.map((insight, index) => (
                  <ListItem key={index} sx={{ px: 0, alignItems: 'flex-start' }}>
                    <ListItemIcon sx={{ mt: 1 }}>
                      {getInsightIcon(insight.type)}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography variant="subtitle2" fontWeight="600">
                            {insight.title}
                          </Typography>
                          <Chip
                            label={insight.priority.toUpperCase()}
                            size="small"
                            sx={{
                              backgroundColor: getInsightColor(insight.priority),
                              color: 'white',
                              fontSize: '0.7rem'
                            }}
                          />
                        </Box>
                      }
                      secondary={insight.description}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Kayıp Fırsatlar */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                ⚠️ Kayıp Fırsatlar
              </Typography>

              <Box mb={3}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="body2">Son 1 Saatte</Typography>
                  <Typography variant="h5" fontWeight="600" color="error.main">
                    {analytics?.abandoned_conversations?.last_hour || 0}
                  </Typography>
                </Box>

                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="body2">Bugün Toplam</Typography>
                  <Typography variant="h5" fontWeight="600" color="warning.main">
                    {analytics?.abandoned_conversations?.today || 0}
                  </Typography>
                </Box>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle2" fontWeight="600" sx={{ mb: 2 }}>
                Yaygın Çıkış Noktaları
              </Typography>

              {analytics?.abandoned_conversations?.common_exit_points?.map((point, index) => (
                <Box key={index} display="flex" justifyContent="space-between" alignItems="center" py={1}>
                  <Typography variant="body2">{point.point}</Typography>
                  <Chip
                    label={point.count}
                    size="small"
                    color="error"
                    variant="outlined"
                  />
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* Performans Özeti */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                📈 Performans Özeti
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box textAlign="center" p={2} sx={{ backgroundColor: 'rgba(76, 175, 80, 0.1)', borderRadius: 2 }}>
                    <Typography variant="h4" fontWeight="700" color="success.main">
                      %{analytics?.conversation_completion_rate || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Tamamlanan Konuşma
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={6}>
                  <Box textAlign="center" p={2} sx={{ backgroundColor: 'rgba(33, 150, 243, 0.1)', borderRadius: 2 }}>
                    <Typography variant="h4" fontWeight="700" color="primary">
                      %{analytics?.ai_conversion_rate || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      AI Dönüşüm Oranı
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12}>
                  <Box textAlign="center" p={2} sx={{ backgroundColor: 'rgba(255, 152, 0, 0.1)', borderRadius: 2 }}>
                    <Typography variant="h5" fontWeight="700" color="warning.main">
                      {analytics?.avg_response_time_seconds || 0} saniye
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Ortalama AI Yanıt Süresi
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={Math.max(0, 100 - (analytics?.avg_response_time_seconds || 0) * 10)}
                      color="warning"
                      sx={{ mt: 1, height: 4, borderRadius: 2 }}
                    />
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardAI;
