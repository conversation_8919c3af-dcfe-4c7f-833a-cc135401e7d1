# app/routes/http_routes.py (<PERSON> ve <PERSON> - <PERSON>üm Google Endpoint'<PERSON><PERSON>)

import base64
import os
import uuid
import time
import json
import sqlite3
import traceback # <PERSON>a dökümü için
from flask import request, jsonify, Response, redirect, session, url_for,send_file, render_template
from queue import Queue, Empty
from itsdangerous import URLSafeTimedSerializer, SignatureExpired, BadTimeSignature
import google.generativeai as genai
from datetime import datetime, timezone
import facebook 
from io import BytesIO

# Ana uygulama nesneleri ve yardımcıları
from app import app, socketio, active_chat_sessions, otel_configurations, otel_configs_lock, redis_client, session_lock
from app.config import (
    GEMINI_API_KEY, MODEL_NAME, CONFIG_DIR, SESSION_TIMEOUT_SECONDS,
    STATUS_AI_PENDING_HUMAN, STATUS_HUMAN_CONFIRMED, STATUS_CANCELLED_GUEST,
    STATUS_CANCELLED_HOTEL, STATUS_COMPLETED, STATUS_NO_SHOW, FLASK_SECRET_KEY,GOOGLE_CLIENT_ID,GOOGLE_CLIENT_SECRET,
    FACEBOOK_APP_ID, FACEBOOK_APP_SECRET, FACEBOOK_REDIRECT_URI, MODEL_NAME,
    SESSION_REDIS_PREFIX
)
from app.utils import save_session_to_redis, get_session_from_redis
from app import app_logger # app/__init__.py'de tanımladığımız logger
from app.database import get_db_connection, save_chat_message_to_db
from app.utils import broadcast_active_chats_update, encrypt_data
from app.ai.prompts import generate_system_instruction_for_chat
from app.ai.tools import GEMINI_TOOLS_CONFIG
from app.ai.core import handle_gemini_response_and_tool_calls
from datetime import datetime, timedelta
from google.oauth2.credentials import Credentials
from app.facebook_service import *
from app.stt_tts_service import transcribe_audio_data, synthesize_text_to_speech
from app.places_service import find_place_by_text_query
# Analiz ve Google servisleri
from app.analysis import run_weekly_analysis
from app.google_service import (
    get_google_auth_flow, sync_reviews_from_google, 
    post_reply_to_google, generate_ai_response_draft,
    list_accounts_and_locations
)


# --- Güvenli State Serializer ---
# FLASK_SECRET_KEY'i kullanarak güvenli bir "imzalayıcı" oluştur.
# Bu, state parametresinin bizim tarafımızdan oluşturulduğunu ve değiştirilmediğini garanti eder.
serializer = URLSafeTimedSerializer(FLASK_SECRET_KEY)

@app.route('/')
def index():
    # Bu endpoint, hem API'nin çalıştığını doğrular hem de
    # web widget'ı test etmek için bir sayfa sunar.
    return render_template('index.html')

# /// Otomatik Konum Bulma İçin ///
@app.route('/find_place_details', methods=['POST'])
def find_place_details_endpoint():
    data = request.get_json()
    query = data.get('query')
    if not query:
        return jsonify({"error": "Arama sorgusu ('query') eksik."}), 400
    
    result = find_place_by_text_query(query)
    return jsonify(result)


# --- Otel ve Entegrasyon Yönetimi Endpoint'leri ---

@app.route('/voice_command', methods=['POST'])
def voice_command_endpoint():
    """
    Unity gibi sesli komut istemcilerinden gelen ses verisini işler.
    Bu endpoint, artık tamamen "stateless" mimariye uygun çalışır.

    İşlem Adımları:
    1.  Gelen isteği doğrular, ses verisini ve diğer parametreleri alır.
    2.  Sesi metne çevirir (Speech-to-Text).
    3.  Eğer istekte bir 'session_id' varsa, oturumu Redis ve veritabanından
        yeniden inşa eder (rehydrate).
    4.  Eğer 'session_id' yoksa (ilk komut), yeni bir oturum başlatır ve
        bunu Redis'e kaydeder.
    5.  Yeniden inşa edilen veya yeni oluşturulan Gemini AI nesnesi ile
        kullanıcının mesajını işler.
    6.  AI'dan gelen metin yanıtını sese (Text-to-Speech) ve dudak senkronizasyon
        verisine (visemes) çevirir.
    7.  Tüm bu verileri istemciye JSON formatında döndürür.
    """
    app_logger.info("--- [VOICE] voice_command_endpoint tetiklendi ---")
    
    # --- 1. Gelen İsteği Doğrulama ve Verileri Alma ---
    if 'audio_data' not in request.files or 'hotel_id_key' not in request.form or 'sample_rate' not in request.form:
        app_logger.error("[VOICE-FAIL] Eksik parametreler.")
        return jsonify({ "success": False, "error": "Eksik parametreler: audio_data, hotel_id_key ve sample_rate zorunludur."}), 400

    hotel_id_key = request.form.get('hotel_id_key')
    session_id = request.form.get('session_id')  # Boş olabilir
    request_id = request.form.get('request_id')
    
    try:
        sample_rate = int(request.form.get('sample_rate'))
        audio_bytes = request.files['audio_data'].read()
    except (ValueError, TypeError) as e:
        app_logger.error(f"[VOICE-FAIL] Geçersiz istek verisi: {e}")
        return jsonify({"success": False, "error": "Geçersiz sample_rate veya bozuk ses verisi."}), 400

    # --- 2. Sesi Metne Çevirme (Speech-to-Text) ---
    user_message_text = transcribe_audio_data(audio_bytes, sample_rate)
    
    response_data = {"success": True, "audio_content_b64": None, "visemes": [], "session_id": session_id, "error": None}
    if not user_message_text:
        app_logger.warning("[VOICE] Konuşma algılanamadı veya STT başarısız.")
        error_speech_dict = synthesize_text_to_speech("Üzgünüm, sizi anlayamadım.")
        if error_speech_dict and error_speech_dict.get("audio_content"):
            response_data["audio_content_b64"] = base64.b64encode(error_speech_dict["audio_content"]).decode('utf-8')
            response_data["visemes"] = error_speech_dict.get("visemes", [])
        else:
            response_data.update({"success": False, "error": "Konuşma algılanamadı ve hata sesi üretilemedi."})
        return jsonify(response_data)

    # --- 3. Gemini Sohbet Oturumunu Yönetme (Stateless Yaklaşım) ---
    chat_session_obj = None
    config_data = {}
    
    # Veritabanı bağlantısını aç
    conn = get_db_connection()

    if not session_id:
        # --- YENİ OTURUM BAŞLATMA ---
        app_logger.info(f"[VOICE] Yeni sesli oturum başlatılıyor. Otel ID: {hotel_id_key}")
        
        hotel_row = conn.execute("SELECT hotel_config_json FROM hotels WHERE hotel_id_key = ?", (hotel_id_key,)).fetchone()
        if not hotel_row or not hotel_row['hotel_config_json']:
            conn.close()
            app_logger.error(f"[VOICE-FAIL] Otel yapılandırması bulunamadı: {hotel_id_key}")
            response_data.update({"success": False, "error": f"Sistemde {hotel_id_key} için bir otel yapılandırması bulunamadı."})
            return jsonify(response_data), 404
        
        config_data = json.loads(hotel_row['hotel_config_json'])
        system_instruction = generate_system_instruction_for_chat(config_data, source_type="Sesli Asistan")
        
        session_id = str(uuid.uuid4()) # Yeni session ID oluştur
        model = genai.GenerativeModel(MODEL_NAME, system_instruction=system_instruction, tools=GEMINI_TOOLS_CONFIG)
        chat_session_obj = model.start_chat(history=[]) # Boş geçmişle başlat
        
        # Yeni oturumu Redis'e kaydet
        session_data_for_redis = {'config_data': config_data, 'source': 'voice_command', 'hotel_config_key': hotel_id_key, 'start_time': datetime.now().isoformat(), 'last_activity_time': time.time()}
        save_session_to_redis(session_id, session_data_for_redis)
        
        save_chat_message_to_db(session_id, "SYSTEM", "Yeni sesli komut oturumu başlatıldı.", socketio_instance=socketio, source='voice_command')

    else:
        # --- MEVCUT OTURUMU YENİDEN İNŞA ETME (REHYDRATION) ---
        app_logger.info(f"[VOICE] Mevcut sesli oturum devam ediyor: {session_id[:8]}")
        
        session_data_from_redis = get_session_from_redis(session_id)
        if not session_data_from_redis:
            conn.close()
            app_logger.error(f"[VOICE-FAIL] Devam eden oturum Redis'te bulunamadı: {session_id[:8]}")
            response_data.update({"success": False, "error": "Oturum süresi dolmuş veya geçersiz."})
            return jsonify(response_data), 404

        config_data = session_data_from_redis.get('config_data', {})
        # === hotel_id_key'i Redis'ten alıyoruz ===
        hotel_id_key = session_data_from_redis.get('hotel_config_key')

        if not hotel_id_key:
             app_logger.error(f"KRİTİK: Redis'teki sesli oturumda 'hotel_config_key' eksik! Session: {session_id[:8]}")
        
        # `socketio_handlers.py`'deki aynı geçmiş oluşturma mantığını kullan.
        db_history_rows = conn.execute("SELECT sender, message, tool_name, tool_args, tool_response FROM chat_history WHERE session_id = ? ORDER BY id ASC", (session_id,)).fetchall()
        gemini_history = []
        # (Buraya tool_call'ları da işleyen tam geçmiş oluşturma döngüsü eklenebilir, şimdilik basit tutuyoruz)
        for row in db_history_rows:
            if row['sender'].upper() == 'USER': gemini_history.append({'role': 'user', 'parts': [{'text': row['message']}]})
            elif row['sender'].upper() == 'AI': gemini_history.append({'role': 'model', 'parts': [{'text': row['message']}]})
        
        system_instruction = generate_system_instruction_for_chat(config_data, source_type="Sesli Asistan")
        model = genai.GenerativeModel(MODEL_NAME, system_instruction=system_instruction, tools=GEMINI_TOOLS_CONFIG)
        chat_session_obj = model.start_chat(history=gemini_history)

    # Veritabanı bağlantısını kapat
    conn.close()
    
    # Kullanıcının mesajını veritabanına kaydet
    save_chat_message_to_db(session_id, "USER", user_message_text, socketio_instance=socketio, source='voice_command', hotel_id_key=hotel_id_key)

    # --- 4. Gemini'den Yanıt Alma ---
    ai_text_response, _ = handle_gemini_response_and_tool_calls(
        chat_session_obj, 
        user_message_text, 
        config_data,
        session_id, 
        'voice_command',
        hotel_id_key=hotel_id_key
    )

    # --- 5. AI Yanıtını Sese ve Viseme'e Çevirme (TTS) ---
    app_logger.info(f"[VOICE] AI yanıtı ('{ai_text_response[:30]}...') TTS için gönderiliyor.")
    ai_speech_dict = synthesize_text_to_speech(ai_text_response)

    # --- 6. Son Yanıtı Hazırlama ve Gönderme ---
    if ai_speech_dict and ai_speech_dict.get("audio_content"):
        response_data["audio_content_b64"] = base64.b64encode(ai_speech_dict["audio_content"]).decode('utf-8')
        response_data["visemes"] = ai_speech_dict.get("visemes", [])
        response_data["session_id"] = session_id # Her zaman güncel session_id'yi döndür
        app_logger.info("[VOICE] Başarılı ses ve viseme verisi oluşturuldu.")
    else:
        response_data.update({"success": False, "error": "TTS hatası: AI yanıtı sese çevrilemedi."})
        app_logger.error("[VOICE-FAIL] TTS hatası oluştu.")
        
    return jsonify(response_data), 200 if response_data["success"] else 500

@app.route('/update_hotel_config', methods=['POST'])
def update_hotel_config_api():
    """
    Unity'den gelen otel yapılandırmasını alır ve hem 'hotels' tablosunu
    hem de 'room_types_inventory' tablosunu atomik bir işlemle günceller.
    
    Bu endpoint, otel ve envanter verisinin tutarlılığını sağlamak için
    kritik bir rol oynar.
    """
    config_data = request.get_json()
    if not config_data:
        return jsonify({"error": "Yapılandırma verisi eksik."}), 400
        
    hotel_id_key = config_data.get('hotelId')
    if not hotel_id_key:
        return jsonify({"error": "Yapılandırma verisinde 'hotelId' zorunludur."}), 400

    hotel_name = config_data.get('hotelName', 'İsimsiz Otel')
    gmb_account_id = config_data.get('gmbAccountId')
    gmb_location_id = config_data.get('gmbLocationId')
    config_json_str = json.dumps(config_data)

    conn = None
    try:
        # Veritabanı bağlantısını aç
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # --- 1. Veritabanı İşlemini Başlat (Atomiklik için) ---
        # Bu, ya tüm işlemlerin başarılı olmasını ya da bir hata durumunda
        # hiçbirinin uygulanmamasını garanti eder. Veri bütünlüğü için çok önemlidir.
        cursor.execute('BEGIN')
        app_logger.info(f"CONFIG_UPDATE: '{hotel_id_key}' için veritabanı işlemi başlatıldı.")

        # --- 2. 'hotels' Tablosunu Güncelle veya Ekle (UPSERT) ---
        cursor.execute("SELECT id FROM hotels WHERE hotel_id_key = ?", (hotel_id_key,))
        existing_hotel = cursor.fetchone()
        
        if existing_hotel:
            # Otel zaten varsa, bilgilerini güncelle
            cursor.execute("""
                UPDATE hotels 
                SET hotel_name = ?, gmb_account_id = ?, gmb_location_id = ?, hotel_config_json = ? 
                WHERE hotel_id_key = ?
            """, (hotel_name, gmb_account_id, gmb_location_id, config_json_str, hotel_id_key))
            message = f"Otel '{hotel_name}' yapılandırması güncellendi."
            app_logger.info(f"CONFIG_UPDATE: 'hotels' tablosu '{hotel_id_key}' için güncellendi.")
        else:
            # Otel yoksa, yeni bir kayıt olarak ekle
            cursor.execute("""
                INSERT INTO hotels 
                (hotel_id_key, hotel_name, gmb_account_id, gmb_location_id, hotel_config_json, is_google_connected) 
                VALUES (?, ?, ?, ?, ?, 0)
            """, (hotel_id_key, hotel_name, gmb_account_id, gmb_location_id, config_json_str))
            message = f"Yeni otel '{hotel_name}' yapılandırması eklendi."
            app_logger.info(f"CONFIG_UPDATE: 'hotels' tablosuna yeni kayıt '{hotel_id_key}' eklendi.")

        # --- 3. 'room_types_inventory' Tablosunu Senkronize Et ---
        # Bu, sorununuzu çözecek olan en kritik adımdır.
        
        room_types_from_config = config_data.get('roomTypes', [])
        if isinstance(room_types_from_config, list):
            app_logger.info(f"ENVANTER_SYNC: '{hotel_id_key}' için {len(room_types_from_config)} oda tipi senkronize edilecek.")
            
            # Önce bu otele ait mevcut envanterdeki tüm oda tiplerini alalım.
            # Bu, JSON'dan silinmiş odaları daha sonra veritabanından da silebilmek için gereklidir.
            cursor.execute("SELECT room_type_name FROM room_types_inventory WHERE hotel_config_key = ?", (hotel_id_key,))
            db_room_names = {row['room_type_name'] for row in cursor.fetchall()}

            config_room_names = set()

            for room_data in room_types_from_config:
                # Gelen verinin geçerli bir oda tanımı olduğundan emin ol
                if not isinstance(room_data, dict) or 'name' not in room_data or not room_data['name']:
                    continue # Geçersiz oda verisini atla

                room_name = room_data['name']
                total_quantity = int(room_data.get('totalRoomsOfType', 0))
                last_updated_time = datetime.now().isoformat(timespec='seconds')
                
                config_room_names.add(room_name)

                # Bu oda tipi veritabanında zaten var mı diye kontrol et
                if room_name in db_room_names:
                    # Varsa, sadece toplam adedini ve güncelleme zamanını güncelle
                    cursor.execute("""
                        UPDATE room_types_inventory 
                        SET total_quantity = ?, last_updated = ? 
                        WHERE hotel_config_key = ? AND room_type_name = ?
                    """, (total_quantity, last_updated_time, hotel_id_key, room_name))
                    app_logger.info(f"ENVANTER_SYNC: Mevcut oda '{room_name}' güncellendi. Yeni adet: {total_quantity}.")
                else:
                    # Yoksa, yeni bir envanter kaydı olarak ekle
                    cursor.execute("""
                        INSERT INTO room_types_inventory 
                        (hotel_config_key, room_type_name, total_quantity, last_updated) 
                        VALUES (?, ?, ?, ?)
                    """, (hotel_id_key, room_name, total_quantity, last_updated_time))
                    app_logger.info(f"ENVANTER_SYNC: Yeni oda '{room_name}' eklendi. Adet: {total_quantity}.")

            # --- 4. (Opsiyonel ama Önerilen) Veritabanından Eskimiş Odaları Temizle ---
            # Unity'den gelen yeni JSON'da olmayan ama veritabanında hala var olan odaları sil.
            # Bu, Unity'de bir oda tipini sildiğinizde veritabanının da temiz kalmasını sağlar.
            rooms_to_delete = db_room_names - config_room_names
            if rooms_to_delete:
                for room_to_delete in rooms_to_delete:
                    cursor.execute("DELETE FROM room_types_inventory WHERE hotel_config_key = ? AND room_type_name = ?", (hotel_id_key, room_to_delete))
                    app_logger.warning(f"ENVANTER_SYNC: Eskimiş oda '{room_to_delete}' veritabanından silindi.")

        # --- 5. İşlemi Onayla ---
        # Tüm sorgular başarılı olduysa, değişiklikleri kalıcı hale getir.
        conn.commit()
        app_logger.info(f"CONFIG_UPDATE: '{hotel_id_key}' için veritabanı işlemi başarıyla tamamlandı.")
        
        return jsonify({"message": message}), 200

    except Exception as e:
        # Herhangi bir adımda hata olursa, tüm işlemi geri al.
        if conn:
            conn.rollback()
            app_logger.exception(f"KRİTİK HATA (update_hotel_config): İşlem geri alındı!")
        return jsonify({"error": f"Sunucu hatası: {str(e)}"}), 500
    
    finally:
        # Ne olursa olsun veritabanı bağlantısını kapat.
        if conn:
            conn.close()

        
    
    # --- Facebook OAuth ve API Endpoint'leri ---

@app.route('/facebook_process_token', methods=['POST'])
def facebook_process_token():
    """Tarayıcıdan gelen User Access Token'ı işler ve yönetilen sayfaları listeler."""
    data = request.get_json()
    access_token = data.get('accessToken')
    hotel_id_key = data.get('hotel_id_key') # Hangi otel için işlem yaptığımızı bilmek için

    if not access_token or not hotel_id_key:
        return jsonify({"error": "Eksik parametre."}), 400

    try:
        graph = facebook.GraphAPI(access_token=access_token)
        # Sayfaları, isimlerini ve en önemlisi her sayfanın kendi token'ını da iste
        pages_data = graph.get_connections('me', 'accounts', fields='id,name,access_token')
        
        # Bu bilgiyi Unity'ye değil, doğrudan JS SDK'nın çalıştığı HTML'e geri gönder
        return jsonify({"locations": pages_data.get('data', [])})

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route('/facebook_finalize_selection', methods=['POST'])
def facebook_finalize_selection():
    """HTML sayfasından gelen son seçimi alır ve veritabanına kaydeder."""
    data = request.get_json()
    hotel_id_key = data.get('hotel_id_key')
    page_id = data.get('page_id')
    page_name = data.get('page_name')
    page_access_token = data.get('page_access_token')

    if not all([hotel_id_key, page_id, page_name, page_access_token]):
        return jsonify({"error": "Eksik parametre."}), 400

    try:
        # Sayfa jetonunu uzun ömürlü hale getir
        graph = facebook.GraphAPI()
        long_lived_token_data = graph.extend_access_token(FACEBOOK_APP_ID, FACEBOOK_APP_SECRET, page_access_token)
        long_lived_page_token = long_lived_token_data.get('access_token')

        encrypted_token = encrypt_data(long_lived_page_token)
        
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            UPDATE hotels SET facebook_page_id = ?, facebook_page_name = ?, 
            facebook_page_token_encrypted = ?, is_facebook_connected = 1 
            WHERE hotel_id_key = ?
        """, (page_id, page_name, encrypted_token, hotel_id_key))
        conn.commit()
        conn.close()
        
        return jsonify({"message": "Facebook Sayfası başarıyla bağlandı!"})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# --- Google OAuth ve Yorum Yönetimi Endpoint'leri ---

@app.route('/google_auth_start')
def google_auth_start():
    hotel_id_key = request.args.get('hotel_id_key')
    if not hotel_id_key: return jsonify({"error": "hotel_id_key parametresi gerekli."}), 400
    flow = get_google_auth_flow()
    state = serializer.dumps(hotel_id_key, salt='google-auth-salt')
    authorization_url, _ = flow.authorization_url(access_type='offline', prompt='consent', state=state)
    return jsonify({"authorization_url": authorization_url})


@app.route('/google_auth_callback')
def google_auth_callback():
    try:
        state = request.args.get('state')
        hotel_id_key = serializer.loads(state, salt='google-auth-salt', max_age=3600)
        flow = get_google_auth_flow()
        flow.fetch_token(authorization_response=request.url)
        
        credentials = Credentials(
            token=flow.credentials.token, refresh_token=flow.credentials.refresh_token,
            token_uri=flow.credentials.token_uri, client_id=GOOGLE_CLIENT_ID,
            client_secret=GOOGLE_CLIENT_SECRET, scopes=flow.credentials.scopes
        )
        
        locations_result = list_accounts_and_locations(credentials)
        if not locations_result.get("success"):
            error_message = locations_result.get('error', 'Bilinmeyen bir hata.')
            print(f"HATA (list_accounts_and_locations): {error_message}")
            return f"Hata: Konumlar listelenemedi. Detay: {error_message}", 500

        session['pending_google_auth'] = {
            'hotel_id_key': hotel_id_key,
            'refresh_token': credentials.refresh_token,
            'locations': locations_result.get('locations')
        }
        
        return """<html><body style='font-family: sans-serif; text-align: center; padding-top: 50px;'><h1>Doğrulama Başarılı!</h1><p>Lütfen bu pencereyi kapatıp, konum seçimi için Unity uygulamasındaki panele geri dönün.</p></body></html>"""
    except Exception as e:
        import traceback
        print(f"CRITICAL: Google callback işlenirken genel bir hata oluştu: {traceback.format_exc()}")
        return f"Beklenmedik bir hata oluştu: {e}", 500


@app.route('/get_google_locations_from_session', methods=['GET'])
def get_google_locations_from_session():
    pending_auth = session.get('pending_google_auth')
    if not pending_auth: return jsonify({"error": "Bekleyen bir kimlik doğrulama bulunmuyor."}), 404
    return jsonify(pending_auth)


@app.route('/google_select_location', methods=['POST'])
def google_select_location():
    pending_auth = session.get('pending_google_auth')
    if not pending_auth: return jsonify({"error": "Öncelikle kimlik doğrulama adımı tamamlanmalı."}), 400
    data = request.get_json()
    selected_location_id = data.get('location_id')
    selected_account_id = data.get('account_id')
    if not all([selected_location_id, selected_account_id]): return jsonify({"error": "account_id ve location_id zorunludur."}), 400
    hotel_id_key = pending_auth.get('hotel_id_key')
    refresh_token = pending_auth.get('refresh_token')
    try:
        encrypted_token = encrypt_data(refresh_token)
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("UPDATE hotels SET gmb_account_id = ?, gmb_location_id = ?, google_refresh_token_encrypted = ?, is_google_connected = 1 WHERE hotel_id_key = ?",
                       (selected_account_id, selected_location_id, encrypted_token, hotel_id_key))
        conn.commit()
        conn.close()
        session.pop('pending_google_auth', None)
        return jsonify({"message": "Google İşletme Profili başarıyla bağlandı ve kaydedildi."})
    except Exception as e:
        print(f"CRITICAL: Google konumu kaydedilirken hata: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/sync_google_reviews', methods=['POST'])
def sync_google_reviews_api():
    data = request.get_json()
    hotel_id_key = data.get('hotel_id_key')
    if not hotel_id_key: return jsonify({"error": "hotel_id_key gerekli."}), 400
    socketio.start_background_task(sync_reviews_from_google, hotel_id_key=hotel_id_key)
    return jsonify({"message": "Yorum senkronizasyon görevi arka planda başlatıldı."}), 202


@app.route('/get_google_reviews', methods=['GET'])
def get_google_reviews_api():
    hotel_id_key = request.args.get('hotel_id_key')
    if not hotel_id_key: return jsonify({"error": "hotel_id_key parametresi gerekli."}), 400
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM google_reviews ORDER BY create_time DESC")
        reviews = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return jsonify({"reviews": reviews})
    except Exception as e: return jsonify({"error": str(e)}), 500


@app.route('/generate_review_response_draft', methods=['POST'])
def generate_review_response_api():
    data = request.get_json()
    if not data: return jsonify({"error": "İstek gövdesi boş."}), 400
    comment = data.get('comment')
    star_rating = data.get('star_rating')
    hotel_name = data.get('hotel_name', 'Otelimiz')
    review_id = data.get('review_id') # Hangi yoruma ait olduğunu bilmek için
    if comment is None or star_rating is None or review_id is None: return jsonify({"error": "review_id, yorum metni ve yıldız puanı gerekli."}), 400
    result = generate_ai_response_draft(comment, star_rating, hotel_name)
    if result.get("success"):
        result['review_id'] = review_id # Yanıta review_id'yi ekle
    return jsonify(result)


@app.route('/post_review_reply', methods=['POST'])
def post_review_reply_api():
    data = request.get_json()
    if not data: return jsonify({"error": "İstek gövdesi boş."}), 400
    hotel_id_key = data.get('hotel_id_key')
    review_id = data.get('review_id')
    reply_text = data.get('reply_text')
    if not all([hotel_id_key, review_id, reply_text]): return jsonify({"error": "hotel_id_key, review_id ve reply_text zorunludur."}), 400
    result = post_reply_to_google(hotel_id_key, review_id, reply_text)
    return jsonify(result)

# --- Mevcut Diğer Endpoint'ler ---

@app.route('/start_chat_with_config', methods=['POST'])
def start_chat_with_config_api():
    """
    Unity panelinden gelen otel yapılandırması ile yeni bir test sohbeti başlatır.
    Bu fonksiyon artık tamamen stateless çalışır ve oturumu Redis'e kaydeder.
    """
    app_logger.info("--- [HTTP-POST] /start_chat_with_config tetiklendi ---")
    config_data = request.get_json()
    if not config_data:
        return jsonify({"error": "JSON verisi eksik."}), 400

    # Otelin benzersiz anahtarını JSON'dan al.
    hotel_id_key = config_data.get('hotelId')
    if not hotel_id_key:
        # Geriye dönük uyumluluk veya esneklik için bir fallback oluşturabiliriz.
        hotel_name_from_config = config_data.get('hotelName', f"otel_{str(uuid.uuid4())[:6]}")
        hotel_id_key = hotel_name_from_config.lower().replace(" ", "_")
        app_logger.warning(f"/start_chat_with_config için 'hotelId' bulunamadı, varsayılan atandı: {hotel_id_key}")

    hotel_name = config_data.get('hotelName', hotel_id_key)

    # === STATELESS MİMARİ UYGULAMASI ===
    # Artık 'active_chat_sessions' RAM sözlüğünü KULLANMIYORUZ.

    system_instruction = generate_system_instruction_for_chat(config_data, source_type="Unity Dashboard")
    session_id = str(uuid.uuid4())    
    
    try:
        # Yeni bir Gemini sohbet nesnesi oluştur (geçmişsiz).
        model = genai.GenerativeModel(MODEL_NAME, system_instruction=system_instruction, tools=GEMINI_TOOLS_CONFIG)
        chat_obj = model.start_chat(history=[]) # Bu nesne sadece ilk mesaj için kullanılacak ve sonra atılacak.

        # Oturumun meta verilerini Redis'e kaydet.
        session_data_for_redis = {
            'config_data': config_data,
            'source': 'unity_dashboard',
            'hotel_config_key': hotel_id_key,
            'hotelName': hotel_name,
            'start_time': datetime.now(timezone.utc).isoformat(),
            'last_activity_time': time.time(),
            'presence': 'active' # Unity dashboard sohbetleri de aktif sayılır.
        }
        save_session_to_redis(session_id, session_data_for_redis)
        app_logger.info(f"REDIS_SAVE (Unity): Session {session_id[:8]} Redis'e kaydedildi.")

        # İlk "Merhaba" mesajını, YENİ ve ZORUNLU olan 'hotel_id_key' parametresiyle kaydet.
        initial_msg = f"Merhaba! Ben {hotel_name} (Dashboard Test) asistanınız. Nasıl yardımcı olabilirim?"
        save_chat_message_to_db(
            session_id=session_id, 
            sender="AI", 
            message=initial_msg, 
            socketio_instance=socketio,
            source='unity_dashboard',
            hotel_id_key=hotel_id_key # <<< EKSİK OLAN KRİTİK PARAMETRE
        )
        
        # Yeni bir sohbet başladığı için tüm panelleri güncelle.
        broadcast_active_chats_update()
        
        app_logger.info(f"SESSION_START (Unity Dashboard): {session_id[:8]} - Otel: {hotel_name} (ID: {hotel_id_key})")
        return jsonify({"session_id": session_id, "initial_message": initial_msg}), 200

    except Exception as e:
        app_logger.exception("HATA (start_chat_with_config_api):")
        return jsonify({"error": f"Sohbet oturumu başlatılamadı: {str(e)}"}), 500

@app.route('/get_reservations', methods=['GET'])
def get_reservations_api_endpoint():
    """
    BELİRLİ BİR OTELE ait tüm rezervasyonları listeler.
    Veri izolasyonunu sağlamak için 'hotel_id_key' parametresi zorunludur.
    """
    hotel_id_key = request.args.get('hotel_id_key')
    
    if not hotel_id_key:
        app_logger.error("[GET-RESERVATIONS-FAIL] 'hotel_id_key' parametresi eksik.")
        return jsonify({"error": "'hotel_id_key' query parametresi zorunludur."}), 400

    app_logger.info(f"--- [HTTP-GET] /get_reservations tetiklendi --- Otel: {hotel_id_key}")
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # === NİHAİ VE DOĞRU SORGULAMA ===
        # Artık doğrudan ve güvenilir olan 'hotel_id_key' sütununa göre filtreliyoruz.
        cursor.execute(
            "SELECT * FROM reservations WHERE hotel_id_key = ? ORDER BY id DESC", 
            (hotel_id_key,)
        )
        # ==================================
        
        reservations = [dict(row) for row in cursor.fetchall()]
        app_logger.info(f"[GET-RESERVATIONS] '{hotel_id_key}' için {len(reservations)} adet rezervasyon bulundu.")
        return jsonify({"reservations": reservations}), 200
        
    except Exception:
        app_logger.exception(f"[GET-RESERVATIONS-FAIL] Beklenmedik hata. Otel: {hotel_id_key}")
        return jsonify({"error": "Sunucu hatası."}), 500
    finally:
        if conn:
            conn.close()

@app.route('/get_active_chats', methods=['GET'])
def get_active_chats_api_endpoint():
    """
    Bu endpoint, artık doğrudan Redis'i okuyarak o anki tüm aktif
    sohbetlerin tam ve tutarlı bir listesini döndürür.
    
    YENİ: Bu fonksiyon da artık 'presence' durumunu kontrol eder ve
    sadece 'active' olanları döndürür.
    """
    app_logger.info("--- [HTTP-GET] /get_active_chats tetiklendi ---")
    if not redis_client:
        app_logger.error("[HTTP-GET-FAIL] /get_active_chats - Redis bağlantısı yok.")
        return jsonify({"error": "Redis bağlantısı yok.", "active_chats": [], "count": 0}), 500

    active_list = []
    try:
        session_keys = redis_client.keys(f"{SESSION_REDIS_PREFIX}*")
        if session_keys:
            session_data_list = redis_client.mget(session_keys)
            for i, session_json in enumerate(session_data_list):
                if session_json:
                    try:
                        s_data = json.loads(session_json)
                        
                        # === FİLTRELEME VE DÜZELTME ===
                        # 1. Sadece 'presence' durumu 'active' olanları dikkate al.
                        if s_data.get('presence') == 'active':
                            # 2. .decode() çağrısını kaldır, çünkü redis_client zaten string döndürüyor.
                            session_id = session_keys[i].replace(SESSION_REDIS_PREFIX, "")

                            # Misafir adını çıkarmaya çalış
                            try:
                                conn = get_db_connection()
                                cursor = conn.cursor()
                                extracted_name = extract_guest_name_from_messages(session_id, cursor)
                                guest_name = extracted_name if extracted_name else "Anonim Misafir"
                                conn.close()
                            except:
                                guest_name = "Anonim Misafir"

                            active_list.append({
                                "session_id": session_id,
                                "source": s_data.get('source', 'unknown'),
                                "hotel_name": s_data.get('hotelName', 'Bilinmeyen Otel'),
                                "start_time": s_data.get('start_time', 'N/A'),
                                "hotel_config_key": s_data.get('hotel_config_key'),
                                "guest_name": guest_name
                            })
                    except (json.JSONDecodeError, AttributeError) as e:
                        app_logger.warning(f"[HTTP-GET] /get_active_chats - Redis verisi işlenemedi: {session_keys[i]}. Hata: {e}")
                        continue
                        
    except Exception as e:
        app_logger.exception("[HTTP-GET-FAIL] /get_active_chats - Genel hata.")
        return jsonify({"error": str(e), "active_chats": [], "count": 0}), 500

    app_logger.info(f"[HTTP-GET] /get_active_chats - {len(active_list)} aktif sohbet bulundu ve döndürüldü.")
    return jsonify({"active_chats": active_list, "count": len(active_list)}), 200

@app.route('/update_reservation_status/<int:reservation_db_id>', methods=['POST'])
def update_reservation_status_api_endpoint(reservation_db_id):
    data = request.get_json(); new_status = data.get('new_status'); reason = data.get('reason')
    if not new_status: return jsonify({"error": "'new_status' gerekli."}), 400
    valid_statuses = [STATUS_AI_PENDING_HUMAN, STATUS_HUMAN_CONFIRMED, STATUS_CANCELLED_GUEST, STATUS_CANCELLED_HOTEL, STATUS_COMPLETED, STATUS_NO_SHOW]
    if new_status not in valid_statuses: return jsonify({"error": f"Geçersiz statü: {new_status}."}), 400
    conn = None
    try:
        conn = get_db_connection(); cursor = conn.cursor(); current_time_iso = datetime.now().isoformat(timespec='seconds')
        cursor.execute("SELECT id, status FROM reservations WHERE id = ?", (reservation_db_id,))
        if not cursor.fetchone(): return jsonify({"error": f"Rezervasyon {reservation_db_id} bulunamadı."}), 404
        if reason: cursor.execute("UPDATE reservations SET status = ?, last_status_update_time = ?, cancellation_reason = ? WHERE id = ?", (new_status, current_time_iso, reason, reservation_db_id))
        else: cursor.execute("UPDATE reservations SET status = ?, last_status_update_time = ? WHERE id = ?", (new_status, current_time_iso, reservation_db_id))
        conn.commit()
        if cursor.rowcount == 0: return jsonify({"error": f"Rezervasyon {reservation_db_id} güncellenemedi."}), 404
        socketio.emit('reservation_status_updated', {
            "reservation_db_id": reservation_db_id, 
            "new_status": new_status, 
            "reason": reason
        })
        print(f"STATU_GUNCELLEME: Rez.ID {reservation_db_id} -> {new_status}")
        return jsonify({"message": f"Rezervasyon {reservation_db_id} statüsü '{new_status}' oldu.", "updated_id": reservation_db_id}), 200
    except sqlite3.Error as e: print(f"HATA (update_status DB): {e}"); return jsonify({"error": f"DB hatası: {e}"}), 500
    except Exception as ex: print(f"HATA (update_status Genel): {ex}"); return jsonify({"error": f"Sorun: {ex}"}), 500
    finally:
        if conn: conn.close()

@app.route('/get_chat_history/<session_id_param>', methods=['GET'])
def get_chat_history_api_endpoint(session_id_param):
    conn = None
    if not session_id_param: return jsonify({"error": "session_id parametresi eksik."}), 400
    try:
        conn = get_db_connection(); cursor = conn.cursor()
        cursor.execute("SELECT id, session_id, sender, message, timestamp, tool_name, tool_args, tool_response, source FROM chat_history WHERE session_id = ? ORDER BY id ASC", (session_id_param,))
        history_list = [dict(row) for row in cursor.fetchall()]
        for item in history_list:
            for key in ['tool_args', 'tool_response']:
                if item.get(key):
                    try: item[key] = json.loads(item[key])
                    except (json.JSONDecodeError, TypeError): item[key] = {"error": "JSON parse edilemedi veya null değil", "raw_value": item[key]}
        return jsonify({"chat_history": history_list, "session_id": session_id_param}), 200
    except sqlite3.Error as e: print(f"HATA (get_chat_history DB): {e}"); return jsonify({"error": f"DB Hatası: {e}"}), 500
    except Exception as e_gen: print(f"HATA (get_chat_history Genel): {e_gen}"); return jsonify({"error": f"Genel Hata: {e_gen}"}), 500
    finally:
        if conn: conn.close()

@app.route('/get_dashboard_totals', methods=['GET'])
def get_dashboard_totals_api_endpoint():
    hotel_id_key = request.args.get('hotel_id_key')
    if not hotel_id_key:
        app_logger.error("[GET-TOTALS-FAIL] 'hotel_id_key' parametresi eksik.")
        return jsonify({"error": "'hotel_id_key' query parametresi zorunludur."}), 400
    
    app_logger.info(f"--- [HTTP-GET] /get_dashboard_totals tetiklendi --- Otel: {hotel_id_key}")

    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT COUNT(DISTINCT session_id) AS total_chat_sessions FROM chat_history WHERE hotel_id_key = ?", (hotel_id_key,))
        total_chats = cursor.fetchone()['total_chat_sessions']
        
        cursor.execute(f"SELECT COUNT(id) AS total_confirmed_reservations FROM reservations WHERE status = '{STATUS_HUMAN_CONFIRMED}' AND hotel_id_key = ?", (hotel_id_key,))
        total_confirmed_reservations = cursor.fetchone()['total_confirmed_reservations']
        
        cursor.execute("SELECT COUNT(id) AS total_ai_initiated_reservations FROM reservations WHERE session_id_at_booking IS NOT NULL AND session_id_at_booking != '' AND hotel_id_key = ?", (hotel_id_key,))
        total_ai_initiated_reservations = cursor.fetchone()['total_ai_initiated_reservations']

        # Not: İsterseniz diğer statülerdeki rezervasyon sayılarını da buraya ekleyebilirsiniz.
        # Örn: cursor.execute("SELECT COUNT(id) FROM reservations WHERE hotel_id_key = ?", (hotel_id_key,))
        #      total_reservations = cursor.fetchone()[0]

        response_data = {
            "total_chat_sessions": total_chats,
            "total_confirmed_reservations": total_confirmed_reservations,
            "total_ai_initiated_reservations": total_ai_initiated_reservations
            # "total_reservations": total_reservations // İsterseniz ekleyin
        }
        app_logger.info(f"[GET-TOTALS] '{hotel_id_key}' için metrikler hesaplandı.")
        return jsonify(response_data), 200

    except Exception as e:
        app_logger.exception(f"HATA (get_dashboard_totals_api_endpoint - Otel: {hotel_id_key}):")
        return jsonify({"error": str(e)}), 500
    finally:
        if conn: conn.close()     

@app.route('/chat', methods=['POST'])
def http_chat_for_unity_endpoint():
    """
    Unity panelinden gelen manuel/test sohbet isteklerini işler.
    Bu endpoint, artık tamamen "stateless" mimariye uygun çalışır.
    
    ÖNEMLİ: Bu endpoint'in bir oturum başlatma özelliği yoktur. Sadece
    'start_chat_with_config' ile başlatılmış ve Redis'te var olan bir
    oturumu devam ettirebilir.

    İşlem Adımları:
    1.  Gelen isteği doğrular, 'session_id' ve 'message' parametrelerini alır.
    2.  Oturumun geçerliliğini Redis'ten kontrol eder.
    3.  Geçerliyse, oturum geçmişini veritabanından çekerek Gemini AI nesnesini
        yeniden inşa eder (rehydrate).
    4.  Kullanıcının mesajını işler ve AI yanıtını alır.
    5.  AI yanıtını JSON formatında Unity paneline geri döndürür.
    """
    app_logger.info("--- [HTTP-CHAT] http_chat_for_unity_endpoint tetiklendi ---")
    req = request.get_json()
    msg = req.get('message')
    sid = req.get('session_id')
    
    if not msg or not sid:
        app_logger.error("[HTTP-CHAT-FAIL] Mesaj veya session_id eksik.")
        return jsonify({"error": "'message' ve 'session_id' alanları zorunludur."}), 400

    # --- Oturumu Yeniden İnşa Etme (Rehydration) ---
    app_logger.info(f"[HTTP-CHAT] Mevcut oturum devam ediyor: {sid[:8]}")
    
    session_data_from_redis = get_session_from_redis(sid)
    if not session_data_from_redis:
        app_logger.error(f"[HTTP-CHAT-FAIL] Oturum Redis'te bulunamadı: {sid[:8]}")
        return jsonify({"error": "Geçersiz veya süresi dolmuş oturum."}), 404

    config_data = session_data_from_redis.get('config_data', {})
    hotel_id_key = session_data_from_redis.get('hotel_config_key') # <<< KRİTİK EKLEME
    if not hotel_id_key:
        app_logger.error(f"KRİTİK: Redis'teki Unity oturumunda 'hotel_config_key' eksik! Session: {sid[:8]}")
        return jsonify({"error": "Oturum verisi bozuk, otel kimliği bulunamadı."}), 500
    
    # Veritabanından geçmişi çek ve Gemini için hazırla
    conn = get_db_connection()
    db_history_rows = conn.execute(
        "SELECT sender, message, tool_name, tool_args, tool_response FROM chat_history WHERE session_id = ? ORDER BY id ASC", 
        (sid,)
    ).fetchall()
    conn.close()

    # (Buraya da en güncel ve tam geçmiş oluşturma döngüsünü koymak en doğrusu olur)
    gemini_history = []
    for row in db_history_rows:
        sender_upper = row['sender'].upper()
        if sender_upper == 'USER':
            gemini_history.append({'role': 'user', 'parts': [{'text': row['message']}]})
        elif sender_upper == 'AI':
            if row['tool_name'] and row['tool_args']:
                try:
                    gemini_history.append({'role': 'model', 'parts': [{'function_call': {'name': row['tool_name'], 'args': json.loads(row['tool_args'])}}]})
                    if row['tool_response']:
                        gemini_history.append({'role': 'tool', 'parts': [{'function_response': {'name': row['tool_name'], 'response': json.loads(row['tool_response'])}}]})
                except (json.JSONDecodeError, TypeError):
                    pass
            if row['message']:
                gemini_history.append({'role': 'model', 'parts': [{'text': row['message']}]})

    # Gemini nesnesini yeniden inşa et
    try:
        system_instruction = generate_system_instruction_for_chat(config_data, source_type="Unity Dashboard")
        model = genai.GenerativeModel(MODEL_NAME, system_instruction=system_instruction, tools=GEMINI_TOOLS_CONFIG)
        chat_session_obj = model.start_chat(history=gemini_history)
        app_logger.info(f"[HTTP-CHAT] Rehydration başarılı. {len(gemini_history)} adımlık geçmiş yüklendi.")
    except Exception as e_gemini:
        app_logger.exception("[HTTP-CHAT-FAIL] Gemini nesnesi oluşturulamadı.")
        return jsonify({"error": f"Yapay zeka motoru başlatılamadı: {e_gemini}"}), 500

    # Kullanıcının yeni mesajını DB'ye kaydet
    save_chat_message_to_db(
        sid, "USER", msg, 
        socketio_instance=socketio, 
        source='unity_dashboard',
        hotel_id_key=hotel_id_key 
    )
    
    # AI yanıtını al
    ai_resp, _ = handle_gemini_response_and_tool_calls(
        chat_session_obj, 
        msg, 
        config_data, 
        sid, 
        'unity_dashboard',
        hotel_id_key=hotel_id_key 
    )
    
    return jsonify({"session_id": sid, "response": ai_resp, "timestamp": int(time.time())})

@app.route('/get_analysis_reports', methods=['GET'])
def get_analysis_reports_api():
    """Veritabanındaki tüm AI analiz raporlarını getirir."""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT id, report_type, start_date, end_date, generated_at, report_summary_text FROM dashboard_analysis_reports ORDER BY id DESC")
        reports = [dict(row) for row in cursor.fetchall()]
        return jsonify({"reports": reports}), 200
    except Exception as e:
        print(f"HATA (get_analysis_reports): {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        if conn:
            conn.close()

@app.route('/get_analysis_report_detail/<int:report_id>', methods=['GET'])
def get_analysis_report_detail_api(report_id):
    """Belirli bir raporun tam JSON verisini getirir."""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT report_data_json FROM dashboard_analysis_reports WHERE id = ?", (report_id,))
        report = cursor.fetchone()
        if report and report['report_data_json']:
            report_data = json.loads(report['report_data_json'])
            return jsonify(report_data), 200
        else:
            return jsonify({"error": "Rapor bulunamadı veya detay içermiyor."}), 404
    except Exception as e:
        print(f"HATA (get_analysis_report_detail): {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        if conn:
            conn.close()

@app.route('/run_analysis', methods=['POST'])
def run_analysis_api():
    """
    Unity panelinden gelen istekle manuel olarak analiz görevini çalıştırır.
    """
    data = request.get_json()
    analysis_type = data.get('type', 'weekly')

    print(f"API isteğiyle manuel analiz başlatılıyor: TİP = {analysis_type}")

    try:
        if analysis_type.lower() == 'weekly':
            socketio.start_background_task(run_weekly_analysis)
            message = "Haftalık analiz görevi arka planda başlatıldı. Raporun tamamlanması birkaç dakika sürebilir."
        else:
            return jsonify({"error": "Geçersiz analiz tipi. 'weekly' olmalı."}), 400
        
        return jsonify({"message": message}), 202
    except Exception as e:
        print(f"CRITICAL: Analiz görevi başlatılamadı: {e}")
        return jsonify({"error": f"Analiz görevi başlatılırken bir hata oluştu: {str(e)}"}), 500

def extract_guest_name_from_messages(session_id, cursor):
    """
    Chat mesajlarından misafir adını çıkarmaya çalışır
    """
    # Rezervasyon tool_response'larından isim çıkar
    cursor.execute("""
        SELECT tool_response FROM chat_history
        WHERE session_id = ? AND tool_name = 'create_reservation'
        AND tool_response IS NOT NULL
        ORDER BY id DESC LIMIT 1
    """, (session_id,))

    reservation_row = cursor.fetchone()
    if reservation_row and reservation_row['tool_response']:
        try:
            tool_response = json.loads(reservation_row['tool_response'])
            if 'guest_name' in str(tool_response):
                # Tool response'dan isim çıkar
                response_str = str(tool_response)
                if 'guest_name' in response_str:
                    return "Rezervasyon Yapan Misafir"
        except:
            pass

    # USER mesajlarından isim pattern'i ara
    cursor.execute("""
        SELECT message FROM chat_history
        WHERE session_id = ? AND sender = 'USER'
        ORDER BY id ASC
    """, (session_id,))

    user_messages = cursor.fetchall()
    for msg_row in user_messages:
        message = msg_row['message'].lower()

        # İsim pattern'leri ara
        name_patterns = [
            'adım ', 'ismim ', 'ben ', 'benim adım',
            'adı ', 'ismi ', 'name is', 'i am'
        ]

        for pattern in name_patterns:
            if pattern in message:
                # Pattern'den sonraki kelimeyi al
                parts = message.split(pattern)
                if len(parts) > 1:
                    potential_name = parts[1].split()[0] if parts[1].split() else ""
                    if len(potential_name) > 2 and potential_name.isalpha():
                        return potential_name.title()

    return None

@app.route('/get_historical_chat_sessions', methods=['GET'])
def get_historical_chat_sessions():
    """
    Geçmiş chat oturumlarını döndürür (tamamlanmış, terk edilmiş vb.)
    """
    hotel_id_key = request.args.get('hotel_id_key')
    if not hotel_id_key:
        return jsonify({"error": "hotel_id_key parametresi gerekli."}), 400

    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Son 30 günün chat oturumlarını çek
        thirty_days_ago = (datetime.now() - timedelta(days=30)).isoformat()

        # Chat oturumlarını session_id bazında grupla ve özet bilgileri çıkar
        cursor.execute("""
            SELECT
                session_id,
                hotel_id_key,
                MIN(timestamp) as start_time,
                MAX(timestamp) as end_time,
                COUNT(*) as message_count,
                GROUP_CONCAT(DISTINCT sender) as participants,
                MAX(CASE WHEN sender = 'USER' THEN message END) as last_user_message,
                MAX(CASE WHEN sender = 'AI' THEN message END) as last_ai_message
            FROM chat_history
            WHERE hotel_id_key = ?
            AND timestamp >= ?
            AND session_id NOT IN (
                SELECT DISTINCT session_id
                FROM chat_history
                WHERE timestamp >= datetime('now', '-1 hour')
                AND hotel_id_key = ?
            )
            GROUP BY session_id
            ORDER BY MAX(timestamp) DESC
            LIMIT 50
        """, (hotel_id_key, thirty_days_ago, hotel_id_key))

        sessions = cursor.fetchall()

        historical_sessions = []
        for session in sessions:
            session_dict = dict(session)

            # Başlangıç ve bitiş zamanlarını parse et
            start_time = datetime.fromisoformat(session_dict['start_time'])
            end_time = datetime.fromisoformat(session_dict['end_time'])

            # Süreyi hesapla
            duration_seconds = (end_time - start_time).total_seconds()
            duration_minutes = int(duration_seconds / 60)

            # Sonucu tahmin et
            outcome = 'information_provided'  # Varsayılan
            last_user_msg = session_dict['last_user_message'] or ''
            if any(word in last_user_msg.lower() for word in ['rezervasyon', 'booking', 'oda ayır']):
                outcome = 'reservation_made'
            elif duration_minutes < 2:
                outcome = 'abandoned'
            elif any(word in last_user_msg.lower() for word in ['teşekkür', 'sağol', 'thanks']):
                outcome = 'information_provided'

            # Misafir adını çıkarmaya çalış
            extracted_name = extract_guest_name_from_messages(session_dict['session_id'], cursor)
            guest_name = extracted_name if extracted_name else "Anonim Misafir"

            # Durumu belirle
            status = 'completed' if duration_minutes >= 2 else 'abandoned'

            historical_sessions.append({
                'id': session_dict['session_id'],
                'sessionId': session_dict['session_id'],
                'guestName': guest_name,
                'startTime': start_time.strftime('%H:%M'),
                'startDate': start_time.strftime('%d/%m/%Y'),
                'endTime': end_time.strftime('%H:%M'),
                'duration': f"{duration_minutes} dakika",
                'status': status,
                'source': 'web_widget',  # Varsayılan
                'messageCount': session_dict['message_count'],
                'outcome': outcome,
                'lastUserMessage': session_dict['last_user_message'],
                'lastAiMessage': session_dict['last_ai_message']
            })

        return jsonify({
            "historical_sessions": historical_sessions,
            "count": len(historical_sessions)
        }), 200

    except Exception as e:
        app_logger.exception(f"Error getting historical chat sessions for {hotel_id_key}")
        return jsonify({"error": f"Sunucu hatası: {str(e)}"}), 500

    finally:
        if conn:
            conn.close()

@app.route('/send_admin_message', methods=['POST'])
def send_admin_message():
    """
    Admin panelinden chat'e mesaj gönderme endpoint'i
    """
    data = request.get_json()
    session_id = data.get('session_id')
    message = data.get('message')

    if not session_id or not message:
        return jsonify({"error": "session_id ve message parametreleri gerekli."}), 400

    try:
        # Redis'ten session bilgilerini al
        session_data = get_session_from_redis(session_id)
        if not session_data:
            return jsonify({"error": "Session bulunamadı veya süresi dolmuş."}), 404

        hotel_id_key = session_data.get('hotel_config_key')

        # Mesajı veritabanına kaydet
        save_chat_message_to_db(
            session_id=session_id,
            sender='ADMIN',
            message=message,
            socketio_instance=socketio,
            source='admin_panel',
            hotel_id_key=hotel_id_key
        )

        # Mesajı web widget'a gönder
        socketio.emit('admin_message_to_widget', {
            'session_id': session_id,
            'message': message,
            'sender': 'ADMIN',
            'timestamp': datetime.now().isoformat()
        }, broadcast=True)

        app_logger.info(f"Admin message sent to session {session_id[:8]}: {message[:50]}...")

        return jsonify({
            "success": True,
            "message": "Mesaj başarıyla gönderildi"
        }), 200

    except Exception as e:
        app_logger.exception(f"Error sending admin message to session {session_id}")
        return jsonify({"error": f"Mesaj gönderilemedi: {str(e)}"}), 500

@app.route('/get_reservation_chat_history/<reservation_id>', methods=['GET'])
def get_reservation_chat_history(reservation_id):
    """
    Belirli bir rezervasyona ait chat geçmişini döndürür
    """
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Önce rezervasyonun session_id'sini bul
        cursor.execute("""
            SELECT session_id_at_booking, guest_name, guest_surname, room_type, check_in_date, check_out_date
            FROM reservations
            WHERE reservation_id = ?
        """, (reservation_id,))

        reservation_row = cursor.fetchone()
        if not reservation_row:
            return jsonify({"error": "Rezervasyon bulunamadı."}), 404

        reservation_dict = dict(reservation_row)
        session_id = reservation_dict['session_id_at_booking']

        if not session_id:
            return jsonify({
                "reservation_info": reservation_dict,
                "chat_history": [],
                "message": "Bu rezervasyon için chat geçmişi bulunmuyor."
            }), 200

        # Chat geçmişini çek
        cursor.execute("""
            SELECT id, session_id, sender, message, timestamp, tool_name, tool_args, tool_response, source
            FROM chat_history
            WHERE session_id = ?
            ORDER BY id ASC
        """, (session_id,))

        chat_history = []
        for row in cursor.fetchall():
            chat_dict = dict(row)

            # JSON alanları parse et
            for key in ['tool_args', 'tool_response']:
                if chat_dict.get(key):
                    try:
                        chat_dict[key] = json.loads(chat_dict[key])
                    except:
                        pass

            chat_history.append(chat_dict)

        return jsonify({
            "reservation_info": reservation_dict,
            "chat_history": chat_history,
            "session_id": session_id,
            "message_count": len(chat_history)
        }), 200

    except Exception as e:
        app_logger.exception(f"Error getting chat history for reservation {reservation_id}")
        return jsonify({"error": f"Chat geçmişi alınırken hata oluştu: {str(e)}"}), 500

    finally:
        if conn:
            conn.close()