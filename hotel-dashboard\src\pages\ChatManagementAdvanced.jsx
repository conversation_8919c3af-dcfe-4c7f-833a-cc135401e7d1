import { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Avatar,
  Chip,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  TextField,
  IconButton,
  Badge,
  Paper,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Chat as ChatIcon,
  Person as PersonIcon,
  SmartToy as BotIcon,
  Send as SendIcon,
  Refresh as RefreshIcon,
  Visibility as ViewIcon,
  VolumeUp as SpeakIcon,
  Stop as StopIcon,
  History as HistoryIcon,
  LiveTv as LiveIcon,
  Message as MessageIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  RadioButtonChecked as OnlineIcon,
  RadioButtonUnchecked as OfflineIcon,
  Notifications as NotificationIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { useApi } from '../contexts/ApiContext';
import io from 'socket.io-client';

const ChatManagementAdvanced = () => {
  const { getActiveChatSessions, getChatHistory, currentHotelId } = useApi();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Live Chat States
  const [activeSessions, setActiveSessions] = useState([]);
  const [selectedSession, setSelectedSession] = useState(null);
  const [chatHistory, setChatHistory] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [liveMessages, setLiveMessages] = useState([]);
  
  // Historical Chat States
  const [historicalSessions, setHistoricalSessions] = useState([]);
  const [selectedHistoricalSession, setSelectedHistoricalSession] = useState(null);
  const [historicalChatHistory, setHistoricalChatHistory] = useState([]);
  
  // Socket connection
  const [socket, setSocket] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const messagesEndRef = useRef(null);
  const liveMessagesEndRef = useRef(null);

  // Socket.IO bağlantısı
  useEffect(() => {
    const newSocket = io('http://localhost:5000', {
      transports: ['websocket', 'polling']
    });

    newSocket.on('connect', () => {
      console.log('Socket.IO bağlantısı kuruldu');
      setConnectionStatus('connected');
    });

    newSocket.on('disconnect', () => {
      console.log('Socket.IO bağlantısı kesildi');
      setConnectionStatus('disconnected');
    });

    // Canlı mesaj dinleme
    newSocket.on('live_chat_message', (data) => {
      console.log('Canlı mesaj alındı:', data);
      setLiveMessages(prev => [...prev, {
        ...data,
        timestamp: new Date(data.timestamp).toLocaleTimeString('tr-TR')
      }]);
      
      // Eğer seçili session'a ait mesajsa, chat history'yi güncelle
      if (selectedSession && data.session_id === selectedSession.sessionId) {
        setChatHistory(prev => [...prev, {
          sender: data.sender,
          message: data.message,
          timestamp: data.timestamp
        }]);
      }
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, [selectedSession]);

  // Aktif chat oturumlarını çek
  const fetchActiveSessions = useCallback(async () => {
    try {
      setLoading(true);
      const activeChatsData = await getActiveChatSessions(currentHotelId);
      
      const formattedSessions = activeChatsData.active_chats?.map(session => ({
        id: session.session_id,
        sessionId: session.session_id,
        guestName: `Misafir (${session.source})`,
        hotelName: session.hotel_name,
        startTime: new Date(session.start_time).toLocaleTimeString('tr-TR'),
        startDate: new Date(session.start_time).toLocaleDateString('tr-TR'),
        status: session.presence || 'active',
        source: session.source,
        lastActivity: new Date(session.start_time),
        messageCount: 0
      })) || [];

      setActiveSessions(formattedSessions);
      setError(null);
    } catch (err) {
      console.error('Aktif oturumlar yüklenirken hata:', err);
      setError('Aktif chat oturumları yüklenirken hata oluştu.');
    } finally {
      setLoading(false);
    }
  }, [getActiveChatSessions, currentHotelId]);

  // Geçmiş chat oturumlarını çek (demo data)
  const fetchHistoricalSessions = useCallback(async () => {
    try {
      // Demo data - gerçek API'den gelecek
      const historicalData = [
        {
          id: 'hist_001',
          sessionId: 'hist_001',
          guestName: 'Ahmet Yılmaz',
          startTime: '14:30',
          startDate: '15/12/2024',
          endTime: '14:45',
          duration: '15 dakika',
          status: 'completed',
          source: 'web_widget',
          messageCount: 12,
          outcome: 'reservation_made'
        },
        {
          id: 'hist_002',
          sessionId: 'hist_002',
          guestName: 'Fatma Kaya',
          startTime: '13:15',
          startDate: '15/12/2024',
          endTime: '13:25',
          duration: '10 dakika',
          status: 'completed',
          source: 'web_widget',
          messageCount: 8,
          outcome: 'information_provided'
        },
        {
          id: 'hist_003',
          sessionId: 'hist_003',
          guestName: 'Mehmet Demir',
          startTime: '12:00',
          startDate: '15/12/2024',
          endTime: '12:30',
          duration: '30 dakika',
          status: 'abandoned',
          source: 'web_widget',
          messageCount: 15,
          outcome: 'abandoned'
        }
      ];

      setHistoricalSessions(historicalData);
    } catch (err) {
      console.error('Geçmiş oturumlar yüklenirken hata:', err);
    }
  }, []);

  // Chat geçmişini çek
  const fetchChatHistory = useCallback(async (sessionId) => {
    try {
      const historyData = await getChatHistory(sessionId);
      
      const formattedHistory = historyData.messages?.map(msg => ({
        sender: msg.sender,
        message: msg.message,
        timestamp: msg.timestamp
      })) || [];

      if (tabValue === 0) {
        setChatHistory(formattedHistory);
      } else {
        setHistoricalChatHistory(formattedHistory);
      }
    } catch (err) {
      console.error('Chat geçmişi yüklenirken hata:', err);
    }
  }, [getChatHistory, tabValue]);

  // Mesaj gönder
  const handleSendMessage = () => {
    if (!newMessage.trim() || !selectedSession) return;

    // Socket üzerinden mesaj gönder
    if (socket && socket.connected) {
      socket.emit('admin_message', {
        session_id: selectedSession.sessionId,
        message: newMessage,
        sender: 'ADMIN'
      });

      // Local olarak mesajı ekle
      setChatHistory(prev => [...prev, {
        sender: 'ADMIN',
        message: newMessage,
        timestamp: new Date().toISOString()
      }]);

      setNewMessage('');
    }
  };

  // Canlı dinlemeyi başlat/durdur
  const toggleLiveListening = () => {
    setIsListening(!isListening);
    if (!isListening) {
      setLiveMessages([]);
    }
  };

  // Scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatHistory]);

  useEffect(() => {
    liveMessagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [liveMessages]);

  // Initial data fetch
  useEffect(() => {
    fetchActiveSessions();
    fetchHistoricalSessions();
    
    // Her 30 saniyede bir aktif oturumları güncelle
    const interval = setInterval(fetchActiveSessions, 30000);
    return () => clearInterval(interval);
  }, [fetchActiveSessions, fetchHistoricalSessions]);

  // Session seçildiğinde chat geçmişini çek
  useEffect(() => {
    if (selectedSession) {
      fetchChatHistory(selectedSession.sessionId);
    }
  }, [selectedSession, fetchChatHistory]);

  useEffect(() => {
    if (selectedHistoricalSession) {
      fetchChatHistory(selectedHistoricalSession.sessionId);
    }
  }, [selectedHistoricalSession, fetchChatHistory]);

  const getStatusChip = (status) => {
    const statusMap = {
      'active': { label: 'Aktif', color: 'success', icon: <OnlineIcon /> },
      'passive': { label: 'Pasif', color: 'warning', icon: <OfflineIcon /> },
      'completed': { label: 'Tamamlandı', color: 'info', icon: <CheckCircleIcon /> },
      'abandoned': { label: 'Terk Edildi', color: 'error', icon: <WarningIcon /> }
    };
    
    const statusInfo = statusMap[status] || { label: 'Bilinmiyor', color: 'default' };
    return (
      <Chip
        label={statusInfo.label}
        color={statusInfo.color}
        size="small"
        icon={statusInfo.icon}
      />
    );
  };

  const getOutcomeChip = (outcome) => {
    const outcomeMap = {
      'reservation_made': { label: 'Rezervasyon Yapıldı', color: 'success' },
      'information_provided': { label: 'Bilgi Verildi', color: 'info' },
      'abandoned': { label: 'Terk Edildi', color: 'error' },
      'transferred_to_human': { label: 'İnsana Aktarıldı', color: 'warning' }
    };
    
    const outcomeInfo = outcomeMap[outcome] || { label: 'Bilinmiyor', color: 'default' };
    return (
      <Chip
        label={outcomeInfo.label}
        color={outcomeInfo.color}
        size="small"
        variant="outlined"
      />
    );
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={4}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box display="flex" alignItems="center">
            <Avatar sx={{ bgcolor: 'primary.main', mr: 2, width: 56, height: 56 }}>
              <ChatIcon />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight="600">
                Chat Yönetimi
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Canlı chat oturumları ve geçmiş konuşmaları yönetin
              </Typography>
            </Box>
          </Box>
          
          <Box display="flex" gap={2} alignItems="center">
            {/* Bağlantı Durumu */}
            <Chip
              label={connectionStatus === 'connected' ? 'Bağlı' : 'Bağlantı Kesildi'}
              color={connectionStatus === 'connected' ? 'success' : 'error'}
              icon={connectionStatus === 'connected' ? <OnlineIcon /> : <OfflineIcon />}
            />
            
            {/* Canlı Dinleme */}
            <FormControlLabel
              control={
                <Switch
                  checked={isListening}
                  onChange={toggleLiveListening}
                  color="primary"
                />
              }
              label="Canlı Dinleme"
            />
            
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchActiveSessions}
            >
              Yenile
            </Button>
          </Box>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Tab Navigation */}
      <Paper sx={{ mb: 3 }}>
        <Tabs 
          value={tabValue} 
          onChange={(e, newValue) => setTabValue(newValue)}
          variant="fullWidth"
        >
          <Tab 
            label={
              <Box display="flex" alignItems="center" gap={1}>
                <LiveIcon />
                <span>Canlı Chat Oturumları</span>
                <Badge badgeContent={activeSessions.length} color="primary" />
              </Box>
            } 
          />
          <Tab 
            label={
              <Box display="flex" alignItems="center" gap={1}>
                <HistoryIcon />
                <span>Geçmiş Konuşmalar</span>
                <Badge badgeContent={historicalSessions.length} color="secondary" />
              </Box>
            } 
          />
          <Tab 
            label={
              <Box display="flex" alignItems="center" gap={1}>
                <NotificationIcon />
                <span>Canlı İzleme</span>
                <Badge badgeContent={liveMessages.length} color="error" />
              </Box>
            } 
          />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      {tabValue === 0 && (
        <Grid container spacing={3}>
          {/* Aktif Oturumlar Listesi */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent sx={{ p: 2 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 2 }}>
                  🟢 Aktif Chat Oturumları ({activeSessions.length})
                </Typography>

                <List sx={{ maxHeight: 600, overflow: 'auto' }}>
                  {activeSessions.map((session) => (
                    <ListItem
                      key={session.id}
                      button
                      selected={selectedSession?.id === session.id}
                      onClick={() => setSelectedSession(session)}
                      sx={{
                        mb: 1,
                        borderRadius: 2,
                        border: selectedSession?.id === session.id ? '2px solid' : '1px solid',
                        borderColor: selectedSession?.id === session.id ? 'primary.main' : 'divider'
                      }}
                    >
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: 'primary.main' }}>
                          <PersonIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box display="flex" justifyContent="space-between" alignItems="center">
                            <Typography variant="subtitle2" fontWeight="600">
                              {session.guestName}
                            </Typography>
                            {getStatusChip(session.status)}
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="caption" color="text.secondary">
                              Başlangıç: {session.startDate} {session.startTime}
                            </Typography>
                            <br />
                            <Typography variant="caption" color="text.secondary">
                              Kaynak: {session.source}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}

                  {activeSessions.length === 0 && (
                    <Box textAlign="center" py={4}>
                      <ChatIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                      <Typography variant="body2" color="text.secondary">
                        Şu anda aktif chat oturumu bulunmuyor
                      </Typography>
                    </Box>
                  )}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Chat Detayları */}
          <Grid item xs={12} md={8}>
            {selectedSession ? (
              <Card>
                <CardContent sx={{ p: 0 }}>
                  {/* Chat Header */}
                  <Box p={2} sx={{ borderBottom: '1px solid', borderColor: 'divider' }}>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Box display="flex" alignItems="center">
                        <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                          <PersonIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="h6" fontWeight="600">
                            {selectedSession.guestName}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Session ID: {selectedSession.sessionId.substring(0, 8)}...
                          </Typography>
                        </Box>
                      </Box>
                      <Box display="flex" gap={1}>
                        {getStatusChip(selectedSession.status)}
                        <Chip
                          label={selectedSession.source}
                          size="small"
                          variant="outlined"
                        />
                      </Box>
                    </Box>
                  </Box>

                  {/* Chat Messages */}
                  <Box sx={{ height: 400, overflow: 'auto', p: 2 }}>
                    {chatHistory.map((message, index) => (
                      <Box
                        key={index}
                        display="flex"
                        justifyContent={message.sender === 'USER' ? 'flex-end' : 'flex-start'}
                        mb={2}
                      >
                        <Box
                          sx={{
                            maxWidth: '70%',
                            p: 2,
                            borderRadius: 2,
                            backgroundColor:
                              message.sender === 'USER' ? 'primary.main' :
                              message.sender === 'AI' ? 'grey.100' : 'warning.light',
                            color: message.sender === 'USER' ? 'white' : 'text.primary'
                          }}
                        >
                          <Box display="flex" alignItems="center" gap={1} mb={1}>
                            {message.sender === 'USER' ? <PersonIcon sx={{ fontSize: 16 }} /> :
                             message.sender === 'AI' ? <BotIcon sx={{ fontSize: 16 }} /> :
                             <SettingsIcon sx={{ fontSize: 16 }} />}
                            <Typography variant="caption" fontWeight="600">
                              {message.sender === 'USER' ? 'Misafir' :
                               message.sender === 'AI' ? 'AI Asistan' : 'Yönetici'}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {new Date(message.timestamp).toLocaleTimeString('tr-TR')}
                            </Typography>
                          </Box>
                          <Typography variant="body2">
                            {message.message}
                          </Typography>
                        </Box>
                      </Box>
                    ))}
                    <div ref={messagesEndRef} />
                  </Box>

                  {/* Message Input */}
                  <Box p={2} sx={{ borderTop: '1px solid', borderColor: 'divider' }}>
                    <Box display="flex" gap={1}>
                      <TextField
                        fullWidth
                        placeholder="Misafirle konuşmak için mesaj yazın..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                        size="small"
                      />
                      <IconButton
                        color="primary"
                        onClick={handleSendMessage}
                        disabled={!newMessage.trim()}
                      >
                        <SendIcon />
                      </IconButton>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent sx={{ textAlign: 'center', py: 8 }}>
                  <ChatIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary" mb={1}>
                    Chat Oturumu Seçin
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Detayları görüntülemek için sol taraftan bir chat oturumu seçin
                  </Typography>
                </CardContent>
              </Card>
            )}
          </Grid>
        </Grid>
      )}

      {tabValue === 1 && (
        <Grid container spacing={3}>
          {/* Geçmiş Oturumlar Listesi */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent sx={{ p: 2 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 2 }}>
                  📚 Geçmiş Konuşmalar ({historicalSessions.length})
                </Typography>

                <List sx={{ maxHeight: 600, overflow: 'auto' }}>
                  {historicalSessions.map((session) => (
                    <ListItem
                      key={session.id}
                      button
                      selected={selectedHistoricalSession?.id === session.id}
                      onClick={() => setSelectedHistoricalSession(session)}
                      sx={{
                        mb: 1,
                        borderRadius: 2,
                        border: selectedHistoricalSession?.id === session.id ? '2px solid' : '1px solid',
                        borderColor: selectedHistoricalSession?.id === session.id ? 'secondary.main' : 'divider'
                      }}
                    >
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: 'secondary.main' }}>
                          <HistoryIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box display="flex" justifyContent="space-between" alignItems="center">
                            <Typography variant="subtitle2" fontWeight="600">
                              {session.guestName}
                            </Typography>
                            {getStatusChip(session.status)}
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="caption" color="text.secondary">
                              {session.startDate} {session.startTime} - {session.endTime}
                            </Typography>
                            <br />
                            <Typography variant="caption" color="text.secondary">
                              Süre: {session.duration} • {session.messageCount} mesaj
                            </Typography>
                            <br />
                            {getOutcomeChip(session.outcome)}
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Geçmiş Chat Detayları */}
          <Grid item xs={12} md={8}>
            {selectedHistoricalSession ? (
              <Card>
                <CardContent sx={{ p: 0 }}>
                  {/* Chat Header */}
                  <Box p={2} sx={{ borderBottom: '1px solid', borderColor: 'divider' }}>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Box display="flex" alignItems="center">
                        <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                          <HistoryIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="h6" fontWeight="600">
                            {selectedHistoricalSession.guestName}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {selectedHistoricalSession.startDate} • {selectedHistoricalSession.duration}
                          </Typography>
                        </Box>
                      </Box>
                      <Box display="flex" gap={1}>
                        {getStatusChip(selectedHistoricalSession.status)}
                        {getOutcomeChip(selectedHistoricalSession.outcome)}
                      </Box>
                    </Box>
                  </Box>

                  {/* Chat Messages */}
                  <Box sx={{ height: 500, overflow: 'auto', p: 2 }}>
                    {historicalChatHistory.map((message, index) => (
                      <Box
                        key={index}
                        display="flex"
                        justifyContent={message.sender === 'USER' ? 'flex-end' : 'flex-start'}
                        mb={2}
                      >
                        <Box
                          sx={{
                            maxWidth: '70%',
                            p: 2,
                            borderRadius: 2,
                            backgroundColor:
                              message.sender === 'USER' ? 'secondary.main' :
                              message.sender === 'AI' ? 'grey.100' : 'warning.light',
                            color: message.sender === 'USER' ? 'white' : 'text.primary'
                          }}
                        >
                          <Box display="flex" alignItems="center" gap={1} mb={1}>
                            {message.sender === 'USER' ? <PersonIcon sx={{ fontSize: 16 }} /> :
                             message.sender === 'AI' ? <BotIcon sx={{ fontSize: 16 }} /> :
                             <SettingsIcon sx={{ fontSize: 16 }} />}
                            <Typography variant="caption" fontWeight="600">
                              {message.sender === 'USER' ? 'Misafir' :
                               message.sender === 'AI' ? 'AI Asistan' : 'Yönetici'}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {new Date(message.timestamp).toLocaleTimeString('tr-TR')}
                            </Typography>
                          </Box>
                          <Typography variant="body2">
                            {message.message}
                          </Typography>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent sx={{ textAlign: 'center', py: 8 }}>
                  <HistoryIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary" mb={1}>
                    Geçmiş Konuşma Seçin
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Detayları görüntülemek için sol taraftan bir konuşma seçin
                  </Typography>
                </CardContent>
              </Card>
            )}
          </Grid>
        </Grid>
      )}

      {tabValue === 2 && (
        <Grid container spacing={3}>
          {/* Canlı İzleme Paneli */}
          <Grid item xs={12}>
            <Card>
              <CardContent sx={{ p: 2 }}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6" fontWeight="600">
                    🔴 Canlı Mesaj İzleme
                  </Typography>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Chip
                      label={isListening ? 'Dinleniyor' : 'Durduruldu'}
                      color={isListening ? 'success' : 'default'}
                      icon={isListening ? <OnlineIcon /> : <OfflineIcon />}
                    />
                    <Button
                      variant={isListening ? 'contained' : 'outlined'}
                      color={isListening ? 'error' : 'success'}
                      startIcon={isListening ? <StopIcon /> : <LiveIcon />}
                      onClick={toggleLiveListening}
                    >
                      {isListening ? 'Dinlemeyi Durdur' : 'Canlı Dinlemeyi Başlat'}
                    </Button>
                  </Box>
                </Box>

                {isListening && (
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Canlı dinleme aktif. Tüm chat mesajları gerçek zamanlı olarak burada görünecek.
                  </Alert>
                )}

                <Box sx={{ height: 500, overflow: 'auto', border: '1px solid', borderColor: 'divider', borderRadius: 1, p: 2 }}>
                  {liveMessages.map((message, index) => (
                    <Box
                      key={index}
                      sx={{
                        p: 2,
                        mb: 1,
                        borderRadius: 1,
                        backgroundColor: 'grey.50',
                        borderLeft: '4px solid',
                        borderLeftColor:
                          message.sender === 'USER' ? 'primary.main' :
                          message.sender === 'AI' ? 'success.main' : 'warning.main'
                      }}
                    >
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Box display="flex" alignItems="center" gap={1}>
                          {message.sender === 'USER' ? <PersonIcon sx={{ fontSize: 16 }} /> :
                           message.sender === 'AI' ? <BotIcon sx={{ fontSize: 16 }} /> :
                           <SettingsIcon sx={{ fontSize: 16 }} />}
                          <Typography variant="subtitle2" fontWeight="600">
                            {message.sender === 'USER' ? 'Misafir' :
                             message.sender === 'AI' ? 'AI Asistan' : 'Yönetici'}
                          </Typography>
                          <Chip
                            label={message.session_id.substring(0, 8)}
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                        <Typography variant="caption" color="text.secondary">
                          {message.timestamp}
                        </Typography>
                      </Box>
                      <Typography variant="body2">
                        {message.message}
                      </Typography>
                    </Box>
                  ))}

                  {liveMessages.length === 0 && (
                    <Box textAlign="center" py={8}>
                      <NotificationIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                      <Typography variant="body2" color="text.secondary">
                        {isListening ? 'Canlı mesajlar bekleniyor...' : 'Canlı dinleme kapalı'}
                      </Typography>
                    </Box>
                  )}

                  <div ref={liveMessagesEndRef} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default ChatManagementAdvanced;
