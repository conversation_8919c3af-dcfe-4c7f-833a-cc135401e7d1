import React from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Button,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Chat as ChatIcon,
  Person as PersonIcon,
  SmartToy as BotIcon
} from '@mui/icons-material';
import { useApi } from '../contexts/ApiContext';

const ChatManagement = () => {
  const { getActiveChatSessions, getChatHistory, currentHotelId } = useApi();
  const [activeSessions, setActiveSessions] = React.useState([]);
  const [passiveSessions, setPassiveSessions] = React.useState([]);
  const [recentMessages, setRecentMessages] = React.useState([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);
  const [selectedSession, setSelectedSession] = React.useState(null);

  const fetchChatData = async () => {
    try {
      setLoading(true);
      console.log('Fetching Redis chat sessions for hotel:', currentHotelId);

      // Redis'ten aktif chat oturumlarını çek
      const activeChatsData = await getActiveChatSessions();
      console.log('Active chats from Redis:', activeChatsData);

      // Aktif oturumları formatla
      const formattedActiveSessions = activeChatsData.active_chats?.map(session => ({
        id: session.session_id,
        sessionId: session.session_id,
        guestName: `Misafir (${session.source})`,
        hotelName: session.hotel_name,
        startTime: new Date(session.start_time).toLocaleTimeString('tr-TR'),
        startDate: new Date(session.start_time).toLocaleDateString('tr-TR'),
        source: session.source,
        status: 'active',
        presence: 'active',
        lastMessage: 'Chat devam ediyor...',
        messageCount: 0 // Bu bilgiyi chat history'den alacağız
      })) || [];

      setActiveSessions(formattedActiveSessions);

      // İlk aktif oturum varsa, onun mesajlarını çek
      if (formattedActiveSessions.length > 0) {
        const firstSession = formattedActiveSessions[0];
        await fetchSessionMessages(firstSession.sessionId);
      }

      setError(null);
    } catch (err) {
      console.error('Chat data fetch error:', err);
      setError('Chat verileri yüklenirken bir hata oluştu: ' + err.message);

      // Hata durumunda demo veriler göster
      setActiveSessions([
        {
          id: 'demo_session_001',
          sessionId: 'demo_session_001',
          guestName: 'Demo Kullanıcı',
          hotelName: 'MyPlus Hotel Ataşehir',
          startTime: new Date().toLocaleTimeString('tr-TR'),
          startDate: new Date().toLocaleDateString('tr-TR'),
          source: 'web_widget',
          status: 'active',
          presence: 'active',
          lastMessage: 'Demo chat oturumu',
          messageCount: 5
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const fetchSessionMessages = async (sessionId) => {
    try {
      console.log('Fetching messages for session:', sessionId);
      const historyData = await getChatHistory(sessionId);
      console.log('Chat history:', historyData);

      const messages = historyData.chat_history?.slice(-5).map(msg => ({
        id: msg.id,
        sender: msg.sender.toLowerCase(),
        message: msg.message,
        time: new Date(msg.timestamp).toLocaleTimeString('tr-TR'),
        sessionId: sessionId
      })) || [];

      setRecentMessages(messages);

      // Mesaj sayısını güncelle
      setActiveSessions(prev => prev.map(session =>
        session.sessionId === sessionId
          ? { ...session, messageCount: historyData.chat_history?.length || 0 }
          : session
      ));

    } catch (err) {
      console.error('Session messages fetch error:', err);
    }
  };

  const handleSessionSelect = async (sessionId) => {
    setSelectedSession(sessionId);
    await fetchSessionMessages(sessionId);
  };

  React.useEffect(() => {
    fetchChatData();

    // Auto refresh every 10 seconds for live Redis updates
    const interval = setInterval(fetchChatData, 10000);
    return () => clearInterval(interval);
  }, [currentHotelId]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom fontWeight="bold">
        Chat Yönetimi
      </Typography>
      <Typography variant="body1" color="text.secondary" mb={3}>
        Aktif chat oturumlarını izleyin ve yönetin.
      </Typography>

      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  Redis Chat Oturumları
                </Typography>
                <Chip
                  label={`${activeSessions.length} Aktif`}
                  color="success"
                  variant="outlined"
                />
              </Box>

              {activeSessions.length === 0 ? (
                <Box textAlign="center" py={4}>
                  <Typography variant="body2" color="text.secondary">
                    Şu anda aktif chat oturumu bulunmuyor.
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Redis'ten canlı veriler çekiliyor...
                  </Typography>
                </Box>
              ) : (
                <List>
                  {activeSessions.map((session) => (
                    <ListItem
                      key={session.sessionId}
                      divider
                      sx={{
                        cursor: 'pointer',
                        '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.05)' },
                        backgroundColor: selectedSession === session.sessionId ? 'rgba(33, 150, 243, 0.1)' : 'transparent'
                      }}
                      onClick={() => handleSessionSelect(session.sessionId)}
                    >
                      <ListItemAvatar>
                        <Avatar sx={{
                          bgcolor: session.presence === 'active' ? 'success.main' : 'warning.main'
                        }}>
                          <PersonIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box display="flex" alignItems="center" gap={1}>
                            <Typography variant="subtitle2">
                              {session.guestName}
                            </Typography>
                            <Chip
                              label={session.source}
                              size="small"
                              variant="outlined"
                              sx={{ fontSize: '0.7rem', height: '20px' }}
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              {session.startDate} {session.startTime} • {session.messageCount} mesaj
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Session: {session.sessionId.substring(0, 8)}...
                            </Typography>
                          </Box>
                        }
                      />
                      <Box>
                        <Chip
                          label={session.presence === 'active' ? '🟢 Aktif' : '🟡 Pasif'}
                          color={session.presence === 'active' ? 'success' : 'warning'}
                          size="small"
                          sx={{ mr: 1 }}
                        />
                      </Box>
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {selectedSession ? 'Chat Geçmişi' : 'Mesaj Detayları'}
              </Typography>

              {selectedSession ? (
                <Box>
                  <Typography variant="body2" color="text.secondary" mb={2}>
                    Session: {selectedSession.substring(0, 12)}...
                  </Typography>

                  {recentMessages.length === 0 ? (
                    <Typography variant="body2" color="text.secondary" textAlign="center" py={2}>
                      Bu oturum için mesaj bulunamadı.
                    </Typography>
                  ) : (
                    <List dense>
                      {recentMessages.map((message) => (
                        <ListItem key={message.id} divider sx={{ px: 0 }}>
                          <ListItemAvatar>
                            <Avatar
                              sx={{
                                bgcolor: message.sender === 'ai' ? 'secondary.main' : 'primary.main',
                                width: 32,
                                height: 32
                              }}
                            >
                              {message.sender === 'ai' ? <BotIcon fontSize="small" /> : <PersonIcon fontSize="small" />}
                            </Avatar>
                          </ListItemAvatar>
                          <ListItemText
                            primary={
                              <Typography variant="body2" sx={{ wordBreak: 'break-word' }}>
                                {message.message}
                              </Typography>
                            }
                            secondary={
                              <Typography variant="caption" color="text.secondary">
                                {message.time} • {message.sender === 'ai' ? 'AI Asistan' : 'Misafir'}
                              </Typography>
                            }
                          />
                        </ListItem>
                      ))}
                    </List>
                  )}
                </Box>
              ) : (
                <Box textAlign="center" py={4}>
                  <Typography variant="body2" color="text.secondary">
                    Mesajları görüntülemek için bir chat oturumu seçin.
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ChatManagement;
