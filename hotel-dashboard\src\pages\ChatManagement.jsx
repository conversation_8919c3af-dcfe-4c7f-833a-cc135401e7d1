import React from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Button
} from '@mui/material';
import {
  Chat as ChatIcon,
  Person as PersonIcon,
  SmartToy as BotIcon
} from '@mui/icons-material';

const ChatManagement = () => {
  const [activeSessions, setActiveSessions] = React.useState([
    {
      id: 'session_001',
      guestName: '<PERSON><PERSON> Yılmaz',
      startTime: '14:30',
      messageCount: 12,
      status: 'active',
      lastMessage: 'Rezervasyon yapmak istiyorum'
    },
    {
      id: 'session_002',
      guestName: 'Fatma Kaya',
      startTime: '15:15',
      messageCount: 8,
      status: 'waiting',
      lastMessage: 'Oda fiyatları nedir?'
    },
    {
      id: 'session_003',
      guestName: 'Mehmet Demir',
      startTime: '16:45',
      messageCount: 5,
      status: 'active',
      lastMessage: 'Check-in saati kaçta?'
    }
  ]);

  // Simulate live updates
  React.useEffect(() => {
    const interval = setInterval(() => {
      setActiveSessions(prev => prev.map(session => ({
        ...session,
        messageCount: session.messageCount + Math.floor(Math.random() * 3),
        status: Math.random() > 0.7 ? (session.status === 'active' ? 'waiting' : 'active') : session.status
      })));
    }, 20000);
    return () => clearInterval(interval);
  }, []);

  const recentMessages = [
    {
      id: 1,
      sender: 'user',
      message: 'Merhaba, rezervasyon yapmak istiyorum.',
      time: '15:45',
      sessionId: 'session_001'
    },
    {
      id: 2,
      sender: 'ai',
      message: 'Merhaba! Size yardımcı olmaktan mutluluk duyarım. Hangi tarihler için rezervasyon yapmak istiyorsunuz?',
      time: '15:45',
      sessionId: 'session_001'
    }
  ];

  return (
    <Box>
      <Typography variant="h4" gutterBottom fontWeight="bold">
        Chat Yönetimi
      </Typography>
      <Typography variant="body1" color="text.secondary" mb={3}>
        Aktif chat oturumlarını izleyin ve yönetin.
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Aktif Chat Oturumları
              </Typography>
              <List>
                {activeSessions.map((session) => (
                  <ListItem key={session.id} divider>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        <PersonIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={session.guestName}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            Başlangıç: {session.startTime} • {session.messageCount} mesaj
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Son mesaj: {session.lastMessage}
                          </Typography>
                        </Box>
                      }
                    />
                    <Box>
                      <Chip
                        label={session.status === 'active' ? 'Aktif' : 'Bekliyor'}
                        color={session.status === 'active' ? 'success' : 'warning'}
                        size="small"
                        sx={{ mr: 1 }}
                      />
                      <Button variant="outlined" size="small">
                        Görüntüle
                      </Button>
                    </Box>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Son Mesajlar
              </Typography>
              <List>
                {recentMessages.map((message) => (
                  <ListItem key={message.id} divider>
                    <ListItemAvatar>
                      <Avatar sx={{ 
                        bgcolor: message.sender === 'ai' ? 'secondary.main' : 'primary.main' 
                      }}>
                        {message.sender === 'ai' ? <BotIcon /> : <PersonIcon />}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={message.message}
                      secondary={`${message.time} • ${message.sender === 'ai' ? 'AI Asistan' : 'Misafir'}`}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ChatManagement;
