import React from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Button,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Chat as ChatIcon,
  Person as PersonIcon,
  SmartToy as BotIcon
} from '@mui/icons-material';
import { useApi } from '../contexts/ApiContext';

const ChatManagement = () => {
  const { getChatHistory, startChatSession, currentHotelId } = useApi();
  const [activeSessions, setActiveSessions] = React.useState([]);
  const [recentMessages, setRecentMessages] = React.useState([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);

  const fetchChatData = async () => {
    try {
      setLoading(true);
      console.log('Fetching chat data for hotel:', currentHotelId);

      // For now, we'll simulate active sessions since we need specific endpoints
      // In a real implementation, you'd have endpoints like /get_active_sessions
      setActiveSessions([
        {
          id: 'session_001',
          guestName: 'Aktif Kullanıcı 1',
          startTime: new Date().toLocaleTimeString(),
          messageCount: Math.floor(Math.random() * 20) + 5,
          status: 'active',
          lastMessage: 'Rezervasyon yapmak istiyorum'
        },
        {
          id: 'session_002',
          guestName: 'Aktif Kullanıcı 2',
          startTime: new Date(Date.now() - 30*60*1000).toLocaleTimeString(),
          messageCount: Math.floor(Math.random() * 15) + 3,
          status: 'waiting',
          lastMessage: 'Oda fiyatları nedir?'
        }
      ]);

      // Simulate recent messages
      setRecentMessages([
        {
          id: 1,
          sender: 'user',
          message: 'Merhaba, rezervasyon yapmak istiyorum.',
          time: new Date().toLocaleTimeString(),
          sessionId: 'session_001'
        },
        {
          id: 2,
          sender: 'ai',
          message: 'Merhaba! Size yardımcı olmaktan mutluluk duyarım. Hangi tarihler için rezervasyon yapmak istiyorsunuz?',
          time: new Date().toLocaleTimeString(),
          sessionId: 'session_001'
        }
      ]);

      setError(null);
    } catch (err) {
      console.error('Chat data fetch error:', err);
      setError('Chat verileri yüklenirken bir hata oluştu: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    fetchChatData();

    // Auto refresh every 15 seconds for live updates
    const interval = setInterval(fetchChatData, 15000);
    return () => clearInterval(interval);
  }, [currentHotelId]);

  const recentMessages = [
    {
      id: 1,
      sender: 'user',
      message: 'Merhaba, rezervasyon yapmak istiyorum.',
      time: '15:45',
      sessionId: 'session_001'
    },
    {
      id: 2,
      sender: 'ai',
      message: 'Merhaba! Size yardımcı olmaktan mutluluk duyarım. Hangi tarihler için rezervasyon yapmak istiyorsunuz?',
      time: '15:45',
      sessionId: 'session_001'
    }
  ];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom fontWeight="bold">
        Chat Yönetimi
      </Typography>
      <Typography variant="body1" color="text.secondary" mb={3}>
        Aktif chat oturumlarını izleyin ve yönetin.
      </Typography>

      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Aktif Chat Oturumları
              </Typography>
              <List>
                {activeSessions.map((session) => (
                  <ListItem key={session.id} divider>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        <PersonIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={session.guestName}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            Başlangıç: {session.startTime} • {session.messageCount} mesaj
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Son mesaj: {session.lastMessage}
                          </Typography>
                        </Box>
                      }
                    />
                    <Box>
                      <Chip
                        label={session.status === 'active' ? 'Aktif' : 'Bekliyor'}
                        color={session.status === 'active' ? 'success' : 'warning'}
                        size="small"
                        sx={{ mr: 1 }}
                      />
                      <Button variant="outlined" size="small">
                        Görüntüle
                      </Button>
                    </Box>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Son Mesajlar
              </Typography>
              <List>
                {recentMessages.map((message) => (
                  <ListItem key={message.id} divider>
                    <ListItemAvatar>
                      <Avatar sx={{ 
                        bgcolor: message.sender === 'ai' ? 'secondary.main' : 'primary.main' 
                      }}>
                        {message.sender === 'ai' ? <BotIcon /> : <PersonIcon />}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={message.message}
                      secondary={`${message.time} • ${message.sender === 'ai' ? 'AI Asistan' : 'Misafir'}`}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ChatManagement;
