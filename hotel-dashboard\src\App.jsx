import React from 'react';
import { Box, Typography, Button } from '@mui/material';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

// Simple dark theme for testing
const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#2196f3',
    },
    background: {
      default: '#0a0e27',
      paper: '#1a1d3a',
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box
        sx={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #0a0e27 0%, #1a1d3a 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column',
          gap: 3
        }}
      >
        <Typography variant="h3" color="primary" gutterBottom>
          🏨 Otel Dashboard
        </Typography>
        <Typography variant="h6" color="text.secondary" textAlign="center">
          Modern frontend dashboard başarıyla çalışıyor!
        </Typography>
        <Button
          variant="contained"
          size="large"
          onClick={() => window.location.reload()}
        >
          Test Başarılı ✅
        </Button>
      </Box>
    </ThemeProvider>
  );
}

export default App;
