import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { AuthProvider } from './contexts/AuthContext';
import { ApiProvider } from './contexts/ApiContext';
import LoginPage from './pages/LoginPage';
import DashboardLayout from './components/Layout/DashboardLayoutClean';
import Dashboard from './pages/DashboardAI';
import Reservations from './pages/Reservations';
import ChatManagement from './pages/ChatManagementNew';
import Analytics from './pages/AnalyticsAdvanced';
import Integrations from './pages/IntegrationsModern';
import HotelSettings from './pages/HotelConfigFull';
import StrategyRoom from './pages/StrategyRoom';
import ProtectedRoute from './components/Auth/ProtectedRoute';

// Yeni sayfalar - şimdilik placeholder
const AIAnalysis = () => (
  <div style={{ padding: '20px' }}>
    <h2>🤖 AI Analiz</h2>
    <p>Yapay zeka analiz raporları burada görünecek...</p>
  </div>
);

const Performance = () => (
  <div style={{ padding: '20px' }}>
    <h2>📊 Performans Metrikleri</h2>
    <p>Detaylı performans analizi burada görünecek...</p>
  </div>
);

const Revenue = () => (
  <div style={{ padding: '20px' }}>
    <h2>💰 Gelir Analizi</h2>
    <p>Gelir ve karlılık analizi burada görünecek...</p>
  </div>
);

const Guests = () => (
  <div style={{ padding: '20px' }}>
    <h2>👥 Misafir Profili</h2>
    <p>Misafir analizi ve segmentasyon burada görünecek...</p>
  </div>
);

const Reviews = () => (
  <div style={{ padding: '20px' }}>
    <h2>⭐ Yorumlar</h2>
    <p>Google ve Facebook yorumları burada görünecek...</p>
  </div>
);

const Security = () => (
  <div style={{ padding: '20px' }}>
    <h2>🔒 Güvenlik</h2>
    <p>Sistem güvenliği ve loglar burada görünecek...</p>
  </div>
);

// Modern dark theme
const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#2196f3',
      light: '#64b5f6',
      dark: '#1976d2',
    },
    secondary: {
      main: '#f50057',
      light: '#ff5983',
      dark: '#c51162',
    },
    background: {
      default: '#0a0e27',
      paper: '#1a1d3a',
    },
    text: {
      primary: '#ffffff',
      secondary: '#b0bec5',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 600,
    },
    h5: {
      fontWeight: 600,
    },
    h6: {
      fontWeight: 600,
    },
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          backgroundImage: 'linear-gradient(135deg, #1a1d3a 0%, #2a2d5a 100%)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundImage: 'linear-gradient(135deg, #1a1d3a 0%, #2a2d5a 100%)',
          backdropFilter: 'blur(10px)',
        },
      },
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <ApiProvider>
          <Router>
            <Routes>
              <Route path="/login" element={<LoginPage />} />
              <Route path="/" element={
                <ProtectedRoute>
                  <DashboardLayout />
                </ProtectedRoute>
              }>
                <Route index element={<Dashboard />} />
                <Route path="reservations" element={<Reservations />} />
                <Route path="chat" element={<ChatManagement />} />
                <Route path="analytics" element={<Analytics />} />
                <Route path="integrations" element={<Integrations />} />
                <Route path="ai-analysis" element={<AIAnalysis />} />
                <Route path="performance" element={<Performance />} />
                <Route path="revenue" element={<Revenue />} />
                <Route path="guests" element={<Guests />} />
                <Route path="reviews" element={<Reviews />} />
                <Route path="security" element={<Security />} />
                <Route path="hotel-settings" element={<HotelSettings />} />
                <Route path="strategy-room" element={<StrategyRoom />} />
              </Route>
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </Router>
        </ApiProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
