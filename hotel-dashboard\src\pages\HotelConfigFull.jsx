import { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Alert,
  Chip,
  Avatar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  InputAdornment,
  Switch,
  FormControlLabel,
  Stepper,
  Step,
  StepLabel,
  Paper
} from '@mui/material';
import {
  Hotel as HotelIcon,
  Save as SaveIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Room as RoomIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  Business as BusinessIcon,
  AccessTime as TimeIcon,
  AttachMoney as MoneyIcon,
  People as PeopleIcon,
  Straighten as SizeIcon,
  Bed as BedIcon,
  Landscape as ViewIcon,
  ExpandMore as ExpandMoreIcon,
  SmartToy as AIIcon,
  Map as MapIcon,
  Payment as PaymentIcon,
  Restaurant as RestaurantIcon,
  Wifi as WifiIcon,
  Pool as PoolIcon,
  FitnessCenter as FitnessIcon,
  Spa as SpaIcon,
  LocalParking as ParkingIcon,
  RoomService as RoomServiceIcon,
  Business as ConferenceIcon,
  Pets as PetsIcon,
  AirportShuttle as ShuttleIcon,
  Security as SecurityIcon,
  Balcony as BalconyIcon
} from '@mui/icons-material';
import { useApi } from '../contexts/ApiContext';
import { useAuth } from '../contexts/AuthContext';

const HotelConfigFull = () => {
  const { getHotelConfig, updateHotelConfig, currentHotelId } = useApi();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [roomDialogOpen, setRoomDialogOpen] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  
  // Unity AsistanConfiguration'a uygun form state
  const [formData, setFormData] = useState({
    // Sistem mesajı (AI için)
    systemMessage: '',
    
    // Temel otel bilgileri
    hotelName: '',
    hotelCity: '',
    hotelId: '',
    hotelDescription: '',
    hotelAddress: '',
    hotelPhone: '',
    hotelEmail: '',
    hotelCheckInTime: '14:00',
    hotelCheckOutTime: '12:00',
    
    // AI ayarları
    aiResponseDelaySeconds: 2.5,
    
    // Otel politikaları ve bilgileri
    hotelPolicy: '',
    hotelAmenities: '',
    paymentMethods: '',
    nearbyAttractions: '',
    contactHours: '24/7',
    
    // Konum bilgileri
    hotelLatitude: '',
    hotelLongitude: '',
    placeId: '',
    
    // Google My Business entegrasyonu
    gmbAccountId: '',
    gmbLocationId: '',
    
    // Oda tipleri (RoomTypeData array)
    roomTypes: []
  });

  // Yeni oda için state (RoomTypeData yapısına uygun)
  const [newRoom, setNewRoom] = useState({
    name: '',
    capacity: '2',
    price: '800',
    features: '',
    totalRoomsOfType: 10,
    bedType: 'Çift Kişilik',
    viewType: 'Şehir Manzaralı',
    sizeSquareMeters: 25.0,
    roomAmenities: 'Klima, TV, Wi-Fi',
    minStayNights: 1,
    maxGuests: 2,
    imageUrls: []
  });

  // Seçenekler
  const amenitiesOptions = [
    { label: 'WiFi', icon: <WifiIcon /> },
    { label: 'Spa', icon: <SpaIcon /> },
    { label: 'Fitness', icon: <FitnessIcon /> },
    { label: 'Restaurant', icon: <RestaurantIcon /> },
    { label: 'Bar', icon: <RestaurantIcon /> },
    { label: 'Parking', icon: <ParkingIcon /> },
    { label: 'Pool', icon: <PoolIcon /> },
    { label: 'Concierge', icon: <BusinessIcon /> },
    { label: 'Room Service', icon: <RoomServiceIcon /> },
    { label: 'Laundry', icon: <RoomServiceIcon /> },
    { label: 'Airport Shuttle', icon: <ShuttleIcon /> },
    { label: 'Pet Friendly', icon: <PetsIcon /> },
    { label: 'Conference Room', icon: <ConferenceIcon /> },
    { label: 'Business Center', icon: <BusinessIcon /> },
    { label: 'Sauna', icon: <SpaIcon /> },
    { label: 'Jacuzzi', icon: <SpaIcon /> },
    { label: 'Garden', icon: <BalconyIcon /> },
    { label: 'Terrace', icon: <BalconyIcon /> },
    { label: 'Security', icon: <SecurityIcon /> }
  ];

  const paymentOptions = [
    { label: 'Nakit', icon: <MoneyIcon /> },
    { label: 'Kredi Kartı', icon: <PaymentIcon /> },
    { label: 'Havale', icon: <PaymentIcon /> },
    { label: 'PayPal', icon: <PaymentIcon /> },
    { label: 'Kripto Para', icon: <PaymentIcon /> },
    { label: 'Taksit', icon: <PaymentIcon /> }
  ];

  const bedTypes = ['Tek Kişilik', 'Çift Kişilik', 'King Size', 'Queen Size', 'Ranza', 'Yatak Odası + Salon'];
  const viewTypes = ['Şehir Manzaralı', 'Deniz Manzaralı', 'Dağ Manzaralı', 'Bahçe Manzaralı', 'Avlu Manzaralı', 'Manzarasız'];

  const steps = [
    'Temel Bilgiler',
    'Otel Özellikleri', 
    'AI & Sistem Ayarları',
    'Oda Tipleri',
    'Konum & Entegrasyon'
  ];

  useEffect(() => {
    fetchHotelConfig();
  }, [currentHotelId]);

  const fetchHotelConfig = async () => {
    try {
      setLoading(true);
      const config = await getHotelConfig(currentHotelId);
      
      setFormData({
        systemMessage: config.systemMessage || '',
        hotelName: config.hotelName || '',
        hotelCity: config.hotelCity || '',
        hotelId: config.hotelId || currentHotelId,
        hotelDescription: config.hotelDescription || '',
        hotelAddress: config.hotelAddress || '',
        hotelPhone: config.hotelPhone || '',
        hotelEmail: config.hotelEmail || '',
        hotelCheckInTime: config.hotelCheckInTime || '14:00',
        hotelCheckOutTime: config.hotelCheckOutTime || '12:00',
        aiResponseDelaySeconds: config.aiResponseDelaySeconds || 2.5,
        hotelPolicy: config.hotelPolicy || '',
        hotelAmenities: config.hotelAmenities || '',
        paymentMethods: config.paymentMethods || '',
        nearbyAttractions: config.nearbyAttractions || '',
        contactHours: config.contactHours || '24/7',
        hotelLatitude: config.hotelLatitude || '',
        hotelLongitude: config.hotelLongitude || '',
        placeId: config.placeId || '',
        gmbAccountId: config.gmbAccountId || '',
        gmbLocationId: config.gmbLocationId || '',
        roomTypes: config.roomTypes || []
      });
    } catch (err) {
      console.error('Hotel config fetch error:', err);
      setError('Otel konfigürasyonu yüklenirken hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAmenityToggle = (amenity) => {
    const currentAmenities = formData.hotelAmenities.split(', ').filter(a => a.trim());
    const updatedAmenities = currentAmenities.includes(amenity)
      ? currentAmenities.filter(a => a !== amenity)
      : [...currentAmenities, amenity];
    
    handleInputChange('hotelAmenities', updatedAmenities.join(', '));
  };

  const handlePaymentToggle = (method) => {
    const currentMethods = formData.paymentMethods.split(', ').filter(m => m.trim());
    const updatedMethods = currentMethods.includes(method)
      ? currentMethods.filter(m => m !== method)
      : [...currentMethods, method];
    
    handleInputChange('paymentMethods', updatedMethods.join(', '));
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);
      
      await updateHotelConfig(formData);
      setSuccess('Otel konfigürasyonu başarıyla güncellendi!');
      
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Hotel config update error:', err);
      setError('Otel konfigürasyonu güncellenirken hata oluştu.');
    } finally {
      setSaving(false);
    }
  };

  const handleAddRoom = () => {
    if (newRoom.name && newRoom.totalRoomsOfType > 0) {
      setFormData(prev => ({
        ...prev,
        roomTypes: [...prev.roomTypes, { ...newRoom }]
      }));
      setNewRoom({
        name: '',
        capacity: '2',
        price: '800',
        features: '',
        totalRoomsOfType: 10,
        bedType: 'Çift Kişilik',
        viewType: 'Şehir Manzaralı',
        sizeSquareMeters: 25.0,
        roomAmenities: 'Klima, TV, Wi-Fi',
        minStayNights: 1,
        maxGuests: 2,
        imageUrls: []
      });
      setRoomDialogOpen(false);
    }
  };

  const handleDeleteRoom = (index) => {
    setFormData(prev => ({
      ...prev,
      roomTypes: prev.roomTypes.filter((_, i) => i !== index)
    }));
  };

  const handleRoomInputChange = (field, value) => {
    setNewRoom(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Otel konfigürasyonu yükleniyor...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={4}>
        <Box display="flex" alignItems="center" mb={2}>
          <Avatar sx={{ bgcolor: 'primary.main', mr: 2, width: 56, height: 56 }}>
            <HotelIcon />
          </Avatar>
          <Box>
            <Typography variant="h4" fontWeight="600">
              Otel Konfigürasyonu
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Unity AsistanConfiguration uyumlu tam konfigürasyon
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Alerts */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      {/* Progress Stepper */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Stepper activeStep={activeStep} alternativeLabel>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
        
        <Box mt={3} display="flex" justifyContent="center" gap={2}>
          {steps.map((step, index) => (
            <Button
              key={step}
              variant={activeStep === index ? 'contained' : 'outlined'}
              onClick={() => setActiveStep(index)}
              size="small"
            >
              {step}
            </Button>
          ))}
        </Box>
      </Paper>

      {/* Step Content */}
      {activeStep === 0 && (
        <Grid container spacing={3}>
          {/* Temel Bilgiler */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  🏨 Temel Otel Bilgileri
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Otel Adı"
                      value={formData.hotelName}
                      onChange={(e) => handleInputChange('hotelName', e.target.value)}
                      InputProps={{
                        startAdornment: <BusinessIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>

                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Şehir"
                      value={formData.hotelCity}
                      onChange={(e) => handleInputChange('hotelCity', e.target.value)}
                      InputProps={{
                        startAdornment: <LocationIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>

                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Otel ID"
                      value={formData.hotelId}
                      onChange={(e) => handleInputChange('hotelId', e.target.value)}
                      disabled
                      helperText="Sistem tarafından otomatik atanır"
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Otel Açıklaması"
                      multiline
                      rows={3}
                      value={formData.hotelDescription}
                      onChange={(e) => handleInputChange('hotelDescription', e.target.value)}
                      placeholder="Otel hakkında detaylı açıklama..."
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Adres"
                      multiline
                      rows={2}
                      value={formData.hotelAddress}
                      onChange={(e) => handleInputChange('hotelAddress', e.target.value)}
                      InputProps={{
                        startAdornment: <LocationIcon sx={{ mr: 1, color: 'text.secondary', alignSelf: 'flex-start', mt: 1 }} />
                      }}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* İletişim Bilgileri */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  📞 İletişim Bilgileri
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Telefon"
                      value={formData.hotelPhone}
                      onChange={(e) => handleInputChange('hotelPhone', e.target.value)}
                      InputProps={{
                        startAdornment: <PhoneIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Email"
                      type="email"
                      value={formData.hotelEmail}
                      onChange={(e) => handleInputChange('hotelEmail', e.target.value)}
                      InputProps={{
                        startAdornment: <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>

                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Check-in Saati"
                      type="time"
                      value={formData.hotelCheckInTime}
                      onChange={(e) => handleInputChange('hotelCheckInTime', e.target.value)}
                      InputProps={{
                        startAdornment: <TimeIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>

                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Check-out Saati"
                      type="time"
                      value={formData.hotelCheckOutTime}
                      onChange={(e) => handleInputChange('hotelCheckOutTime', e.target.value)}
                      InputProps={{
                        startAdornment: <TimeIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="İletişim Saatleri"
                      value={formData.contactHours}
                      onChange={(e) => handleInputChange('contactHours', e.target.value)}
                      placeholder="Örn: 24/7, 09:00-22:00"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {activeStep === 1 && (
        <Grid container spacing={3}>
          {/* Otel Olanakları */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  🏊 Otel Olanakları
                </Typography>

                <Box display="flex" flexWrap="wrap" gap={1} mb={3}>
                  {amenitiesOptions.map((amenity) => (
                    <Chip
                      key={amenity.label}
                      icon={amenity.icon}
                      label={amenity.label}
                      onClick={() => handleAmenityToggle(amenity.label)}
                      color={formData.hotelAmenities.includes(amenity.label) ? 'primary' : 'default'}
                      variant={formData.hotelAmenities.includes(amenity.label) ? 'filled' : 'outlined'}
                      sx={{ cursor: 'pointer', m: 0.5 }}
                    />
                  ))}
                </Box>

                <TextField
                  fullWidth
                  label="Olanaklar (Virgülle Ayrılmış)"
                  value={formData.hotelAmenities}
                  onChange={(e) => handleInputChange('hotelAmenities', e.target.value)}
                  multiline
                  rows={3}
                  helperText="Yukarıdaki chip'lere tıklayarak veya manuel olarak ekleyebilirsiniz"
                />
              </CardContent>
            </Card>
          </Grid>

          {/* Ödeme Yöntemleri */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  💳 Ödeme Yöntemleri
                </Typography>

                <Box display="flex" flexWrap="wrap" gap={1} mb={3}>
                  {paymentOptions.map((payment) => (
                    <Chip
                      key={payment.label}
                      icon={payment.icon}
                      label={payment.label}
                      onClick={() => handlePaymentToggle(payment.label)}
                      color={formData.paymentMethods.includes(payment.label) ? 'secondary' : 'default'}
                      variant={formData.paymentMethods.includes(payment.label) ? 'filled' : 'outlined'}
                      sx={{ cursor: 'pointer', m: 0.5 }}
                    />
                  ))}
                </Box>

                <TextField
                  fullWidth
                  label="Ödeme Yöntemleri"
                  value={formData.paymentMethods}
                  onChange={(e) => handleInputChange('paymentMethods', e.target.value)}
                  multiline
                  rows={2}
                />
              </CardContent>
            </Card>
          </Grid>

          {/* Politikalar ve Yakın Yerler */}
          <Grid item xs={12}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  📋 Otel Politikaları ve Yakın Yerler
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Otel Politikası"
                      multiline
                      rows={4}
                      value={formData.hotelPolicy}
                      onChange={(e) => handleInputChange('hotelPolicy', e.target.value)}
                      placeholder="Otel kuralları, politikaları ve önemli bilgiler..."
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Yakın Yerler ve Cazibe Merkezleri"
                      multiline
                      rows={4}
                      value={formData.nearbyAttractions}
                      onChange={(e) => handleInputChange('nearbyAttractions', e.target.value)}
                      placeholder="Zorlu Center, Vadistanbul, Optimum Outlet..."
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {activeStep === 2 && (
        <Grid container spacing={3}>
          {/* AI Ayarları */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  🤖 AI Asistan Ayarları
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" sx={{ mb: 2 }}>
                      AI Yanıt Gecikmesi: {formData.aiResponseDelaySeconds} saniye
                    </Typography>
                    <Slider
                      value={formData.aiResponseDelaySeconds}
                      onChange={(e, value) => handleInputChange('aiResponseDelaySeconds', value)}
                      min={0.5}
                      max={10}
                      step={0.5}
                      marks={[
                        { value: 0.5, label: '0.5s' },
                        { value: 2.5, label: '2.5s' },
                        { value: 5, label: '5s' },
                        { value: 10, label: '10s' }
                      ]}
                      valueLabelDisplay="on"
                      sx={{ mb: 2 }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      AI'nın yanıt vermeden önce bekleyeceği süre (saniye)
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Sistem Mesajı */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  💬 AI Sistem Mesajı
                </Typography>

                <TextField
                  fullWidth
                  label="Sistem Mesajı"
                  multiline
                  rows={8}
                  value={formData.systemMessage}
                  onChange={(e) => handleInputChange('systemMessage', e.target.value)}
                  placeholder="AI asistanın davranışını belirleyen sistem mesajı..."
                  helperText="Bu mesaj AI'nın nasıl davranacağını belirler"
                />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {activeStep === 3 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                  <Typography variant="h6" fontWeight="600">
                    🛏️ Oda Tipleri Yönetimi
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => setRoomDialogOpen(true)}
                    sx={{
                      background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #388e3c 0%, #2e7d32 100%)',
                      }
                    }}
                  >
                    Yeni Oda Tipi Ekle
                  </Button>
                </Box>

                <Grid container spacing={2}>
                  {formData.roomTypes.map((room, index) => (
                    <Grid item xs={12} md={6} lg={4} key={index}>
                      <Card variant="outlined" sx={{ height: '100%' }}>
                        <CardContent sx={{ p: 2 }}>
                          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                            <Typography variant="h6" fontWeight="600" color="primary">
                              {room.name}
                            </Typography>
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleDeleteRoom(index)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Box>

                          <Grid container spacing={1}>
                            <Grid item xs={6}>
                              <Box>
                                <Typography variant="caption" color="text.secondary">Kapasite</Typography>
                                <Typography variant="body2" fontWeight="600">{room.capacity} kişi</Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={6}>
                              <Box>
                                <Typography variant="caption" color="text.secondary">Fiyat</Typography>
                                <Typography variant="body2" fontWeight="600" color="success.main">₺{room.price}/gece</Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={6}>
                              <Box>
                                <Typography variant="caption" color="text.secondary">Oda Sayısı</Typography>
                                <Typography variant="body2" fontWeight="600">{room.totalRoomsOfType} oda</Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={6}>
                              <Box>
                                <Typography variant="caption" color="text.secondary">Boyut</Typography>
                                <Typography variant="body2" fontWeight="600">{room.sizeSquareMeters} m²</Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={12}>
                              <Box>
                                <Typography variant="caption" color="text.secondary">Yatak Tipi</Typography>
                                <Typography variant="body2">{room.bedType}</Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={12}>
                              <Box>
                                <Typography variant="caption" color="text.secondary">Manzara</Typography>
                                <Typography variant="body2">{room.viewType}</Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={6}>
                              <Box>
                                <Typography variant="caption" color="text.secondary">Min. Gece</Typography>
                                <Typography variant="body2">{room.minStayNights}</Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={6}>
                              <Box>
                                <Typography variant="caption" color="text.secondary">Max. Kişi</Typography>
                                <Typography variant="body2">{room.maxGuests}</Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={12}>
                              <Box>
                                <Typography variant="caption" color="text.secondary">Olanaklar</Typography>
                                <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
                                  {room.roomAmenities}
                                </Typography>
                              </Box>
                            </Grid>
                            {room.features && (
                              <Grid item xs={12}>
                                <Box>
                                  <Typography variant="caption" color="text.secondary">Özellikler</Typography>
                                  <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
                                    {room.features}
                                  </Typography>
                                </Box>
                              </Grid>
                            )}
                          </Grid>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}

                  {formData.roomTypes.length === 0 && (
                    <Grid item xs={12}>
                      <Box textAlign="center" py={6}>
                        <RoomIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                        <Typography variant="h6" color="text.secondary" mb={1}>
                          Henüz oda tipi eklenmemiş
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Yeni oda tipi eklemek için yukarıdaki butona tıklayın
                        </Typography>
                      </Box>
                    </Grid>
                  )}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {activeStep === 4 && (
        <Grid container spacing={3}>
          {/* Konum Bilgileri */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  📍 Konum Bilgileri
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Enlem (Latitude)"
                      value={formData.hotelLatitude}
                      onChange={(e) => handleInputChange('hotelLatitude', e.target.value)}
                      placeholder="41.0082"
                      InputProps={{
                        startAdornment: <MapIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>

                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Boylam (Longitude)"
                      value={formData.hotelLongitude}
                      onChange={(e) => handleInputChange('hotelLongitude', e.target.value)}
                      placeholder="28.9784"
                      InputProps={{
                        startAdornment: <MapIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Google Places ID"
                      value={formData.placeId}
                      onChange={(e) => handleInputChange('placeId', e.target.value)}
                      placeholder="ChIJVVVVVVVVVVVVVVVVVVVVVV"
                      helperText="Google Places API'den alınan benzersiz ID"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Google My Business Entegrasyonu */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  🔗 Google My Business Entegrasyonu
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="GMB Account ID"
                      value={formData.gmbAccountId}
                      onChange={(e) => handleInputChange('gmbAccountId', e.target.value)}
                      placeholder="gmb_account_123"
                      helperText="Google My Business hesap ID'si"
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="GMB Location ID"
                      value={formData.gmbLocationId}
                      onChange={(e) => handleInputChange('gmbLocationId', e.target.value)}
                      placeholder="gmb_location_456"
                      helperText="Google My Business konum ID'si"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Kaydet Butonu */}
      <Box mt={4} textAlign="center">
        <Button
          variant="contained"
          size="large"
          startIcon={<SaveIcon />}
          onClick={handleSave}
          disabled={saving}
          sx={{
            px: 6,
            py: 2,
            fontSize: '1.1rem',
            background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #388e3c 0%, #2e7d32 100%)',
            },
            '&:disabled': {
              background: 'rgba(0, 0, 0, 0.12)',
            }
          }}
        >
          {saving ? 'Kaydediliyor...' : 'Otel Konfigürasyonunu Kaydet'}
        </Button>
      </Box>

      {/* Oda Ekleme Dialog'u */}
      <Dialog open={roomDialogOpen} onClose={() => setRoomDialogOpen(false)} maxWidth="lg" fullWidth>
        <DialogTitle>
          <Box display="flex" alignItems="center">
            <RoomIcon sx={{ mr: 1 }} />
            Yeni Oda Tipi Ekle
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            {/* Temel Bilgiler */}
            <Grid item xs={12}>
              <Typography variant="h6" color="primary" sx={{ mb: 2 }}>
                Temel Bilgiler
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Oda Tipi Adı"
                value={newRoom.name}
                onChange={(e) => handleRoomInputChange('name', e.target.value)}
                placeholder="Örn: Standart Oda, Deluxe Oda, Suite"
                InputProps={{
                  startAdornment: <RoomIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>

            <Grid item xs={6} md={3}>
              <TextField
                fullWidth
                label="Kapasite"
                value={newRoom.capacity}
                onChange={(e) => handleRoomInputChange('capacity', e.target.value)}
                InputProps={{
                  startAdornment: <PeopleIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>

            <Grid item xs={6} md={3}>
              <TextField
                fullWidth
                label="Gecelik Fiyat (₺)"
                value={newRoom.price}
                onChange={(e) => handleRoomInputChange('price', e.target.value)}
                InputProps={{
                  startAdornment: <MoneyIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>

            {/* Oda Detayları */}
            <Grid item xs={12}>
              <Typography variant="h6" color="primary" sx={{ mb: 2, mt: 2 }}>
                Oda Detayları
              </Typography>
            </Grid>

            <Grid item xs={6} md={3}>
              <TextField
                fullWidth
                label="Oda Sayısı"
                type="number"
                value={newRoom.totalRoomsOfType}
                onChange={(e) => handleRoomInputChange('totalRoomsOfType', parseInt(e.target.value) || 0)}
              />
            </Grid>

            <Grid item xs={6} md={3}>
              <TextField
                fullWidth
                label="Boyut (m²)"
                type="number"
                value={newRoom.sizeSquareMeters}
                onChange={(e) => handleRoomInputChange('sizeSquareMeters', parseFloat(e.target.value) || 0)}
                InputProps={{
                  startAdornment: <SizeIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>

            <Grid item xs={6} md={3}>
              <TextField
                fullWidth
                label="Min. Konaklama (Gece)"
                type="number"
                value={newRoom.minStayNights}
                onChange={(e) => handleRoomInputChange('minStayNights', parseInt(e.target.value) || 1)}
              />
            </Grid>

            <Grid item xs={6} md={3}>
              <TextField
                fullWidth
                label="Max. Misafir"
                type="number"
                value={newRoom.maxGuests}
                onChange={(e) => handleRoomInputChange('maxGuests', parseInt(e.target.value) || 2)}
              />
            </Grid>

            <Grid item xs={6} md={6}>
              <FormControl fullWidth>
                <InputLabel>Yatak Tipi</InputLabel>
                <Select
                  value={newRoom.bedType}
                  onChange={(e) => handleRoomInputChange('bedType', e.target.value)}
                  startAdornment={<BedIcon sx={{ mr: 1, color: 'text.secondary' }} />}
                >
                  {bedTypes.map(type => (
                    <MenuItem key={type} value={type}>{type}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={6} md={6}>
              <FormControl fullWidth>
                <InputLabel>Manzara Tipi</InputLabel>
                <Select
                  value={newRoom.viewType}
                  onChange={(e) => handleRoomInputChange('viewType', e.target.value)}
                  startAdornment={<ViewIcon sx={{ mr: 1, color: 'text.secondary' }} />}
                >
                  {viewTypes.map(type => (
                    <MenuItem key={type} value={type}>{type}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Açıklamalar */}
            <Grid item xs={12}>
              <Typography variant="h6" color="primary" sx={{ mb: 2, mt: 2 }}>
                Açıklamalar
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Oda Özellikleri"
                value={newRoom.features}
                onChange={(e) => handleRoomInputChange('features', e.target.value)}
                placeholder="Oda hakkında genel açıklama..."
                multiline
                rows={3}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Oda Olanakları"
                value={newRoom.roomAmenities}
                onChange={(e) => handleRoomInputChange('roomAmenities', e.target.value)}
                placeholder="Klima, TV, Wi-Fi, Minibar..."
                multiline
                rows={3}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={() => setRoomDialogOpen(false)} size="large">
            İptal
          </Button>
          <Button
            onClick={handleAddRoom}
            variant="contained"
            size="large"
            disabled={!newRoom.name || newRoom.totalRoomsOfType <= 0}
            sx={{
              background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #388e3c 0%, #2e7d32 100%)',
              }
            }}
          >
            Oda Tipini Ekle
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default HotelConfigFull;
