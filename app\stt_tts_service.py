# // Dosya Adı: stt_tts_service.py
# // Konum: app/
# // --- SSML Zekası Eklenmiş, <PERSON> ve <PERSON>üncellenmiş Kod ---

from google.cloud import speech
from google.cloud import texttospeech_v1beta1 as texttospeech
import os
import traceback
import re
import emoji 

PHONETIC_REPLACEMENTS = {
    "misafirlerimize": "misaafirlerimize",
    "misafir": "misaafir",
    "teşekkürler": "teşekkürleer",
    "deluxe": "delüks",
    # Örnek: "STT" kelimesini "es ti ti" olarak okumasını sağlamak için
    # "STT": "es ti ti"
}

# ... (Google istemcilerini başlatan kod) ...
speech_client = None
tts_client = None
try:
    # ... (mevcut kodunuz)
    creds_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
    if creds_path and os.path.exists(creds_path):
        speech_client = speech.SpeechClient()
        tts_client = texttospeech.TextToSpeechClient()
        print("INFO: Google STT/TTS servisleri başarıyla başlatıldı.")
    else:
        print("UYARI: Google STT/TTS servisleri için 'GOOGLE_APPLICATION_CREDENTIALS' ortam değişkeni bulunamadı veya dosya yolu geçersiz. Bu servisler çalışmayacak.")
except Exception as e:
    print(f"KRİTİK HATA: Google STT/TTS istemcileri başlatılamadı: {e}")

def _apply_phonetic_corrections(text: str) -> str:
    """
    Verilen metindeki kelimeleri, daha iyi bir telaffuz için
    PHONETIC_REPLACEMENTS sözlüğüne göre değiştirir.
    """
    # Büyük/küçük harf duyarsız bir değiştirme yapmak için
    for word_to_find, replacement in PHONETIC_REPLACEMENTS.items():
        # \b kelime sınırlarını belirtir, böylece "misafir" kelimesi değişmez.
        pattern = re.compile(r'\b' + re.escape(word_to_find) + r'\b', re.IGNORECASE)
        text = pattern.sub(replacement, text)
    return text    

def _clean_text_for_speech(text: str) -> str:
    """
    Metni sese dönüştürmeden önce konuşulmayacak karakterlerden arındırır.
    """
    if not text:
        return ""
    
    # 1. Emoji'leri metin açıklamalarına çevir veya kaldır.
    # emoji.demojize(text) -> "Merhaba 👋" -> "Merhaba :waving_hand:"
    # Biz tamamen kaldıracağız.
    cleaned_text = emoji.replace_emoji(text, replace='')

    # 2. Markdown'ı temizle: **, *, __, _
    cleaned_text = re.sub(r'(\*\*|__)(.*?)(\1)', r'\2', cleaned_text) # Kalın
    cleaned_text = re.sub(r'(\*|_)(.*?)(\1)', r'\2', cleaned_text)     # İtalik

    # 3. URL'leri kaldır veya "link paylaşıldı" gibi bir ifadeye dönüştür.
    cleaned_text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', cleaned_text)
    
    # 4. Potansiyel SSML benzeri etiketleri veya artefaktları kaldır.
    # Örnek: "[image: ...]", "pitch:", vb.
    cleaned_text = re.sub(r'\[.*?\]', '', cleaned_text)
    cleaned_text = re.sub(r'\b(pitch|rate|volume):', '', cleaned_text, flags=re.IGNORECASE)

    # 5. Çoklu boşlukları tek boşluğa indirge
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
    
    return cleaned_text


def _create_ssml_from_text(text: str) -> str:
    """
    YENİDEN YAZILDI: Temizlenmiş metni alır ve Google dokümanlarına uygun,
    güvenli bir şekilde SSML'e dönüştürür.
    """
    # 1. Metni güvenli hale getir
    safe_text = text.replace('&', '&').replace('<', '<').replace('>', '>').replace('"', '"')
    
    # 2. Kelimelere ayır
    words = safe_text.split()
    
    # 3. Her kelimenin başına <mark> etiketini ekle
    marked_words = []
    for i, word in enumerate(words):
        marked_words.append(f'<mark name="w{i}"/>{word}')
    
    # 4. İşaretlenmiş kelimeleri birleştir
    marked_text = " ".join(marked_words)

    # 5. Cümle bazlı SSML efektlerini şimdi uygula
    # Bu yöntem, <mark> etiketlerinin içine başka etiketlerin girmesini önler.
    # Şimdilik basit tutuyoruz, daha sonra bu kısım geliştirilebilir.
    # Örneğin, cümlenin tamamını <emphasis> içine almak gibi.
    final_ssml_body = marked_text.replace('?', '?<break time="200ms"/>')

    # 6. Tamamını <speak> etiketiyle sar
    return f'<speak>{final_ssml_body}</speak>'


def synthesize_text_to_speech(text_to_speak: str, voice_name: str = "tr-TR-Standard-C") -> dict | None:
    if not tts_client or not text_to_speak or not text_to_speak.strip():
        return None
        
    try:
        # 1. YENİ ADIM: Fonetik düzeltmeleri uygula
        corrected_text = _apply_phonetic_corrections(text_to_speak)
        
        # 2. Temizleme işlemini düzeltilmiş metne yap
        cleaned_text = _clean_text_for_speech(corrected_text)
        if not cleaned_text:
            print("UYARI (TTS): Temizleme sonrası metin boş kaldı, ses üretilmiyor.")
            return None

        # 3. SSML'i temizlenmiş metinden oluştur
        ssml_text = _create_ssml_from_text(cleaned_text)
        print(f"DEBUG SSML (Nihai Hali): {ssml_text}")

        # 4. Google API için Gerekli Nesneleri Oluştur (Değişiklik yok)
        synthesis_input = texttospeech.SynthesisInput(ssml=ssml_text)
        voice = texttospeech.VoiceSelectionParams(language_code="tr-TR", name=voice_name)
        audio_config = texttospeech.AudioConfig(audio_encoding=texttospeech.AudioEncoding.MP3)
        
        request = texttospeech.SynthesizeSpeechRequest(
            input=synthesis_input,
            voice=voice,
            audio_config=audio_config,
            enable_time_pointing=[texttospeech.SynthesizeSpeechRequest.TimepointType.SSML_MARK]
        )
        
        # 5. API İsteğini Gönder ve Yanıtı İşle (Değişiklik yok)
        response = tts_client.synthesize_speech(request=request)

        word_timings = []
        original_words = cleaned_text.split()
        for timepoint in response.timepoints:
            try:
                word_index = int(timepoint.mark_name.replace('w', ''))
                word = original_words[word_index]
                word_timings.append({ "time": timepoint.time_seconds, "value": word })
            except (ValueError, IndexError):
                continue

        if response.audio_content and not word_timings and len(original_words) > 0:
             print("UYARI: Ses üretildi ancak kelime zamanlaması (timepoint) alınamadı. SSML yapısı hatalı olabilir.")

        print(f"✅ TTS Başarılı ({voice_name}): Ses ve {len(word_timings)} adet KELİME zamanlaması oluşturuldu.")
        
        return { "audio_content": response.audio_content, "visemes": word_timings }
        
    except Exception as e:
        print(f"HATA (synthesize_text_to_speech): {type(e).__name__} - {e}")
        traceback.print_exc()
        return None

# transcribe_audio_data fonksiyonu aynı kalabilir.
def transcribe_audio_data(audio_data: bytes, sample_rate: int) -> str | None:
    # ... (Mevcut kodunuz)
    if not speech_client:
        print("HATA (transcribe_audio_data): SpeechClient başlatılmamış.")
        return None
    try:
        audio = speech.RecognitionAudio(content=audio_data)
        config = speech.RecognitionConfig(
            encoding=speech.RecognitionConfig.AudioEncoding.LINEAR16,
            sample_rate_hertz=sample_rate,
            language_code="tr-TR",
            enable_automatic_punctuation=True
        )
        response = speech_client.recognize(config=config, audio=audio)
        if response.results:
            transcript = response.results[0].alternatives[0].transcript
            print(f"STT Sonucu: '{transcript}'")
            return transcript
        else:
            print("INFO (transcribe_audio_data): Konuşma algılanamadı.")
            return None
    except Exception as e:
        print(f"HATA (transcribe_audio_data): Beklenmedik Google STT API hatası.")
        traceback.print_exc()
        return None