<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Lip Sync Test Widget</title>
    <style>
        body { font-family: sans-serif; padding: 20px; }
        button { font-size: 16px; padding: 10px; cursor: pointer; }
        #logs { margin-top: 20px; border: 1px solid #ccc; padding: 10px; height: 300px; overflow-y: scroll; background: #f5f5f5; }
        .log-item { margin-bottom: 5px; }
        .viseme { color: blue; font-weight: bold; }
    </style>
</head>
<body>
    <h1>Basit Lip Sync Testi</h1>
    <p>Aşağıdaki butona tıklayarak sunucudan örnek bir ses ve dudak senkronizasyon verisi isteyin.</p>
    
    <input type="text" id="messageInput" value="Merhaba, oteliniz hakkında bilgi alabilir miyim?" style="width: 400px; padding: 5px;">
    <button id="testButton">Test Mesajı Gönder</button>
    <hr>
    <h3>Loglar:</h3>
    <div id="logs"></div>

    <!-- Socket.IO Client Kütüphanesi -->
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>

    <script>
        // --- Değişkenler ve Kurulum ---
        const backendUrl = `${window.location.protocol}//${window.location.host}`;
        const socket = io(backendUrl);

        const testButton = document.getElementById('testButton');
        const messageInput = document.getElementById('messageInput');
        const logContainer = document.getElementById('logs');
        
        // Web Audio API için kurulum
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        let currentSessionId = null;

        // --- Yardımcı Fonksiyonlar ---
        function logMessage(message, className = '') {
            const p = document.createElement('p');
            p.textContent = message;
            p.className = `log-item ${className}`;
            logContainer.appendChild(p);
            logContainer.scrollTop = logContainer.scrollHeight; // Otomatik aşağı kaydır
            console.log(message); // Konsola da yaz
        }

        // Base64'ü ArrayBuffer'a çevirir
        function base64ToArrayBuffer(base64) {
            const binaryString = window.atob(base64);
            const len = binaryString.length;
            const bytes = new Uint8Array(len);
            for (let i = 0; i < len; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }
            return bytes.buffer;
        }

        // --- Olay Dinleyicileri ---
        testButton.addEventListener('click', () => {
            logMessage('Test mesajı sunucuya gönderiliyor...');
            const userMessage = messageInput.value;
            
            // Eğer session yoksa, önce bir session başlat
            if (!currentSessionId) {
                // Not: 'hotel_identifier' sizin sisteminizde nasıl ayarlandıysa onu kullanın.
                socket.emit('start_chat_from_widget', { hotel_identifier: 'myplusıd123456' });
            } else {
                // Session varsa direkt mesajı gönder
                socket.emit('user_message_to_server', { message: userMessage, session_id: currentSessionId });
            }
        });

        socket.on('connect', () => {
            logMessage('Sunucuya başarıyla bağlandı.', 'viseme');
        });

        socket.on('initial_chat_data', (data) => {
            if(data.error) {
                logMessage(`Hata: ${data.error}`);
                return;
            }
            currentSessionId = data.session_id;
            logMessage(`Yeni oturum başlatıldı: ${currentSessionId.substring(0, 8)}...`);
            // Oturum başladıktan sonra test mesajını hemen gönderelim.
            testButton.click(); 
        });

        // --- ANA MANTIK: Sunucudan Gelen Yanıtı İşleme ---
        socket.on('ai_response_to_widget', (data) => {
            logMessage(`AI yanıtı alındı: "${data.message}"`);

            if (data.audio_content_b64 && data.visemes) {
                logMessage('Ses ve Viseme verisi mevcut. Oynatma başlıyor...');
                
                const audioBuffer = base64ToArrayBuffer(data.audio_content_b64);
                
                audioContext.decodeAudioData(audioBuffer, (decodedBuffer) => {
                    const source = audioContext.createBufferSource();
                    source.buffer = decodedBuffer;
                    source.connect(audioContext.destination);
                    
                    // Sesi çalmaya başla ve başlangıç zamanını kaydet
                    const startTime = audioContext.currentTime;
                    source.start(0);
                    logMessage('Ses çalmaya başladı.', 'viseme');
                    
                    // Viseme'leri zamanlamak için döngü başlat
                    playVisemes(data.visemes, startTime);

                }, (error) => {
                    logMessage(`Ses verisi decode edilemedi: ${error}`);
                });

            } else {
                logMessage('Bu yanıtta ses veya viseme verisi bulunmuyor.');
            }
        });

        function playVisemes(visemes, audioStartTime) {
            let visemeIndex = 0;

            function checkVisemes() {
                if (visemeIndex >= visemes.length) {
                    return; // Tüm viseme'ler işlendi
                }

                const elapsedTime = audioContext.currentTime - audioStartTime;
                const nextViseme = visemes[visemeIndex];

                if (elapsedTime >= nextViseme.time) {
                    // Zamanı geldi, viseme'i "oynat" (yani konsola yazdır)
                    logMessage(`Viseme -> Zaman: ${nextViseme.time.toFixed(2)}s, Değer: ${nextViseme.value}`, 'viseme');
                    
                    // Bir sonraki viseme'e geç
                    visemeIndex++;
                }

                // Bir sonraki frame'de tekrar kontrol et
                requestAnimationFrame(checkVisemes);
            }

            // Döngüyü başlat
            requestAnimationFrame(checkVisemes);
        }

    </script>
</body>
</html>