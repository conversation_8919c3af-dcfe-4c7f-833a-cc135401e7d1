import React, { createContext, useContext } from 'react';
import axios from 'axios';
import { useAuth } from './AuthContext';

const ApiContext = createContext();

export const useApi = () => {
  const context = useContext(ApiContext);
  if (!context) {
    throw new Error('useApi must be used within an ApiProvider');
  }
  return context;
};

export const ApiProvider = ({ children }) => {
  const { token, user } = useAuth();

  // Production API URL - using proxy to avoid CORS
  const BASE_URL = '/api';

  // Demo mode flag - set to false when backend is ready
  const DEMO_MODE = false;

  // Simulate real-time data updates
  const [liveData, setLiveData] = React.useState({
    activeSessions: 3,
    newReservations: 2,
    lastUpdate: new Date().toLocaleTimeString()
  });

  // Simulate live updates every 30 seconds
  React.useEffect(() => {
    if (DEMO_MODE) {
      const interval = setInterval(() => {
        setLiveData(prev => ({
          activeSessions: Math.floor(Math.random() * 8) + 1,
          newReservations: Math.floor(Math.random() * 5),
          lastUpdate: new Date().toLocaleTimeString()
        }));
      }, 30000);
      return () => clearInterval(interval);
    }
  }, []);

  // Create axios instance with default config
  const api = axios.create({
    baseURL: BASE_URL,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Add auth token to requests if available
  api.interceptors.request.use((config) => {
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  });

  // API methods
  const apiMethods = {
    // Dashboard metrics - Otel yöneticisi için kapsamlı metrikler
    getDashboardTotals: async (hotelId) => {
      console.log('API Call - Hotel ID:', hotelId);
      if (DEMO_MODE) {
        // Return mock data for demo
        return {
          total_chat_sessions: 156,
          total_confirmed_reservations: 89,
          total_ai_initiated_reservations: 67,
          today_revenue: 12500,
          occupancy_rate: 78.5,
          avg_response_time: 2.3,
          customer_satisfaction: 4.6,
          conversion_rate: 23.4
        };
      }
      const url = `/get_dashboard_totals?hotel_id_key=${hotelId}`;
      console.log('API URL:', url);
      const response = await api.get(url);
      console.log('API Response:', response.data);
      return response.data;
    },

    // Otel performans metrikleri
    getHotelMetrics: async (hotelId, period = '7d') => {
      console.log('API Call - Hotel Metrics:', hotelId, period);
      if (DEMO_MODE) {
        return {
          revenue_trend: [
            { date: '2024-01-01', revenue: 8500, bookings: 12 },
            { date: '2024-01-02', revenue: 9200, bookings: 15 },
            { date: '2024-01-03', revenue: 11800, bookings: 18 },
            { date: '2024-01-04', revenue: 10500, bookings: 14 },
            { date: '2024-01-05', revenue: 13200, bookings: 20 },
            { date: '2024-01-06', revenue: 15600, bookings: 24 },
            { date: '2024-01-07', revenue: 12400, bookings: 16 }
          ],
          occupancy_trend: [
            { date: '2024-01-01', rate: 65 },
            { date: '2024-01-02', rate: 72 },
            { date: '2024-01-03', rate: 85 },
            { date: '2024-01-04', rate: 78 },
            { date: '2024-01-05', rate: 92 },
            { date: '2024-01-06', rate: 95 },
            { date: '2024-01-07', rate: 88 }
          ],
          ai_performance: {
            total_interactions: 1247,
            successful_bookings: 89,
            avg_conversation_length: 4.2,
            top_intents: [
              { intent: 'Rezervasyon', count: 456 },
              { intent: 'Fiyat Sorgusu', count: 234 },
              { intent: 'Oda Özellikleri', count: 189 },
              { intent: 'İptal/Değişiklik', count: 156 }
            ]
          }
        };
      }
      const response = await api.get(`/get_hotel_metrics?hotel_id_key=${hotelId}&period=${period}`);
      return response.data;
    },

    // Reservations
    getReservations: async (hotelId) => {
      console.log('API Call - Get Reservations for Hotel ID:', hotelId);
      if (DEMO_MODE) {
        // Return mock data for demo
        return {
          reservations: [
            {
              id: 1,
              reservation_id: 'RES001',
              guest_name: 'Ahmet',
              guest_surname: 'Yılmaz',
              room_type: 'Deluxe Room',
              check_in_date: '2024-01-15',
              check_out_date: '2024-01-18',
              status: 'HUMAN_CONFIRMED',
              total_price_at_booking: '2400'
            },
            {
              id: 2,
              reservation_id: 'RES002',
              guest_name: 'Fatma',
              guest_surname: 'Kaya',
              room_type: 'Standard Room',
              check_in_date: '2024-01-20',
              check_out_date: '2024-01-22',
              status: 'AI_PENDING_HUMAN',
              total_price_at_booking: '1600'
            }
          ]
        };
      }
      const url = `/get_reservations?hotel_id_key=${hotelId}`;
      console.log('Reservations API URL:', url);
      const response = await api.get(url);
      console.log('Reservations API Response:', response.data);
      return response.data;
    },

    updateReservationStatus: async (reservationId, status, reason = null) => {
      const response = await api.post('/update_reservation_status', {
        reservation_db_id: reservationId,
        new_status: status,
        reason: reason
      });
      return response.data;
    },

    // Chat management
    getActiveChatSessions: async () => {
      console.log('API Call - Get Active Chat Sessions');
      if (DEMO_MODE) {
        return {
          active_chats: [
            {
              session_id: 'demo_session_001',
              source: 'web_widget',
              hotel_name: 'MyPlus Hotel Ataşehir',
              start_time: new Date().toISOString(),
              hotel_config_key: 'myplushotelataehir_f40e4c3f'
            }
          ],
          count: 1
        };
      }
      const response = await api.get('/get_active_chats');
      console.log('Active Chats API Response:', response.data);
      return response.data;
    },

    getChatHistory: async (sessionId) => {
      console.log('API Call - Get Chat History for session:', sessionId);
      if (DEMO_MODE) {
        return {
          chat_history: [
            {
              id: 1,
              session_id: sessionId,
              sender: 'USER',
              message: 'Merhaba, rezervasyon yapmak istiyorum.',
              timestamp: new Date().toISOString(),
              source: 'web_widget'
            },
            {
              id: 2,
              session_id: sessionId,
              sender: 'AI',
              message: 'Merhaba! Size yardımcı olmaktan mutluluk duyarım. Hangi tarihler için rezervasyon yapmak istiyorsunuz?',
              timestamp: new Date().toISOString(),
              source: 'web_widget'
            }
          ],
          session_id: sessionId
        };
      }
      const response = await api.get(`/get_chat_history/${sessionId}`);
      console.log('Chat History API Response:', response.data);
      return response.data;
    },

    startChatSession: async (hotelId) => {
      const response = await api.post('/start_chat_with_config', {
        hotel_id_key: hotelId
      });
      return response.data;
    },

    sendChatMessage: async (sessionId, message) => {
      const response = await api.post('/chat', {
        session_id: sessionId,
        message: message
      });
      return response.data;
    },

    // Analytics
    getAnalysisReports: async () => {
      const response = await api.get('/get_analysis_reports');
      return response.data;
    },

    getAnalysisReportDetail: async (reportId) => {
      const response = await api.get(`/get_analysis_report_detail/${reportId}`);
      return response.data;
    },

    triggerWeeklyAnalysis: async () => {
      const response = await api.post('/trigger_analysis', {
        analysis_type: 'weekly'
      });
      return response.data;
    },

    // Google integration
    getGoogleReviews: async (hotelId) => {
      const response = await api.get(`/get_google_reviews?hotel_id_key=${hotelId}`);
      return response.data;
    },

    syncGoogleReviews: async (hotelId) => {
      const response = await api.post('/sync_google_reviews', {
        hotel_id_key: hotelId
      });
      return response.data;
    },

    // Facebook integration
    getFacebookComments: async (hotelId) => {
      const response = await api.get(`/get_facebook_comments?hotel_id_key=${hotelId}`);
      return response.data;
    },

    // Hotel configuration
    getHotelConfig: async (hotelId) => {
      const response = await api.get(`/get_hotel_config?hotel_id_key=${hotelId}`);
      return response.data;
    }
  };

  const value = {
    api,
    ...apiMethods,
    baseUrl: BASE_URL,
    currentHotelId: user?.hotelId || 'myplushotelataehir_f40e4c3f'
  };

  return (
    <ApiContext.Provider value={value}>
      {children}
    </ApiContext.Provider>
  );
};
