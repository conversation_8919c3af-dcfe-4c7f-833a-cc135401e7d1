import React, { createContext, useContext } from 'react';
import axios from 'axios';
import { useAuth } from './AuthContext';

const ApiContext = createContext();

export const useApi = () => {
  const context = useContext(ApiContext);
  if (!context) {
    throw new Error('useApi must be used within an ApiProvider');
  }
  return context;
};

export const ApiProvider = ({ children }) => {
  const { token, user } = useAuth();

  // Production API URL - using proxy to avoid CORS
  const BASE_URL = '/api';

  // Demo mode flag - set to false when backend is ready
  const DEMO_MODE = false;

  // Simulate real-time data updates
  const [liveData, setLiveData] = React.useState({
    activeSessions: 3,
    newReservations: 2,
    lastUpdate: new Date().toLocaleTimeString()
  });

  // Simulate live updates every 30 seconds
  React.useEffect(() => {
    if (DEMO_MODE) {
      const interval = setInterval(() => {
        setLiveData(prev => ({
          activeSessions: Math.floor(Math.random() * 8) + 1,
          newReservations: Math.floor(Math.random() * 5),
          lastUpdate: new Date().toLocaleTimeString()
        }));
      }, 30000);
      return () => clearInterval(interval);
    }
  }, []);

  // Create axios instance with default config
  const api = axios.create({
    baseURL: BASE_URL,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Add auth token to requests if available
  api.interceptors.request.use((config) => {
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  });

  // API methods
  const apiMethods = {
    // Dashboard metrics - Otel operasyonel metrikleri
    getDashboardTotals: async (hotelId) => {
      console.log('API Call - Hotel ID:', hotelId);
      if (DEMO_MODE) {
        // Return mock data for demo
        return {
          total_chat_sessions: 156,
          total_confirmed_reservations: 89,
          total_ai_initiated_reservations: 67,
          pending_reservations: 4, // ONAY BEKLEYEN - EN ÖNEMLİ
          today_checkins: 12,
          today_checkouts: 8,
          today_occupancy: 85,
          today_revenue: 42500,
          yesterday_occupancy: 80,
          yesterday_revenue: 44600,
          active_chats: 3,
          abandoned_chats_1h: 1,
          customer_sentiment: 82, // %82 pozitif
          avg_response_time: 2.3,
          top_keywords: [
            { word: 'JAKUZİ', count: 45 },
            { word: 'FİYAT', count: 38 },
            { word: 'KAHVALTI', count: 32 },
            { word: 'ULAŞIM', count: 28 },
            { word: 'FENERBAHÇE', count: 24 },
            { word: 'KONSER', count: 18 }
          ],
          competitor_prices: [
            { name: 'Siz (Standart Oda)', price: 2000, change: 0 },
            { name: 'Rakip X Oteli', price: 2250, change: 1 },
            { name: 'Rakip Y Oteli', price: 1900, change: -1 }
          ],
          ai_insight: "Son 24 saatte 4 farklı kişi 'toplantı odası' hakkında soru sordu. Web sitenizin ana sayfasına 'İş Toplantıları İçin İdealiz' gibi bir başlık eklemek, bu talebi rezervasyona çevirebilir."
        };
      }
      const url = `/get_dashboard_totals?hotel_id_key=${hotelId}`;
      console.log('API URL:', url);
      const response = await api.get(url);
      console.log('API Response:', response.data);
      return response.data;
    },

    // AI Chat Analytics - Gerçek AI resepsiyon metrikleri
    getAIChatAnalytics: async (hotelId) => {
      console.log('API Call - AI Chat Analytics for Hotel ID:', hotelId);
      try {
        const response = await api.get(`/get_ai_chat_analytics?hotel_id_key=${hotelId}`);
        console.log('AI Chat Analytics API Response:', response.data);
        return response.data;
      } catch (error) {
        console.warn('AI Chat Analytics endpoint not found, using demo data:', error);

        // Demo data - AI resepsiyon sistemi metrikleri
        return {
          // Aktif sohbet metrikleri
          active_chat_sessions: 3,
          total_chat_sessions_today: 24,
          total_chat_sessions_week: 156,
          total_chat_sessions_month: 678,

          // AI performans metrikleri
          avg_response_time_seconds: 2.3,
          ai_success_rate: 87.5, // %87.5 başarılı yanıt
          conversation_completion_rate: 73.2, // %73.2 tamamlanan konuşma

          // Rezervasyon dönüşüm metrikleri
          total_reservations_from_ai: 67,
          ai_conversion_rate: 12.8, // %12.8 chat'ten rezervasyona dönüşüm
          pending_reservations: 4,
          confirmed_reservations_today: 8,

          // Konuşma analizi
          most_asked_topics: [
            { topic: 'Oda Fiyatları', count: 45, percentage: 18.2 },
            { topic: 'Otel Olanakları', count: 38, percentage: 15.4 },
            { topic: 'Rezervasyon İşlemleri', count: 32, percentage: 13.0 },
            { topic: 'Konum ve Ulaşım', count: 28, percentage: 11.3 },
            { topic: 'Check-in/Check-out', count: 24, percentage: 9.7 },
            { topic: 'İptal İşlemleri', count: 18, percentage: 7.3 }
          ],

          // Sentiment analizi
          customer_sentiment: {
            positive: 82,
            neutral: 15,
            negative: 3
          },

          // AI insights
          ai_insights: [
            {
              type: 'opportunity',
              title: 'Fiyat Sorularında Artış',
              description: 'Son 24 saatte fiyat soruları %25 arttı. Özel indirim kampanyası başlatmayı düşünebilirsiniz.',
              priority: 'high'
            },
            {
              type: 'improvement',
              title: 'Yanıt Süresi Optimizasyonu',
              description: 'Ortalama yanıt süreniz 2.3 saniye. 2 saniyenin altına düşürmek müşteri memnuniyetini artırabilir.',
              priority: 'medium'
            }
          ],

          // Kayıp fırsatlar
          abandoned_conversations: {
            last_hour: 1,
            today: 8,
            common_exit_points: [
              { point: 'Fiyat bilgisi sonrası', count: 5 },
              { point: 'Rezervasyon formu', count: 2 },
              { point: 'Ödeme bilgileri', count: 1 }
            ]
          }
        };
      }
    },

    // Otel konfigürasyonu yönetimi - Server'daki endpoint'e uygun
    getHotelConfig: async (hotelId) => {
      console.log('API Call - Get Hotel Config:', hotelId);

      try {
        // Önce mevcut hotels tablosundan veri çekmeyi dene
        const response = await api.get(`/get_hotel_config?hotel_id_key=${hotelId}`);
        console.log('Hotel Config API Response:', response.data);
        return response.data;
      } catch (error) {
        console.warn('Hotel config endpoint not found, using demo data:', error);

        // Endpoint yoksa demo data döndür
        return {
          hotelId: hotelId || 'myplushotelataehir_f40e4c3f',
          hotelName: 'MyPlus Hotel Ataşehir',
          hotelCity: 'İstanbul',
          hotelDescription: 'Modern ve konforlu konaklama deneyimi sunan butik otel.',
          hotelAddress: 'Ataşehir, İstanbul',
          hotelPhone: '+90 216 123 45 67',
          hotelEmail: '<EMAIL>',
          hotelCheckInTime: '14:00',
          hotelCheckOutTime: '12:00',
          aiResponseDelaySeconds: 2.5,
          hotelPolicy: 'Misafirlerimizin konforunu ön planda tutuyoruz.',
          hotelAmenities: 'WiFi, Spa, Fitness, Restaurant, Bar, Parking, Pool',
          paymentMethods: 'Nakit, Kredi Kartı, Havale',
          nearbyAttractions: 'Zorlu Center, Vadistanbul, Optimum Outlet',
          contactHours: '24/7',
          hotelLatitude: '41.0082',
          hotelLongitude: '28.9784',
          placeId: 'ChIJVVVVVVVVVVVVVVVVVVVVVV',
          gmbAccountId: 'gmb_account_123',
          gmbLocationId: 'gmb_location_456',
          roomTypes: [
            {
              name: 'Standart Oda',
              capacity: '2',
              price: '800',
              features: 'Klima, TV, Wi-Fi',
              totalRoomsOfType: 50,
              bedType: 'Çift Kişilik',
              viewType: 'Şehir Manzaralı',
              sizeSquareMeters: 25,
              roomAmenities: 'Klima, TV, Wi-Fi, Minibar',
              minStayNights: 1,
              maxGuests: 2,
              imageUrls: []
            },
            {
              name: 'Deluxe Oda',
              capacity: '3',
              price: '1200',
              features: 'Klima, TV, Wi-Fi, Jakuzi',
              totalRoomsOfType: 30,
              bedType: 'King Size',
              viewType: 'Deniz Manzaralı',
              sizeSquareMeters: 35,
              roomAmenities: 'Klima, TV, Wi-Fi, Minibar, Jakuzi',
              minStayNights: 1,
              maxGuests: 3,
              imageUrls: []
            }
          ]
        };
      }
    },

    updateHotelConfig: async (configData) => {
      console.log('API Call - Update Hotel Config:', configData);
      if (DEMO_MODE) {
        return { message: 'Otel konfigürasyonu güncellendi (demo mode)' };
      }
      const response = await api.post('/update_hotel_config', configData);
      console.log('Update Hotel Config API Response:', response.data);
      return response.data;
    },

    // Oda bilgileri yönetimi
    getRoomTypes: async (hotelId) => {
      console.log('API Call - Get Room Types:', hotelId);
      if (DEMO_MODE) {
        return [
          { id: 1, name: 'Standart Oda', quantity: 50, price: 800 },
          { id: 2, name: 'Deluxe Oda', quantity: 30, price: 1200 },
          { id: 3, name: 'Suite', quantity: 10, price: 2000 }
        ];
      }
      const response = await api.get(`/get_room_types?hotel_id_key=${hotelId}`);
      console.log('Room Types API Response:', response.data);
      return response.data;
    },

    updateRoomTypes: async (hotelId, roomTypes) => {
      console.log('API Call - Update Room Types:', hotelId, roomTypes);
      if (DEMO_MODE) {
        return { success: true, message: 'Oda tipleri güncellendi (demo mode)' };
      }
      const response = await api.post('/update_room_types', {
        hotel_id_key: hotelId,
        room_types: roomTypes
      });
      console.log('Update Room Types API Response:', response.data);
      return response.data;
    },

    // Reservations
    getReservations: async (hotelId) => {
      console.log('API Call - Get Reservations for Hotel ID:', hotelId);
      if (DEMO_MODE) {
        // Return mock data for demo
        return {
          reservations: [
            {
              id: 1,
              reservation_id: 'RES001',
              guest_name: 'Ahmet',
              guest_surname: 'Yılmaz',
              room_type: 'Deluxe Room',
              check_in_date: '2024-01-15',
              check_out_date: '2024-01-18',
              status: 'HUMAN_CONFIRMED',
              total_price_at_booking: '2400'
            },
            {
              id: 2,
              reservation_id: 'RES002',
              guest_name: 'Fatma',
              guest_surname: 'Kaya',
              room_type: 'Standard Room',
              check_in_date: '2024-01-20',
              check_out_date: '2024-01-22',
              status: 'AI_PENDING_HUMAN',
              total_price_at_booking: '1600'
            }
          ]
        };
      }
      const url = `/get_reservations?hotel_id_key=${hotelId}`;
      console.log('Reservations API URL:', url);
      const response = await api.get(url);
      console.log('Reservations API Response:', response.data);
      return response.data;
    },

    updateReservationStatus: async (reservationId, status, reason = null) => {
      const response = await api.post('/update_reservation_status', {
        reservation_db_id: reservationId,
        new_status: status,
        reason: reason
      });
      return response.data;
    },

    // Chat management
    getActiveChatSessions: async (hotelId) => {
      console.log('API Call - Get Active Chat Sessions for hotel:', hotelId);
      try {
        const response = await api.get(`/get_active_chats?hotel_id_key=${hotelId}`);
        console.log('Active Chats API Response:', response.data);
        return response.data;
      } catch (error) {
        console.warn('Active chats endpoint error, using demo data:', error);
        return {
          active_chats: [
            {
              session_id: 'demo_session_001',
              source: 'web_widget',
              hotel_name: 'MyPlus Hotel Ataşehir',
              start_time: new Date().toISOString(),
              hotel_config_key: hotelId,
              presence: 'active'
            },
            {
              session_id: 'demo_session_002',
              source: 'web_widget',
              hotel_name: 'MyPlus Hotel Ataşehir',
              start_time: new Date().toISOString(),
              hotel_config_key: hotelId,
              presence: 'active'
            },
            {
              session_id: 'demo_session_003',
              source: 'web_widget',
              hotel_name: 'MyPlus Hotel Ataşehir',
              start_time: new Date().toISOString(),
              hotel_config_key: hotelId,
              presence: 'active'
            }
          ],
          count: 3
        };
      }
    },

    getChatHistory: async (sessionId) => {
      console.log('API Call - Get Chat History for session:', sessionId);
      try {
        const response = await api.get(`/get_chat_history/${sessionId}`);
        console.log('Chat History API Response:', response.data);

        // Server response formatını frontend formatına çevir
        const formattedMessages = response.data.chat_history?.map(msg => ({
          sender: msg.sender,
          message: msg.message,
          timestamp: msg.timestamp
        })) || [];

        return { messages: formattedMessages };
      } catch (error) {
        console.warn('Chat history endpoint error, using demo data:', error);
        return {
          messages: [
            {
              sender: 'USER',
              message: 'Merhaba, rezervasyon yapmak istiyorum.',
              timestamp: new Date().toISOString()
            },
            {
              sender: 'AI',
              message: 'Merhaba! Size yardımcı olmaktan mutluluk duyarım. Hangi tarihler için rezervasyon yapmak istiyorsunuz?',
              timestamp: new Date().toISOString()
            }
          ]
        };
      }
    },

    // Geçmiş chat oturumlarını çek
    getHistoricalChatSessions: async (hotelId) => {
      console.log('API Call - Get Historical Chat Sessions for hotel:', hotelId);
      try {
        const response = await api.get(`/get_historical_chat_sessions?hotel_id_key=${hotelId}`);
        console.log('Historical Chat Sessions API Response:', response.data);
        return response.data;
      } catch (error) {
        console.warn('Historical chat sessions endpoint error, using demo data:', error);
        return {
          historical_sessions: [
            {
              id: 'hist_001',
              sessionId: 'hist_001',
              guestName: 'Ahmet Yılmaz',
              startTime: '14:30',
              startDate: '15/12/2024',
              endTime: '14:45',
              duration: '15 dakika',
              status: 'completed',
              source: 'web_widget',
              messageCount: 12,
              outcome: 'reservation_made'
            },
            {
              id: 'hist_002',
              sessionId: 'hist_002',
              guestName: 'Fatma Kaya',
              startTime: '13:15',
              startDate: '15/12/2024',
              endTime: '13:25',
              duration: '10 dakika',
              status: 'completed',
              source: 'web_widget',
              messageCount: 8,
              outcome: 'information_provided'
            }
          ],
          count: 2
        };
      }
    },

    // Admin mesajı gönder
    sendAdminMessage: async (sessionId, message) => {
      console.log('API Call - Send Admin Message:', { sessionId, message });
      try {
        const response = await api.post('/send_admin_message', {
          session_id: sessionId,
          message: message
        });
        console.log('Send Admin Message API Response:', response.data);
        return response.data;
      } catch (error) {
        console.error('Send admin message error:', error);
        throw error;
      }
    },

    startChatSession: async (hotelId) => {
      const response = await api.post('/start_chat_with_config', {
        hotel_id_key: hotelId
      });
      return response.data;
    },

    sendChatMessage: async (sessionId, message) => {
      const response = await api.post('/chat', {
        session_id: sessionId,
        message: message
      });
      return response.data;
    },

    // Analytics
    getAnalysisReports: async () => {
      const response = await api.get('/get_analysis_reports');
      return response.data;
    },

    getAnalysisReportDetail: async (reportId) => {
      const response = await api.get(`/get_analysis_report_detail/${reportId}`);
      return response.data;
    },

    triggerWeeklyAnalysis: async () => {
      const response = await api.post('/trigger_analysis', {
        analysis_type: 'weekly'
      });
      return response.data;
    },

    // Google integration
    getGoogleReviews: async (hotelId) => {
      const response = await api.get(`/get_google_reviews?hotel_id_key=${hotelId}`);
      return response.data;
    },

    syncGoogleReviews: async (hotelId) => {
      const response = await api.post('/sync_google_reviews', {
        hotel_id_key: hotelId
      });
      return response.data;
    },

    // Facebook integration
    getFacebookComments: async (hotelId) => {
      const response = await api.get(`/get_facebook_comments?hotel_id_key=${hotelId}`);
      return response.data;
    }
  };

  const value = {
    api,
    ...apiMethods,
    baseUrl: BASE_URL,
    currentHotelId: user?.hotelId || 'myplushotelataehir_f40e4c3f'
  };

  return (
    <ApiContext.Provider value={value}>
      {children}
    </ApiContext.Provider>
  );
};
