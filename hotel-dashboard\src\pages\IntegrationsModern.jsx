import { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Switch,
  FormControlLabel,
  Chip,
  Avatar,
  CircularProgress,
  Alert,
  Divider,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Google as GoogleIcon,
  Facebook as FacebookIcon,
  Instagram as InstagramIcon,
  Settings as SettingsIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Sync as SyncIcon,
  Add as AddIcon,
  Launch as LaunchIcon,
  Notifications as NotificationsIcon,
  Analytics as AnalyticsIcon,
  Chat as ChatIcon,
  Star as StarIcon,
  TravelExplore as YandexIcon,
  Hotel as BookingIcon,
  Flight as ExpediaIcon,
  Home as AirbnbIcon,
  Business as TripAdvisorIcon
} from '@mui/icons-material';
import { useApi } from '../contexts/ApiContext';

const IntegrationsModern = () => {
  const { getGoogleReviews, syncGoogleReviews, getFacebookComments, currentHotelId } = useApi();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [syncing, setSyncing] = useState(null);
  const [configDialog, setConfigDialog] = useState({ open: false, integration: null });

  // Entegrasyon durumları
  const [integrations, setIntegrations] = useState([
    {
      id: 'google',
      name: 'Google My Business',
      description: 'Google yorumlarını senkronize edin ve AI ile yanıtlayın',
      icon: <GoogleIcon />,
      color: '#4285f4',
      connected: false,
      status: 'disconnected',
      lastSync: null,
      stats: { reviews: 0, rating: 0 },
      features: ['Yorum Senkronizasyonu', 'AI Yanıt Önerisi', 'Rating Analizi']
    },
    {
      id: 'facebook',
      name: 'Facebook',
      description: 'Facebook sayfa yorumları ve mesajlarını yönetin',
      icon: <FacebookIcon />,
      color: '#1877f2',
      connected: false,
      status: 'disconnected',
      lastSync: null,
      stats: { comments: 0, messages: 0 },
      features: ['Sayfa Yorumları', 'Özel Mesajlar', 'Otomatik Yanıtlar']
    },
    {
      id: 'instagram',
      name: 'Instagram Business',
      description: 'Instagram yorumları ve DM\'leri yönetin',
      icon: <InstagramIcon />,
      color: '#E4405F',
      connected: false,
      status: 'coming_soon',
      lastSync: null,
      stats: { followers: 0, posts: 0 },
      features: ['Yorum Yönetimi', 'DM Otomasyonu', 'Hashtag Analizi']
    },
    {
      id: 'yandex',
      name: 'Yandex Maps',
      description: 'Yandex haritalar yorumlarını takip edin',
      icon: <YandexIcon />,
      color: '#FF0000',
      connected: false,
      status: 'coming_soon',
      lastSync: null,
      stats: { reviews: 0, rating: 0 },
      features: ['Yorum Takibi', 'Rating Analizi', 'Konum Optimizasyonu']
    },
    {
      id: 'booking',
      name: 'Booking.com',
      description: 'Booking.com rezervasyonları ve yorumları',
      icon: <BookingIcon />,
      color: '#003580',
      connected: false,
      status: 'coming_soon',
      lastSync: null,
      stats: { bookings: 0, reviews: 0 },
      features: ['Rezervasyon Senkronizasyonu', 'Yorum Yönetimi', 'Fiyat Analizi']
    },
    {
      id: 'tripadvisor',
      name: 'TripAdvisor',
      description: 'TripAdvisor yorumları ve sıralaması',
      icon: <TripAdvisorIcon />,
      color: '#00AF87',
      connected: false,
      status: 'coming_soon',
      lastSync: null,
      stats: { reviews: 0, ranking: 0 },
      features: ['Yorum Analizi', 'Sıralama Takibi', 'Rekabet Analizi']
    },
    {
      id: 'expedia',
      name: 'Expedia',
      description: 'Expedia rezervasyonları ve yorumları',
      icon: <ExpediaIcon />,
      color: '#FFC72C',
      connected: false,
      status: 'coming_soon',
      lastSync: null,
      stats: { bookings: 0, reviews: 0 },
      features: ['Rezervasyon Yönetimi', 'Yorum Takibi', 'Fiyat Optimizasyonu']
    },
    {
      id: 'airbnb',
      name: 'Airbnb',
      description: 'Airbnb rezervasyonları ve konuk yorumları',
      icon: <AirbnbIcon />,
      color: '#FF5A5F',
      connected: false,
      status: 'coming_soon',
      lastSync: null,
      stats: { bookings: 0, reviews: 0 },
      features: ['Rezervasyon Senkronizasyonu', 'Konuk İletişimi', 'Yorum Yönetimi']
    },
    {
      id: 'whatsapp',
      name: 'WhatsApp Business',
      description: 'WhatsApp üzerinden müşteri iletişimi',
      icon: <ChatIcon />,
      color: '#25D366',
      connected: false,
      status: 'coming_soon',
      lastSync: null,
      stats: { messages: 0, contacts: 0 },
      features: ['Otomatik Yanıtlar', 'Rezervasyon Onayları', 'Müşteri Desteği']
    },
    {
      id: 'telegram',
      name: 'Telegram Bot',
      description: 'Telegram bot ile rezervasyon ve bilgi servisi',
      icon: <NotificationsIcon />,
      color: '#0088CC',
      connected: false,
      status: 'coming_soon',
      lastSync: null,
      stats: { subscribers: 0, messages: 0 },
      features: ['Bot Otomasyonu', 'Bildirimler', 'Rezervasyon Asistanı']
    }
  ]);

  const fetchIntegrationData = async () => {
    try {
      setLoading(true);
      
      // Google entegrasyonu kontrol et
      try {
        const googleData = await getGoogleReviews(currentHotelId);
        setIntegrations(prev => prev.map(int => 
          int.id === 'google' 
            ? { 
                ...int, 
                connected: true, 
                status: 'active',
                lastSync: new Date().toLocaleString('tr-TR'),
                stats: { reviews: googleData.reviews?.length || 0, rating: 4.2 }
              }
            : int
        ));
      } catch (err) {
        console.log('Google entegrasyonu bağlı değil');
      }

      // Facebook entegrasyonu kontrol et
      try {
        const facebookData = await getFacebookComments(currentHotelId);
        setIntegrations(prev => prev.map(int => 
          int.id === 'facebook' 
            ? { 
                ...int, 
                connected: true, 
                status: 'active',
                lastSync: new Date().toLocaleString('tr-TR'),
                stats: { comments: facebookData.comments?.length || 0, messages: 12 }
              }
            : int
        ));
      } catch (err) {
        console.log('Facebook entegrasyonu bağlı değil');
      }

    } catch (err) {
      setError('Entegrasyon verileri yüklenirken hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleSync = async (integrationId) => {
    try {
      setSyncing(integrationId);
      
      if (integrationId === 'google') {
        await syncGoogleReviews(currentHotelId);
      }
      
      // Başarı mesajı
      setIntegrations(prev => prev.map(int => 
        int.id === integrationId 
          ? { ...int, lastSync: new Date().toLocaleString('tr-TR') }
          : int
      ));
      
    } catch (err) {
      setError(`${integrationId} senkronizasyonu başarısız: ${err.message}`);
    } finally {
      setSyncing(null);
    }
  };

  const handleToggleConnection = (integrationId) => {
    const integration = integrations.find(int => int.id === integrationId);
    
    if (integration.status === 'coming_soon') {
      alert('Bu entegrasyon yakında kullanıma sunulacak!');
      return;
    }
    
    if (!integration.connected) {
      setConfigDialog({ open: true, integration });
    } else {
      // Bağlantıyı kes
      setIntegrations(prev => prev.map(int => 
        int.id === integrationId 
          ? { ...int, connected: false, status: 'disconnected' }
          : int
      ));
    }
  };

  const handleConfigSave = () => {
    const { integration } = configDialog;
    setIntegrations(prev => prev.map(int => 
      int.id === integration.id 
        ? { ...int, connected: true, status: 'active', lastSync: new Date().toLocaleString('tr-TR') }
        : int
    ));
    setConfigDialog({ open: false, integration: null });
  };

  const getStatusChip = (status, connected) => {
    if (status === 'coming_soon') {
      return <Chip label="Yakında" color="info" size="small" />;
    }
    
    if (!connected) {
      return <Chip label="Bağlı Değil" color="default" size="small" />;
    }
    
    const statusMap = {
      'active': { label: 'Aktif', color: 'success', icon: <CheckIcon /> },
      'warning': { label: 'Uyarı', color: 'warning', icon: <WarningIcon /> },
      'error': { label: 'Hata', color: 'error', icon: <ErrorIcon /> }
    };
    
    const statusInfo = statusMap[status] || { label: 'Bilinmiyor', color: 'default' };
    return (
      <Chip
        label={statusInfo.label}
        color={statusInfo.color}
        size="small"
        icon={statusInfo.icon}
      />
    );
  };

  useEffect(() => {
    fetchIntegrationData();
  }, [currentHotelId]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={4}>
        <Box display="flex" alignItems="center" mb={2}>
          <Avatar sx={{ bgcolor: 'primary.main', mr: 2, width: 56, height: 56 }}>
            <SettingsIcon />
          </Avatar>
          <Box>
            <Typography variant="h4" fontWeight="600">
              Entegrasyonlar
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Üçüncü taraf servisleri ile entegrasyonlarınızı yönetin
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Entegrasyon Kartları */}
      <Grid container spacing={3}>
        {integrations.map((integration) => (
          <Grid item xs={12} md={6} lg={4} key={integration.id}>
            <Card 
              sx={{ 
                height: '100%',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
                }
              }}
            >
              <CardContent sx={{ p: 3 }}>
                {/* Header */}
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                  <Box display="flex" alignItems="center">
                    <Avatar 
                      sx={{ 
                        bgcolor: integration.color, 
                        mr: 2,
                        width: 48,
                        height: 48
                      }}
                    >
                      {integration.icon}
                    </Avatar>
                    <Box>
                      <Typography variant="h6" fontWeight="600">
                        {integration.name}
                      </Typography>
                      {getStatusChip(integration.status, integration.connected)}
                    </Box>
                  </Box>
                  
                  <FormControlLabel
                    control={
                      <Switch
                        checked={integration.connected}
                        onChange={() => handleToggleConnection(integration.id)}
                        disabled={integration.status === 'coming_soon'}
                        color="primary"
                      />
                    }
                    label=""
                  />
                </Box>

                {/* Açıklama */}
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {integration.description}
                </Typography>

                {/* İstatistikler */}
                {integration.connected && (
                  <Box mb={2}>
                    <Typography variant="subtitle2" fontWeight="600" sx={{ mb: 1 }}>
                      📊 İstatistikler
                    </Typography>
                    <Box display="flex" gap={2}>
                      {Object.entries(integration.stats).map(([key, value]) => (
                        <Box key={key} textAlign="center">
                          <Typography variant="h6" fontWeight="600" color="primary">
                            {value}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {key === 'reviews' ? 'Yorum' : 
                             key === 'rating' ? 'Puan' :
                             key === 'comments' ? 'Yorum' :
                             key === 'messages' ? 'Mesaj' :
                             key === 'followers' ? 'Takipçi' :
                             key === 'posts' ? 'Gönderi' :
                             key === 'bookings' ? 'Rezervasyon' :
                             key === 'ranking' ? 'Sıralama' : key}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </Box>
                )}

                {/* Özellikler */}
                <Box mb={2}>
                  <Typography variant="subtitle2" fontWeight="600" sx={{ mb: 1 }}>
                    ✨ Özellikler
                  </Typography>
                  <List dense>
                    {integration.features.map((feature, index) => (
                      <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
                        <ListItemIcon sx={{ minWidth: 24 }}>
                          <CheckIcon sx={{ fontSize: 16, color: 'success.main' }} />
                        </ListItemIcon>
                        <ListItemText 
                          primary={feature}
                          primaryTypographyProps={{ variant: 'body2' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>

                {/* Son Senkronizasyon */}
                {integration.connected && integration.lastSync && (
                  <Typography variant="caption" color="text.secondary" sx={{ mb: 2, display: 'block' }}>
                    Son senkronizasyon: {integration.lastSync}
                  </Typography>
                )}

                {/* Aksiyon Butonları */}
                <Box display="flex" gap={1}>
                  {integration.connected && integration.status !== 'coming_soon' && (
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={syncing === integration.id ? <CircularProgress size={16} /> : <SyncIcon />}
                      onClick={() => handleSync(integration.id)}
                      disabled={syncing === integration.id}
                      fullWidth
                    >
                      {syncing === integration.id ? 'Senkronize Ediliyor...' : 'Senkronize Et'}
                    </Button>
                  )}
                  
                  {!integration.connected && integration.status !== 'coming_soon' && (
                    <Button
                      variant="contained"
                      size="small"
                      startIcon={<AddIcon />}
                      onClick={() => handleToggleConnection(integration.id)}
                      fullWidth
                    >
                      Bağlan
                    </Button>
                  )}
                  
                  {integration.status === 'coming_soon' && (
                    <Button
                      variant="outlined"
                      size="small"
                      disabled
                      fullWidth
                    >
                      Yakında Kullanıma Sunulacak
                    </Button>
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Konfigürasyon Dialog */}
      <Dialog
        open={configDialog.open}
        onClose={() => setConfigDialog({ open: false, integration: null })}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center">
            {configDialog.integration && (
              <Avatar
                sx={{
                  bgcolor: configDialog.integration.color,
                  mr: 2,
                  width: 40,
                  height: 40
                }}
              >
                {configDialog.integration.icon}
              </Avatar>
            )}
            <Box>
              <Typography variant="h6">
                {configDialog.integration?.name} Entegrasyonu
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Bağlantı ayarlarını yapılandırın
              </Typography>
            </Box>
          </Box>
        </DialogTitle>

        <DialogContent>
          {configDialog.integration?.id === 'google' && (
            <Box>
              <Alert severity="info" sx={{ mb: 3 }}>
                Google My Business entegrasyonu için Google hesabınızla oturum açmanız gerekiyor.
              </Alert>
              <TextField
                fullWidth
                label="Google My Business Konum ID"
                placeholder="Örn: ChIJN1t_tDeuEmsRUsoyG83frY4"
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="API Anahtarı"
                type="password"
                placeholder="Google API anahtarınızı girin"
                sx={{ mb: 2 }}
              />
            </Box>
          )}

          {configDialog.integration?.id === 'facebook' && (
            <Box>
              <Alert severity="info" sx={{ mb: 3 }}>
                Facebook entegrasyonu için sayfa yönetici yetkileriniz olmalı.
              </Alert>
              <TextField
                fullWidth
                label="Facebook Sayfa ID"
                placeholder="Örn: 123456789012345"
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="Sayfa Erişim Token"
                type="password"
                placeholder="Facebook sayfa token'ınızı girin"
                sx={{ mb: 2 }}
              />
            </Box>
          )}

          {configDialog.integration?.id === 'instagram' && (
            <Box>
              <Alert severity="info" sx={{ mb: 3 }}>
                Instagram Business hesabınızın Facebook sayfasına bağlı olması gerekiyor.
              </Alert>
              <TextField
                fullWidth
                label="Instagram Business Hesap ID"
                placeholder="Örn: 17841400455970028"
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="Instagram Erişim Token"
                type="password"
                placeholder="Instagram API token'ınızı girin"
                sx={{ mb: 2 }}
              />
            </Box>
          )}

          {configDialog.integration?.id === 'yandex' && (
            <Box>
              <Alert severity="info" sx={{ mb: 3 }}>
                Yandex Maps entegrasyonu için işletme hesabınızın doğrulanmış olması gerekiyor.
              </Alert>
              <TextField
                fullWidth
                label="Yandex İşletme ID"
                placeholder="Örn: 1234567890"
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="Yandex API Anahtarı"
                type="password"
                placeholder="Yandex API anahtarınızı girin"
                sx={{ mb: 2 }}
              />
            </Box>
          )}

          {configDialog.integration?.id === 'booking' && (
            <Box>
              <Alert severity="info" sx={{ mb: 3 }}>
                Booking.com Partner Hub hesabınızdan API erişimi almanız gerekiyor.
              </Alert>
              <TextField
                fullWidth
                label="Booking.com Otel ID"
                placeholder="Örn: 123456"
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="API Kullanıcı Adı"
                placeholder="Partner Hub kullanıcı adınız"
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="API Şifresi"
                type="password"
                placeholder="Partner Hub şifreniz"
                sx={{ mb: 2 }}
              />
            </Box>
          )}

          {configDialog.integration?.id === 'tripadvisor' && (
            <Box>
              <Alert severity="info" sx={{ mb: 3 }}>
                TripAdvisor Business Advantage hesabınızdan API erişimi almanız gerekiyor.
              </Alert>
              <TextField
                fullWidth
                label="TripAdvisor Konum ID"
                placeholder="Örn: d123456"
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="API Anahtarı"
                type="password"
                placeholder="TripAdvisor API anahtarınızı girin"
                sx={{ mb: 2 }}
              />
            </Box>
          )}

          {configDialog.integration?.id === 'expedia' && (
            <Box>
              <Alert severity="info" sx={{ mb: 3 }}>
                Expedia Partner Central hesabınızdan API erişimi almanız gerekiyor.
              </Alert>
              <TextField
                fullWidth
                label="Expedia Otel ID"
                placeholder="Örn: 123456"
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="API Kullanıcı Adı"
                placeholder="Partner Central kullanıcı adınız"
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="API Şifresi"
                type="password"
                placeholder="Partner Central şifreniz"
                sx={{ mb: 2 }}
              />
            </Box>
          )}

          {configDialog.integration?.id === 'airbnb' && (
            <Box>
              <Alert severity="info" sx={{ mb: 3 }}>
                Airbnb Host hesabınızdan API erişimi almanız gerekiyor.
              </Alert>
              <TextField
                fullWidth
                label="Airbnb Listing ID"
                placeholder="Örn: 12345678"
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="API Anahtarı"
                type="password"
                placeholder="Airbnb API anahtarınızı girin"
                sx={{ mb: 2 }}
              />
            </Box>
          )}

          {configDialog.integration?.id === 'whatsapp' && (
            <Box>
              <Alert severity="info" sx={{ mb: 3 }}>
                WhatsApp Business API hesabınızdan token almanız gerekiyor.
              </Alert>
              <TextField
                fullWidth
                label="WhatsApp Business Telefon Numarası"
                placeholder="Örn: +905551234567"
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="API Token"
                type="password"
                placeholder="WhatsApp Business API token'ınızı girin"
                sx={{ mb: 2 }}
              />
            </Box>
          )}

          {configDialog.integration?.id === 'telegram' && (
            <Box>
              <Alert severity="info" sx={{ mb: 3 }}>
                Telegram BotFather'dan bot oluşturup token almanız gerekiyor.
              </Alert>
              <TextField
                fullWidth
                label="Bot Username"
                placeholder="Örn: @MyHotelBot"
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="Bot Token"
                type="password"
                placeholder="BotFather'dan aldığınız token"
                sx={{ mb: 2 }}
              />
            </Box>
          )}
        </DialogContent>

        <DialogActions>
          <Button onClick={() => setConfigDialog({ open: false, integration: null })}>
            İptal
          </Button>
          <Button onClick={handleConfigSave} variant="contained">
            Bağlan
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default IntegrationsModern;
