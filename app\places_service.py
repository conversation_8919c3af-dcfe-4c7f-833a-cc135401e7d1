#// Dosya Adı: places_service.py
#// Konum: app/
#// Amaç: Google Places ve Directions API'leri ile etkileşimi yönetir.

import requests
from .config import PLACES_API_KEY

def get_nearby_places(latitude: float, longitude: float, place_type: str = None, keyword: str = None, rank_by_distance: bool = False):
    if not PLACES_API_KEY:
        return {"error": "Places API anahtarı ayarlanmamış."}
    
    base_url = "https://maps.googleapis.com/maps/api/place/nearbysearch/json"
    
    params = {
        "location": f"{latitude},{longitude}",
        "key": PLACES_API_KEY,
        "language": "tr"
    }

    # /// YENİ: Akıllı Sıralama Mantığı ///
    if rank_by_distance:
        # Uzaklığa göre sıralama istendiğinde, 'radius' kullanılamaz.
        # Ve en az bir 'keyword' veya 'type' zorunludur.
        params["rankby"] = "distance"
        if keyword:
            params["keyword"] = keyword
        elif place_type:
            params["keyword"] = place_type.replace('_', ' ')
    else:
        # Varsayılan olarak 5km yarıçapta ara ve öne çıkanlara göre sırala
        params["radius"] = 5000
        if keyword:
            params["keyword"] = keyword
        if place_type:
            params["type"] = place_type

    try:
        response = requests.get(base_url, params=params)
        response.raise_for_status()
        results = response.json()
        
        if results.get("status") == "OK":
            places = []
            for place in results.get("results", [])[:3]: # En fazla 3 sonuç yeterli olacaktır
                places.append({
                    "name": place.get("name"),
                    "address": place.get("vicinity"),
                    "rating": place.get("rating", "N/A"),
                })
            return {"success": True, "places": places}
        else:
            return {"success": False, "error": results.get("status"), "message_to_user": "Aradığınız kriterlere uygun bir yer bulunamadı."}
    except Exception as e:
        return {"success": False, "error": str(e)}

# /// YENİ FONKSİYON: Yol Tarifi ///
def get_directions_to_destination(origin_lat: float, origin_lon: float, destination_query: str, travel_mode: str = "driving"):
    if not PLACES_API_KEY:
        return {"error": "Places API anahtarı ayarlanmamış."}
        
    base_url = "https://maps.googleapis.com/maps/api/directions/json"
    params = {
        "origin": f"{origin_lat},{origin_lon}",
        "destination": destination_query,
        "mode": travel_mode.lower(), # driving, walking, transit, bicycling
        "key": PLACES_API_KEY,
        "language": "tr"
    }

    try:
        response = requests.get(base_url, params=params)
        response.raise_for_status()
        results = response.json()

        if results.get("status") == "OK" and results.get("routes"):
            route = results["routes"][0] # En olası rotayı al
            leg = route["legs"][0]
            
            summary = {
                "total_distance": leg.get("distance", {}).get("text", "Bilinmiyor"),
                "total_duration": leg.get("duration", {}).get("text", "Bilinmiyor"),
                "start_address": leg.get("start_address"),
                "end_address": leg.get("end_address"),
                "steps_summary": [step.get("html_instructions").replace("<b>", "").replace("</b>", "") for step in leg.get("steps", [])[:3]] # İlk 3 adımı özetle
            }
            return {"success": True, "directions": summary, "message_to_user": f"'{destination_query}' hedefine rota bulundu. Toplam mesafe {summary['total_distance']}, tahmini süre {summary['total_duration']}."}
        else:
             return {"success": False, "error": results.get("status"), "message_to_user": f"'{destination_query}' hedefine bir rota bulunamadı."}
    except Exception as e:
        return {"success": False, "error": str(e)}

def find_place_by_text_query(query: str):
    """
    Verilen bir metin sorgusu (örn: "My Plus Hotel Ataşehir İstanbul") ile bir yer arar
    ve o yerin place_id, enlem ve boylamını döndürür.
    """
    if not PLACES_API_KEY:
        return {"error": "Places API anahtarı ayarlanmamış."}
    
    base_url = "https://maps.googleapis.com/maps/api/place/findplacefromtext/json"
    params = {
        "input": query,
        "inputtype": "textquery",
        "fields": "place_id,name,geometry",
        "key": PLACES_API_KEY,
        "language": "tr"
    }

    try:
        response = requests.get(base_url, params=params)
        response.raise_for_status()
        results = response.json()

        if results.get("status") == "OK" and results.get("candidates"):
            place = results["candidates"][0]
            location = place.get("geometry", {}).get("location", {})
            return {
                "success": True,
                "place_id": place.get("place_id"),
                "name": place.get("name"),
                "latitude": location.get("lat"),
                "longitude": location.get("lng")
            }
        elif results.get("status") == "ZERO_RESULTS":
             return {"success": False, "error": "ZERO_RESULTS", "message_to_user": f"'{query}' sorgusuyla eşleşen bir yer bulunamadı."}
        else:
            return {"success": False, "error": results.get("status"), "message_to_user": "Yer bilgileri alınırken bir sorun oluştu."}

    except requests.exceptions.RequestException as e:
        return {"success": False, "error": str(e)}