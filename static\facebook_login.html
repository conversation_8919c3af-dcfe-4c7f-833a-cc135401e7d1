<!DOCTYPE html>
<html>
<head>
<title>Facebook ile Giriş</title>
<style>
    body { font-family: sans-serif; text-align: center; padding-top: 50px; }
    #status { font-size: 1.2em; margin-top: 20px; }
</style>
</head>
<body>

<h1>Facebook Hesabınızı Bağlayın</h1>
<p>Lütfen devam etmek için aşağıdaki butona tıklayın.</p>
<button id="fb-login-btn" style="font-size: 18px; padding: 10px 20px;">Facebook ile Giriş Yap</button>
<div id="status"></div>

<script>
    // URL'den backend adresini ve hotel_id_key'i al
    const urlParams = new URLSearchParams(window.location.search);
    const backendUrl = urlParams.get('backend_url') || 'http://localhost:5000'; // Varsayılan
    const hotelIdKey = urlParams.get('hotel_id_key');
    const facebookAppId = urlParams.get('app_id');

    const statusDiv = document.getElementById('status');
    const loginBtn = document.getElementById('fb-login-btn');

    if (!hotelIdKey || !facebookAppId) {
        statusDiv.textContent = 'Hata: Gerekli parametreler (hotel_id_key, app_id) eksik.';
        loginBtn.disabled = true;
    }

    // Facebook SDK'sını asenkron olarak yükle
    window.fbAsyncInit = function() {
        FB.init({
            appId      : facebookAppId,
            cookie     : true,
            xfbml      : true,
            version    : 'v19.0'
        });
    };

    (function(d, s, id){
        var js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) {return;}
        js = d.createElement(s); js.id = id;
        js.src = "https://connect.facebook.net/en_US/sdk.js";
        fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));

    // Butona tıklama olayını dinle
    loginBtn.onclick = function() {
        statusDiv.textContent = 'Facebook pop-up\'ı açılıyor...';
        FB.login(function(response) {
            if (response.authResponse) {
                statusDiv.textContent = 'Kimlik doğrulama başarılı! Token sunucuya gönderiliyor...';
                // Token'ı backend'e gönder
                sendTokenToBackend(response.authResponse.accessToken);
            } else {
                statusDiv.textContent = 'Kullanıcı girişi iptal etti veya yetkilendirmedi.';
            }
        }, {scope: 'pages_show_list,pages_read_engagement,pages_messaging,read_page_mailboxes'});
    }

    // Alınan token'ı backend'e gönderen fonksiyon
    function sendTokenToBackend(accessToken) {
        fetch(`${backendUrl}/facebook_process_token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
                accessToken: accessToken,
                hotel_id_key: hotelIdKey
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.locations) {
                // Konumları session'a kaydetmek yerine, doğrudan HTML'de listeleyelim
                displayLocationSelector(data.locations);
            } else {
                statusDiv.textContent = 'Hata: ' + (data.error || 'Bilinmeyen bir sunucu hatası.');
            }
        })
        .catch((error) => {
            statusDiv.textContent = 'Sunucuya bağlanırken hata oluştu: ' + error;
        });
    }

    // Konum seçme arayüzünü oluşturan fonksiyon
    function displayLocationSelector(locations) {
        document.body.innerHTML = '<h1>İşletme Sayfanızı Seçin</h1>';
        const select = document.createElement('select');
        select.style.fontSize = '16px';
        select.style.padding = '5px';
        locations.forEach(loc => {
            const option = document.createElement('option');
            option.value = JSON.stringify({page_id: loc.id, page_name: loc.name, access_token: loc.access_token});
            option.textContent = `${loc.name} (${loc.id})`;
            select.appendChild(option);
        });
        document.body.appendChild(select);

        const confirmBtn = document.createElement('button');
        confirmBtn.textContent = 'Bu Sayfayı Onayla';
        confirmBtn.style.fontSize = '18px';
        confirmBtn.style.padding = '10px 20px';
        confirmBtn.style.marginTop = '20px';
        document.body.appendChild(confirmBtn);

        confirmBtn.onclick = function() {
            const selectedData = JSON.parse(select.value);
            statusDiv.textContent = 'Seçiminiz kaydediliyor...';
            document.body.appendChild(statusDiv);
            
            fetch(`${backendUrl}/facebook_finalize_selection`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    hotel_id_key: hotelIdKey,
                    page_id: selectedData.page_id,
                    page_name: selectedData.page_name,
                    page_access_token: selectedData.access_token
                })
            })
            .then(res => res.json())
            .then(data => {
                if(data.message) {
                    document.body.innerHTML = `<h1>Başarılı!</h1><p>${data.message} Bu pencereyi kapatabilirsiniz.</p>`;
                } else {
                    statusDiv.textContent = 'Hata: ' + (data.error || 'Kaydedilemedi.');
                }
            });
        }
    }

</script>

</body>
</html>