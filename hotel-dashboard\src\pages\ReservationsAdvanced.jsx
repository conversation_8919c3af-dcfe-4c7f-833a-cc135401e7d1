import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Chip,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Avatar,
  Grid,
  Divider,
  IconButton,
  Collapse,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar
} from '@mui/material';
import {
  EventNote as ReservationIcon,
  Person as PersonIcon,
  SmartToy as BotIcon,
  Settings as SettingsIcon,
  Chat as ChatIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Schedule as ScheduleIcon,
  Hotel as HotelIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';
import { DataGrid } from '@mui/x-data-grid';
import { useApi } from '../contexts/ApiContext';

const ReservationsAdvanced = () => {
  const { getReservations, updateReservationStatus, getReservationChatHistory, currentHotelId } = useApi();
  const [reservations, setReservations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [updating, setUpdating] = useState(null);
  
  // Dialog states
  const [statusDialog, setStatusDialog] = useState({ open: false, reservation: null });
  const [chatDialog, setChatDialog] = useState({ open: false, reservation: null, chatHistory: [], loading: false });
  const [selectedStatus, setSelectedStatus] = useState('');
  const [cancellationReason, setCancellationReason] = useState('');
  
  // Expanded row state
  const [expandedRow, setExpandedRow] = useState(null);

  const fetchReservations = useCallback(async () => {
    try {
      setLoading(true);
      console.log('Fetching reservations for hotel:', currentHotelId);
      const data = await getReservations(currentHotelId);
      console.log('Reservations data:', data);
      setReservations(data.reservations || []);
      setError(null);
    } catch (err) {
      console.error('Reservations fetch error:', err);
      setError('Rezervasyonlar yüklenirken bir hata oluştu: ' + err.message);
      setReservations([]);
    } finally {
      setLoading(false);
    }
  }, [getReservations, currentHotelId]);

  const handleStatusUpdate = async () => {
    if (!statusDialog.reservation || !selectedStatus) return;

    try {
      setUpdating(statusDialog.reservation.id);
      await updateReservationStatus(
        statusDialog.reservation.id, 
        selectedStatus,
        cancellationReason || null
      );
      
      // Refresh reservations after update
      await fetchReservations();
      
      // Close dialog
      setStatusDialog({ open: false, reservation: null });
      setSelectedStatus('');
      setCancellationReason('');
    } catch (err) {
      console.error('Status update error:', err);
      alert('Durum güncellenirken hata oluştu: ' + err.message);
    } finally {
      setUpdating(null);
    }
  };

  const handleViewChat = async (reservation) => {
    setChatDialog({ open: true, reservation, chatHistory: [], loading: true });
    
    try {
      const chatData = await getReservationChatHistory(reservation.reservation_id);
      setChatDialog(prev => ({
        ...prev,
        chatHistory: chatData.chat_history || [],
        loading: false
      }));
    } catch (err) {
      console.error('Chat history fetch error:', err);
      setChatDialog(prev => ({
        ...prev,
        chatHistory: [],
        loading: false
      }));
      alert('Chat geçmişi yüklenirken hata oluştu: ' + err.message);
    }
  };

  const openStatusDialog = (reservation) => {
    setStatusDialog({ open: true, reservation });
    setSelectedStatus('');
    setCancellationReason('');
  };

  useEffect(() => {
    fetchReservations();
    
    // Her 2 dakikada bir otomatik güncelle
    const interval = setInterval(fetchReservations, 2 * 60 * 1000);
    return () => clearInterval(interval);
  }, [fetchReservations]);

  const getStatusChip = (status) => {
    const statusMap = {
      'HUMAN_CONFIRMED': { label: 'Onaylandı', color: 'success' },
      'AI_PENDING_HUMAN': { label: 'Onay Bekliyor', color: 'warning' },
      'CANCELLED_GUEST': { label: 'Misafir İptal', color: 'error' },
      'CANCELLED_HOTEL': { label: 'Otel İptal', color: 'error' },
      'COMPLETED': { label: 'Tamamlandı', color: 'info' },
      'NO_SHOW': { label: 'Gelmedi', color: 'default' }
    };
    
    const statusInfo = statusMap[status] || { label: status, color: 'default' };
    return <Chip label={statusInfo.label} color={statusInfo.color} size="small" />;
  };

  const getQuickActions = (reservation) => {
    const actions = [];
    
    if (reservation.status === 'AI_PENDING_HUMAN') {
      actions.push(
        <Button
          key="confirm"
          variant="contained"
          size="small"
          color="success"
          startIcon={<CheckIcon />}
          disabled={updating === reservation.id}
          onClick={() => {
            setStatusDialog({ open: true, reservation });
            setSelectedStatus('HUMAN_CONFIRMED');
          }}
          sx={{ mr: 1 }}
        >
          Onayla
        </Button>
      );
      actions.push(
        <Button
          key="reject"
          variant="outlined"
          size="small"
          color="error"
          startIcon={<CloseIcon />}
          disabled={updating === reservation.id}
          onClick={() => openStatusDialog(reservation)}
          sx={{ mr: 1 }}
        >
          Reddet
        </Button>
      );
    }
    
    if (reservation.status === 'HUMAN_CONFIRMED') {
      actions.push(
        <Button
          key="complete"
          variant="outlined"
          size="small"
          color="info"
          startIcon={<CheckIcon />}
          disabled={updating === reservation.id}
          onClick={() => {
            setStatusDialog({ open: true, reservation });
            setSelectedStatus('COMPLETED');
          }}
          sx={{ mr: 1 }}
        >
          Tamamla
        </Button>
      );
    }
    
    // Her zaman mevcut olan aksiyonlar
    actions.push(
      <Button
        key="status"
        variant="outlined"
        size="small"
        startIcon={<EditIcon />}
        disabled={updating === reservation.id}
        onClick={() => openStatusDialog(reservation)}
        sx={{ mr: 1 }}
      >
        Durum Değiştir
      </Button>
    );
    
    if (reservation.session_id_at_booking) {
      actions.push(
        <Button
          key="chat"
          variant="outlined"
          size="small"
          startIcon={<ChatIcon />}
          onClick={() => handleViewChat(reservation)}
        >
          Sohbeti Gör
        </Button>
      );
    }
    
    return actions;
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('tr-TR');
  };

  const formatPrice = (price) => {
    if (!price) return '-';
    return `₺${parseFloat(price).toLocaleString('tr-TR')}`;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={4}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box display="flex" alignItems="center">
            <Avatar sx={{ bgcolor: 'primary.main', mr: 2, width: 56, height: 56 }}>
              <ReservationIcon />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight="600">
                Rezervasyon Yönetimi
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tüm rezervasyonları görüntüleyin ve yönetin
              </Typography>
            </Box>
          </Box>
          
          <Button
            variant="outlined"
            startIcon={<ReservationIcon />}
            onClick={fetchReservations}
            disabled={loading}
          >
            Yenile
          </Button>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Rezervasyon Kartları */}
      {reservations.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 8 }}>
            <ReservationIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" mb={1}>
              Rezervasyon Bulunamadı
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Henüz rezervasyon bulunmuyor
            </Typography>
          </CardContent>
        </Card>
      ) : (
        <Box>
          {reservations.map((reservation) => (
            <Card key={reservation.id} sx={{ mb: 2 }}>
              <CardContent sx={{ p: 3 }}>
                {/* Reservation Header */}
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Box display="flex" alignItems="center">
                    <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                      <PersonIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="h6" fontWeight="600">
                        {reservation.guest_name} {reservation.guest_surname}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Rezervasyon ID: {reservation.reservation_id}
                      </Typography>
                    </Box>
                  </Box>
                  <Box display="flex" alignItems="center" gap={1}>
                    {getStatusChip(reservation.status)}
                    <IconButton
                      onClick={() => setExpandedRow(expandedRow === reservation.id ? null : reservation.id)}
                    >
                      {expandedRow === reservation.id ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                  </Box>
                </Box>

                {/* Reservation Summary */}
                <Grid container spacing={2} mb={2}>
                  <Grid item xs={12} md={3}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <HotelIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        <strong>Oda:</strong> {reservation.room_type}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <ScheduleIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        <strong>Giriş:</strong> {formatDate(reservation.check_in_date)}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <ScheduleIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        <strong>Çıkış:</strong> {formatDate(reservation.check_out_date)}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Typography variant="body2">
                      <strong>Tutar:</strong> {formatPrice(reservation.total_price_at_booking)}
                    </Typography>
                  </Grid>
                </Grid>

                {/* Quick Actions */}
                <Box display="flex" flexWrap="wrap" gap={1}>
                  {getQuickActions(reservation)}
                </Box>

                {/* Expanded Details */}
                <Collapse in={expandedRow === reservation.id}>
                  <Divider sx={{ my: 2 }} />
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" fontWeight="600" mb={1}>
                        İletişim Bilgileri
                      </Typography>
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        <EmailIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                        <Typography variant="body2">{reservation.guest_email || 'Belirtilmemiş'}</Typography>
                      </Box>
                      <Box display="flex" alignItems="center" gap={1}>
                        <PhoneIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                        <Typography variant="body2">{reservation.guest_phone}</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" fontWeight="600" mb={1}>
                        Rezervasyon Detayları
                      </Typography>
                      <Typography variant="body2" mb={1}>
                        <strong>Yetişkin:</strong> {reservation.num_adults} • <strong>Çocuk:</strong> {reservation.num_children || 0}
                      </Typography>
                      <Typography variant="body2" mb={1}>
                        <strong>Gece Sayısı:</strong> {reservation.num_nights}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Rezervasyon Tarihi:</strong> {formatDate(reservation.reservation_time)}
                      </Typography>
                    </Grid>
                    {reservation.special_requests && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" fontWeight="600" mb={1}>
                          Özel İstekler
                        </Typography>
                        <Typography variant="body2">{reservation.special_requests}</Typography>
                      </Grid>
                    )}
                  </Grid>
                </Collapse>
              </CardContent>
            </Card>
          ))}
        </Box>
      )}

      {/* Status Update Dialog */}
      <Dialog
        open={statusDialog.open}
        onClose={() => setStatusDialog({ open: false, reservation: null })}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Rezervasyon Durumu Güncelle
        </DialogTitle>
        <DialogContent>
          <Box mb={2}>
            <Typography variant="body2" color="text.secondary" mb={2}>
              {statusDialog.reservation?.guest_name} {statusDialog.reservation?.guest_surname} - {statusDialog.reservation?.reservation_id}
            </Typography>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Yeni Durum</InputLabel>
              <Select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
              >
                <MenuItem value="HUMAN_CONFIRMED">Onaylandı</MenuItem>
                <MenuItem value="AI_PENDING_HUMAN">Onay Bekliyor</MenuItem>
                <MenuItem value="CANCELLED_GUEST">Misafir İptal</MenuItem>
                <MenuItem value="CANCELLED_HOTEL">Otel İptal</MenuItem>
                <MenuItem value="COMPLETED">Tamamlandı</MenuItem>
                <MenuItem value="NO_SHOW">Gelmedi</MenuItem>
              </Select>
            </FormControl>

            {(selectedStatus === 'CANCELLED_GUEST' || selectedStatus === 'CANCELLED_HOTEL' || selectedStatus === 'NO_SHOW') && (
              <TextField
                fullWidth
                label="İptal/Red Nedeni"
                multiline
                rows={3}
                value={cancellationReason}
                onChange={(e) => setCancellationReason(e.target.value)}
                placeholder="İptal veya red nedenini açıklayın..."
              />
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setStatusDialog({ open: false, reservation: null })}>
            İptal
          </Button>
          <Button
            onClick={handleStatusUpdate}
            variant="contained"
            disabled={!selectedStatus || updating}
          >
            {updating ? <CircularProgress size={20} /> : 'Güncelle'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Chat History Dialog */}
      <Dialog
        open={chatDialog.open}
        onClose={() => setChatDialog({ open: false, reservation: null, chatHistory: [], loading: false })}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center">
            <ChatIcon sx={{ mr: 2 }} />
            <Box>
              <Typography variant="h6">
                Chat Geçmişi
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {chatDialog.reservation?.guest_name} {chatDialog.reservation?.guest_surname} - {chatDialog.reservation?.reservation_id}
              </Typography>
            </Box>
          </Box>
        </DialogTitle>
        <DialogContent>
          {chatDialog.loading ? (
            <Box display="flex" justifyContent="center" py={4}>
              <CircularProgress />
            </Box>
          ) : chatDialog.chatHistory.length === 0 ? (
            <Box textAlign="center" py={4}>
              <ChatIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="body2" color="text.secondary">
                Bu rezervasyon için chat geçmişi bulunamadı
              </Typography>
            </Box>
          ) : (
            <Box sx={{ height: 400, overflow: 'auto' }}>
              <List>
                {chatDialog.chatHistory.map((message, index) => (
                  <ListItem key={index} sx={{ alignItems: 'flex-start' }}>
                    <ListItemAvatar>
                      <Avatar sx={{
                        bgcolor: message.sender === 'USER' ? 'primary.main' :
                                 message.sender === 'AI' ? 'success.main' : 'warning.main'
                      }}>
                        {message.sender === 'USER' ? <PersonIcon /> :
                         message.sender === 'AI' ? <BotIcon /> :
                         <SettingsIcon />}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" gap={1} mb={1}>
                          <Typography variant="subtitle2" fontWeight="600">
                            {message.sender === 'USER' ? chatDialog.reservation?.guest_name :
                             message.sender === 'AI' ? 'AI Asistan' : 'Sistem'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {new Date(message.timestamp).toLocaleString('tr-TR')}
                          </Typography>
                        </Box>
                      }
                      secondary={
                        <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                          {message.message}
                        </Typography>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setChatDialog({ open: false, reservation: null, chatHistory: [], loading: false })}>
            Kapat
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ReservationsAdvanced;
