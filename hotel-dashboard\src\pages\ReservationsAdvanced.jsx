import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Button,
  Chip,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Avatar,
  Grid,
  Divider,
  IconButton,
  Collapse,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar
} from '@mui/material';
import {
  EventNote as ReservationIcon,
  Person as PersonIcon,
  SmartToy as BotIcon,
  Settings as SettingsIcon,
  Chat as ChatIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Schedule as ScheduleIcon,
  Hotel as HotelIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Info as InfoIcon,
  Print as PrintIcon,
  Assignment as AssignmentIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import { DataGrid } from '@mui/x-data-grid';
import { useApi } from '../contexts/ApiContext';

const ReservationsAdvanced = () => {
  const { getReservations, updateReservationStatus, getReservationChatHistory, currentHotelId } = useApi();
  const [reservations, setReservations] = useState([]);
  const [filteredReservations, setFilteredReservations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [updating, setUpdating] = useState(null);

  // Filtreleme states
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  
  // Dialog states
  const [statusDialog, setStatusDialog] = useState({ open: false, reservation: null });
  const [chatDialog, setChatDialog] = useState({ open: false, reservation: null, chatHistory: [], loading: false });
  const [detailsDialog, setDetailsDialog] = useState({ open: false, reservation: null });
  const [selectedStatus, setSelectedStatus] = useState('');
  const [cancellationReason, setCancellationReason] = useState('');
  
  // Expanded row state
  const [expandedRow, setExpandedRow] = useState(null);

  const fetchReservations = useCallback(async () => {
    try {
      setLoading(true);
      console.log('Fetching reservations for hotel:', currentHotelId);
      const data = await getReservations(currentHotelId);
      console.log('Reservations data:', data);
      setReservations(data.reservations || []);
      setError(null);
    } catch (err) {
      console.error('Reservations fetch error:', err);
      setError('Rezervasyonlar yüklenirken bir hata oluştu: ' + err.message);
      setReservations([]);
    } finally {
      setLoading(false);
    }
  }, [getReservations, currentHotelId]);

  // Filtreleme fonksiyonu
  const applyFilters = useCallback(() => {
    let filtered = [...reservations];

    // Durum filtresi
    if (statusFilter !== 'all') {
      filtered = filtered.filter(reservation => reservation.status === statusFilter);
    }

    // Tarih filtresi
    if (dateFilter !== 'all') {
      const today = new Date();
      const filterDate = new Date();

      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          filtered = filtered.filter(reservation => {
            const checkIn = new Date(reservation.check_in_date);
            return checkIn.toDateString() === today.toDateString();
          });
          break;
        case 'week':
          filterDate.setDate(today.getDate() - 7);
          filtered = filtered.filter(reservation => {
            const reservationDate = new Date(reservation.reservation_time);
            return reservationDate >= filterDate;
          });
          break;
        case 'month':
          filterDate.setMonth(today.getMonth() - 1);
          filtered = filtered.filter(reservation => {
            const reservationDate = new Date(reservation.reservation_time);
            return reservationDate >= filterDate;
          });
          break;
        default:
          break;
      }
    }

    // Arama filtresi
    if (searchTerm) {
      filtered = filtered.filter(reservation =>
        reservation.guest_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        reservation.guest_surname?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        reservation.reservation_id?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        reservation.guest_phone?.includes(searchTerm)
      );
    }

    setFilteredReservations(filtered);
    setCurrentPage(1); // Filtreleme sonrası ilk sayfaya dön
  }, [reservations, statusFilter, dateFilter, searchTerm]);

  // Pagination hesaplama
  const getPaginatedReservations = useCallback(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredReservations.slice(startIndex, endIndex);
  }, [filteredReservations, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredReservations.length / itemsPerPage);

  const handleStatusUpdate = async () => {
    if (!statusDialog.reservation || !selectedStatus) return;

    try {
      setUpdating(statusDialog.reservation.id);
      await updateReservationStatus(
        statusDialog.reservation.id, 
        selectedStatus,
        cancellationReason || null
      );
      
      // Refresh reservations after update
      await fetchReservations();
      
      // Close dialog
      setStatusDialog({ open: false, reservation: null });
      setSelectedStatus('');
      setCancellationReason('');
    } catch (err) {
      console.error('Status update error:', err);
      alert('Durum güncellenirken hata oluştu: ' + err.message);
    } finally {
      setUpdating(null);
    }
  };

  const handleViewChat = async (reservation) => {
    setChatDialog({ open: true, reservation, chatHistory: [], loading: true });

    try {
      console.log('Fetching chat history for reservation:', reservation.reservation_id);
      const chatData = await getReservationChatHistory(reservation.reservation_id);
      console.log('Chat data received:', chatData);

      setChatDialog(prev => ({
        ...prev,
        chatHistory: chatData.chat_history || [],
        loading: false
      }));
    } catch (err) {
      console.error('Chat history fetch error:', err);
      setChatDialog(prev => ({
        ...prev,
        chatHistory: [],
        loading: false
      }));
      setError('Chat geçmişi yüklenirken hata oluştu: ' + err.message);
    }
  };

  const openStatusDialog = (reservation) => {
    setStatusDialog({ open: true, reservation });
    setSelectedStatus('');
    setCancellationReason('');
  };

  useEffect(() => {
    fetchReservations();

    // Her 2 dakikada bir otomatik güncelle
    const interval = setInterval(fetchReservations, 2 * 60 * 1000);
    return () => clearInterval(interval);
  }, [fetchReservations]);

  // Filtreleme değiştiğinde uygula
  useEffect(() => {
    applyFilters();
  }, [applyFilters]);

  // Rezervasyonlar değiştiğinde filtreleri uygula
  useEffect(() => {
    setFilteredReservations(reservations);
  }, [reservations]);

  const getStatusChip = (status) => {
    const statusMap = {
      'HUMAN_CONFIRMED': { label: 'Onaylandı', color: 'success' },
      'AI_PENDING_HUMAN': { label: 'Onay Bekliyor', color: 'warning' },
      'CANCELLED_GUEST': { label: 'Misafir İptal', color: 'error' },
      'CANCELLED_HOTEL': { label: 'Otel İptal', color: 'error' },
      'COMPLETED': { label: 'Tamamlandı', color: 'info' },
      'NO_SHOW': { label: 'Gelmedi', color: 'default' }
    };
    
    const statusInfo = statusMap[status] || { label: status, color: 'default' };
    return <Chip label={statusInfo.label} color={statusInfo.color} size="small" />;
  };

  const handleQuickConfirm = async (reservation) => {
    try {
      setUpdating(reservation.id);
      await updateReservationStatus(reservation.id, 'HUMAN_CONFIRMED');
      await fetchReservations();
    } catch (err) {
      console.error('Quick confirm error:', err);
      alert('Onaylama sırasında hata oluştu: ' + err.message);
    } finally {
      setUpdating(null);
    }
  };

  const handleQuickReject = async (reservation) => {
    const reason = prompt('Reddetme nedenini girin:');
    if (!reason) return;

    try {
      setUpdating(reservation.id);
      await updateReservationStatus(reservation.id, 'CANCELLED_HOTEL', reason);
      await fetchReservations();
    } catch (err) {
      console.error('Quick reject error:', err);
      alert('Reddetme sırasında hata oluştu: ' + err.message);
    } finally {
      setUpdating(null);
    }
  };

  const handlePrintReservation = (reservation) => {
    // Print fonksiyonu - rezervasyon detaylarını yazdır
    const printWindow = window.open('', '_blank');
    const printContent = `
      <html>
        <head>
          <title>Rezervasyon Detayları - ${reservation.reservation_id}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; margin-bottom: 20px; }
            .section { margin-bottom: 15px; }
            .label { font-weight: bold; }
            table { width: 100%; border-collapse: collapse; margin-top: 10px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>REZERVASYON DETAYLARI</h1>
            <p>Rezervasyon ID: ${reservation.reservation_id}</p>
          </div>

          <div class="section">
            <h3>Misafir Bilgileri</h3>
            <table>
              <tr><th>Ad Soyad</th><td>${reservation.guest_name} ${reservation.guest_surname}</td></tr>
              <tr><th>Telefon</th><td>${reservation.guest_phone || 'Belirtilmemiş'}</td></tr>
              <tr><th>Email</th><td>${reservation.guest_email || 'Belirtilmemiş'}</td></tr>
            </table>
          </div>

          <div class="section">
            <h3>Rezervasyon Bilgileri</h3>
            <table>
              <tr><th>Oda Tipi</th><td>${reservation.room_type}</td></tr>
              <tr><th>Giriş Tarihi</th><td>${formatDate(reservation.check_in_date)}</td></tr>
              <tr><th>Çıkış Tarihi</th><td>${formatDate(reservation.check_out_date)}</td></tr>
              <tr><th>Gece Sayısı</th><td>${reservation.num_nights}</td></tr>
              <tr><th>Yetişkin</th><td>${reservation.num_adults}</td></tr>
              <tr><th>Çocuk</th><td>${reservation.num_children || 0}</td></tr>
              <tr><th>Toplam Tutar</th><td>${formatPrice(reservation.total_price_at_booking)}</td></tr>
              <tr><th>Durum</th><td>${reservation.status}</td></tr>
              <tr><th>Rezervasyon Tarihi</th><td>${formatDate(reservation.reservation_time)}</td></tr>
            </table>
          </div>

          ${reservation.special_requests ? `
          <div class="section">
            <h3>Özel İstekler</h3>
            <p>${reservation.special_requests}</p>
          </div>
          ` : ''}

          <div class="section">
            <p><strong>Yazdırma Tarihi:</strong> ${new Date().toLocaleString('tr-TR')}</p>
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
  };

  const getQuickActions = (reservation) => {
    const actions = [];

    // Onay bekleyen rezervasyonlar için özel butonlar
    if (reservation.status === 'AI_PENDING_HUMAN') {
      actions.push(
        <Button
          key="confirm"
          variant="contained"
          size="small"
          color="success"
          startIcon={<CheckIcon />}
          disabled={updating === reservation.id}
          onClick={() => handleQuickConfirm(reservation)}
          sx={{ mr: 1 }}
        >
          Onayla
        </Button>
      );
      actions.push(
        <Button
          key="reject"
          variant="outlined"
          size="small"
          color="error"
          startIcon={<CloseIcon />}
          disabled={updating === reservation.id}
          onClick={() => handleQuickReject(reservation)}
          sx={{ mr: 1 }}
        >
          Reddet
        </Button>
      );
    }

    // Her rezervasyon için mevcut olan aksiyonlar
    actions.push(
      <Button
        key="status"
        variant="outlined"
        size="small"
        startIcon={<EditIcon />}
        disabled={updating === reservation.id}
        onClick={() => openStatusDialog(reservation)}
        sx={{ mr: 1 }}
      >
        Rezervasyon Durumu Belirle
      </Button>
    );

    actions.push(
      <Button
        key="details"
        variant="outlined"
        size="small"
        startIcon={<InfoIcon />}
        onClick={() => setDetailsDialog({ open: true, reservation })}
        sx={{ mr: 1 }}
      >
        Rezervasyon Bilgileri
      </Button>
    );

    if (reservation.session_id_at_booking) {
      actions.push(
        <Button
          key="chat"
          variant="outlined"
          size="small"
          startIcon={<ChatIcon />}
          onClick={() => handleViewChat(reservation)}
          sx={{ mr: 1 }}
        >
          Sohbeti Gör
        </Button>
      );
    }

    return actions;
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('tr-TR');
  };

  const formatPrice = (price) => {
    if (!price) return '-';
    return `₺${parseFloat(price).toLocaleString('tr-TR')}`;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={4}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box display="flex" alignItems="center">
            <Avatar sx={{ bgcolor: 'primary.main', mr: 2, width: 56, height: 56 }}>
              <ReservationIcon />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight="600">
                Rezervasyon Yönetimi
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tüm rezervasyonları görüntüleyin ve yönetin
              </Typography>
            </Box>
          </Box>
          
          <Button
            variant="outlined"
            startIcon={<ReservationIcon />}
            onClick={fetchReservations}
            disabled={loading}
          >
            Yenile
          </Button>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Filtreleme Paneli */}
      <Card sx={{ mb: 3 }}>
        <CardContent sx={{ p: 2 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                size="small"
                placeholder="Ad, soyad, telefon veya rezervasyon ID ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Durum Filtresi</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <MenuItem value="all">Tüm Durumlar</MenuItem>
                  <MenuItem value="AI_PENDING_HUMAN">Onay Bekliyor</MenuItem>
                  <MenuItem value="HUMAN_CONFIRMED">Onaylandı</MenuItem>
                  <MenuItem value="CANCELLED_GUEST">Misafir İptal</MenuItem>
                  <MenuItem value="CANCELLED_HOTEL">Otel İptal</MenuItem>
                  <MenuItem value="COMPLETED">Tamamlandı</MenuItem>
                  <MenuItem value="NO_SHOW">Gelmedi</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Tarih Filtresi</InputLabel>
                <Select
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                >
                  <MenuItem value="all">Tüm Tarihler</MenuItem>
                  <MenuItem value="today">Bugün Giriş</MenuItem>
                  <MenuItem value="week">Son 7 Gün</MenuItem>
                  <MenuItem value="month">Son 30 Gün</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <Box display="flex" gap={1}>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => {
                    setStatusFilter('all');
                    setDateFilter('all');
                    setSearchTerm('');
                  }}
                >
                  Temizle
                </Button>
                <Typography variant="body2" sx={{ alignSelf: 'center', ml: 1 }}>
                  {filteredReservations.length} rezervasyon
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Rezervasyon Kartları */}
      {filteredReservations.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 8 }}>
            <ReservationIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" mb={1}>
              {reservations.length === 0 ? 'Rezervasyon Bulunamadı' : 'Filtreye Uygun Rezervasyon Yok'}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {reservations.length === 0 ? 'Henüz rezervasyon bulunmuyor' : 'Farklı filtre seçenekleri deneyin'}
            </Typography>
          </CardContent>
        </Card>
      ) : (
        <Box>
          {getPaginatedReservations().map((reservation) => (
            <Card key={reservation.id} sx={{ mb: 2 }}>
              <CardContent sx={{ p: 3 }}>
                {/* Reservation Header */}
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Box display="flex" alignItems="center">
                    <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                      <PersonIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="h6" fontWeight="600">
                        {reservation.guest_name} {reservation.guest_surname}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Rezervasyon ID: {reservation.reservation_id}
                      </Typography>
                    </Box>
                  </Box>
                  <Box display="flex" alignItems="center" gap={1}>
                    {getStatusChip(reservation.status)}
                    <IconButton
                      onClick={() => setExpandedRow(expandedRow === reservation.id ? null : reservation.id)}
                    >
                      {expandedRow === reservation.id ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                  </Box>
                </Box>

                {/* Reservation Summary */}
                <Grid container spacing={2} mb={2}>
                  <Grid item xs={12} md={3}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <HotelIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        <strong>Oda:</strong> {reservation.room_type}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <ScheduleIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        <strong>Giriş:</strong> {formatDate(reservation.check_in_date)}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <ScheduleIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        <strong>Çıkış:</strong> {formatDate(reservation.check_out_date)}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Typography variant="body2">
                      <strong>Tutar:</strong> {formatPrice(reservation.total_price_at_booking)}
                    </Typography>
                  </Grid>
                </Grid>

                {/* Quick Actions */}
                <Box display="flex" flexWrap="wrap" gap={1}>
                  {getQuickActions(reservation)}
                </Box>

                {/* Expanded Details */}
                <Collapse in={expandedRow === reservation.id}>
                  <Divider sx={{ my: 2 }} />
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" fontWeight="600" mb={1}>
                        İletişim Bilgileri
                      </Typography>
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        <EmailIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                        <Typography variant="body2">{reservation.guest_email || 'Belirtilmemiş'}</Typography>
                      </Box>
                      <Box display="flex" alignItems="center" gap={1}>
                        <PhoneIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                        <Typography variant="body2">{reservation.guest_phone}</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" fontWeight="600" mb={1}>
                        Rezervasyon Detayları
                      </Typography>
                      <Typography variant="body2" mb={1}>
                        <strong>Yetişkin:</strong> {reservation.num_adults} • <strong>Çocuk:</strong> {reservation.num_children || 0}
                      </Typography>
                      <Typography variant="body2" mb={1}>
                        <strong>Gece Sayısı:</strong> {reservation.num_nights}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Rezervasyon Tarihi:</strong> {formatDate(reservation.reservation_time)}
                      </Typography>
                    </Grid>
                    {reservation.special_requests && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" fontWeight="600" mb={1}>
                          Özel İstekler
                        </Typography>
                        <Typography variant="body2">{reservation.special_requests}</Typography>
                      </Grid>
                    )}
                  </Grid>
                </Collapse>
              </CardContent>
            </Card>
          ))}

          {/* Pagination */}
          {totalPages > 1 && (
            <Box display="flex" justifyContent="center" alignItems="center" mt={4}>
              <Button
                variant="outlined"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(currentPage - 1)}
                sx={{ mr: 2 }}
              >
                Önceki
              </Button>

              <Box display="flex" gap={1}>
                {Array.from({ length: totalPages }, (_, index) => {
                  const page = index + 1;
                  const isCurrentPage = page === currentPage;

                  // Sayfa numaralarını sınırla (max 7 sayfa göster)
                  if (totalPages <= 7) {
                    return (
                      <Button
                        key={page}
                        variant={isCurrentPage ? "contained" : "outlined"}
                        onClick={() => setCurrentPage(page)}
                        sx={{ minWidth: 40 }}
                      >
                        {page}
                      </Button>
                    );
                  } else {
                    // Çok sayfa varsa akıllı gösterim
                    if (
                      page === 1 ||
                      page === totalPages ||
                      (page >= currentPage - 1 && page <= currentPage + 1)
                    ) {
                      return (
                        <Button
                          key={page}
                          variant={isCurrentPage ? "contained" : "outlined"}
                          onClick={() => setCurrentPage(page)}
                          sx={{ minWidth: 40 }}
                        >
                          {page}
                        </Button>
                      );
                    } else if (page === currentPage - 2 || page === currentPage + 2) {
                      return <Typography key={page} sx={{ px: 1, alignSelf: 'center' }}>...</Typography>;
                    }
                    return null;
                  }
                })}
              </Box>

              <Button
                variant="outlined"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(currentPage + 1)}
                sx={{ ml: 2 }}
              >
                Sonraki
              </Button>

              <Typography variant="body2" sx={{ ml: 3, color: 'text.secondary' }}>
                Sayfa {currentPage} / {totalPages} ({filteredReservations.length} rezervasyon)
              </Typography>
            </Box>
          )}
        </Box>
      )}

      {/* Status Update Dialog */}
      <Dialog
        open={statusDialog.open}
        onClose={() => setStatusDialog({ open: false, reservation: null })}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Rezervasyon Durumu Güncelle
        </DialogTitle>
        <DialogContent>
          <Box mb={2}>
            <Typography variant="body2" color="text.secondary" mb={2}>
              {statusDialog.reservation?.guest_name} {statusDialog.reservation?.guest_surname} - {statusDialog.reservation?.reservation_id}
            </Typography>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Yeni Durum</InputLabel>
              <Select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
              >
                <MenuItem value="HUMAN_CONFIRMED">Onaylandı</MenuItem>
                <MenuItem value="AI_PENDING_HUMAN">Onay Bekliyor</MenuItem>
                <MenuItem value="CANCELLED_GUEST">Misafir İptal</MenuItem>
                <MenuItem value="CANCELLED_HOTEL">Otel İptal</MenuItem>
                <MenuItem value="COMPLETED">Tamamlandı</MenuItem>
                <MenuItem value="NO_SHOW">Gelmedi</MenuItem>
              </Select>
            </FormControl>

            {(selectedStatus === 'CANCELLED_GUEST' || selectedStatus === 'CANCELLED_HOTEL' || selectedStatus === 'NO_SHOW') && (
              <TextField
                fullWidth
                label="İptal/Red Nedeni"
                multiline
                rows={3}
                value={cancellationReason}
                onChange={(e) => setCancellationReason(e.target.value)}
                placeholder="İptal veya red nedenini açıklayın..."
              />
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setStatusDialog({ open: false, reservation: null })}>
            İptal
          </Button>
          <Button
            onClick={handleStatusUpdate}
            variant="contained"
            disabled={!selectedStatus || updating}
          >
            {updating ? <CircularProgress size={20} /> : 'Güncelle'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Chat History Dialog */}
      <Dialog
        open={chatDialog.open}
        onClose={() => setChatDialog({ open: false, reservation: null, chatHistory: [], loading: false })}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center">
            <ChatIcon sx={{ mr: 2 }} />
            <Box>
              <Typography variant="h6">
                Chat Geçmişi
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {chatDialog.reservation?.guest_name} {chatDialog.reservation?.guest_surname} - {chatDialog.reservation?.reservation_id}
              </Typography>
            </Box>
          </Box>
        </DialogTitle>
        <DialogContent>
          {chatDialog.loading ? (
            <Box display="flex" justifyContent="center" py={4}>
              <CircularProgress />
            </Box>
          ) : chatDialog.chatHistory.length === 0 ? (
            <Box textAlign="center" py={4}>
              <ChatIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="body2" color="text.secondary">
                Bu rezervasyon için chat geçmişi bulunamadı
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Session ID: {chatDialog.reservation?.session_id_at_booking || 'Yok'}
              </Typography>
            </Box>
          ) : (
            <Box sx={{ height: 400, overflow: 'auto', border: '1px solid', borderColor: 'divider', borderRadius: 1, p: 1 }}>
              {chatDialog.chatHistory.map((message, index) => (
                <Box
                  key={index}
                  display="flex"
                  justifyContent={message.sender === 'USER' ? 'flex-end' : 'flex-start'}
                  mb={2}
                >
                  <Box
                    sx={{
                      maxWidth: '70%',
                      p: 2,
                      borderRadius: 2,
                      backgroundColor:
                        message.sender === 'USER' ? 'primary.main' :
                        message.sender === 'AI' ? 'grey.100' : 'warning.light',
                      color: message.sender === 'USER' ? 'white' : 'text.primary'
                    }}
                  >
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      {message.sender === 'USER' ? <PersonIcon sx={{ fontSize: 16 }} /> :
                       message.sender === 'AI' ? <BotIcon sx={{ fontSize: 16 }} /> :
                       <SettingsIcon sx={{ fontSize: 16 }} />}
                      <Typography variant="caption" fontWeight="600">
                        {message.sender === 'USER' ? (chatDialog.reservation?.guest_name + ' ' + chatDialog.reservation?.guest_surname) :
                         message.sender === 'AI' ? 'AI Asistan' : 'Sistem'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(message.timestamp).toLocaleString('tr-TR')}
                      </Typography>
                    </Box>
                    <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                      {message.message}
                    </Typography>

                    {/* Tool kullanımı varsa göster */}
                    {message.tool_name && (
                      <Box mt={1} p={1} sx={{ backgroundColor: 'rgba(0,0,0,0.1)', borderRadius: 1 }}>
                        <Typography variant="caption" fontWeight="600">
                          🔧 {message.tool_name}
                        </Typography>
                        {message.tool_response && (
                          <Typography variant="caption" display="block" sx={{ mt: 0.5 }}>
                            {typeof message.tool_response === 'object'
                              ? JSON.stringify(message.tool_response, null, 2)
                              : message.tool_response}
                          </Typography>
                        )}
                      </Box>
                    )}
                  </Box>
                </Box>
              ))}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setChatDialog({ open: false, reservation: null, chatHistory: [], loading: false })}>
            Kapat
          </Button>
        </DialogActions>
      </Dialog>

      {/* Reservation Details Dialog */}
      <Dialog
        open={detailsDialog.open}
        onClose={() => setDetailsDialog({ open: false, reservation: null })}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Box display="flex" alignItems="center">
              <AssignmentIcon sx={{ mr: 2 }} />
              <Box>
                <Typography variant="h6">
                  Rezervasyon Detayları
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {detailsDialog.reservation?.reservation_id}
                </Typography>
              </Box>
            </Box>
            <Button
              variant="outlined"
              startIcon={<PrintIcon />}
              onClick={() => handlePrintReservation(detailsDialog.reservation)}
            >
              Yazdır
            </Button>
          </Box>
        </DialogTitle>
        <DialogContent>
          {detailsDialog.reservation && (
            <Box>
              {/* Misafir Bilgileri */}
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" fontWeight="600" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                    <PersonIcon sx={{ mr: 1 }} />
                    Misafir Bilgileri
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">Ad Soyad</Typography>
                      <Typography variant="body1" fontWeight="600">
                        {detailsDialog.reservation.guest_name} {detailsDialog.reservation.guest_surname}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">Telefon</Typography>
                      <Typography variant="body1" fontWeight="600">
                        {detailsDialog.reservation.guest_phone || 'Belirtilmemiş'}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">Email</Typography>
                      <Typography variant="body1" fontWeight="600">
                        {detailsDialog.reservation.guest_email || 'Belirtilmemiş'}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">Durum</Typography>
                      <Box mt={0.5}>
                        {getStatusChip(detailsDialog.reservation.status)}
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Rezervasyon Bilgileri */}
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" fontWeight="600" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                    <HotelIcon sx={{ mr: 1 }} />
                    Rezervasyon Bilgileri
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">Oda Tipi</Typography>
                      <Typography variant="body1" fontWeight="600">
                        {detailsDialog.reservation.room_type}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">Giriş Tarihi</Typography>
                      <Typography variant="body1" fontWeight="600">
                        {formatDate(detailsDialog.reservation.check_in_date)}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">Çıkış Tarihi</Typography>
                      <Typography variant="body1" fontWeight="600">
                        {formatDate(detailsDialog.reservation.check_out_date)}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">Gece Sayısı</Typography>
                      <Typography variant="body1" fontWeight="600">
                        {detailsDialog.reservation.num_nights}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">Yetişkin Sayısı</Typography>
                      <Typography variant="body1" fontWeight="600">
                        {detailsDialog.reservation.num_adults}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">Çocuk Sayısı</Typography>
                      <Typography variant="body1" fontWeight="600">
                        {detailsDialog.reservation.num_children || 0}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">Toplam Tutar</Typography>
                      <Typography variant="body1" fontWeight="600" color="primary">
                        {formatPrice(detailsDialog.reservation.total_price_at_booking)}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">Rezervasyon Tarihi</Typography>
                      <Typography variant="body1" fontWeight="600">
                        {formatDate(detailsDialog.reservation.reservation_time)}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Özel İstekler */}
              {detailsDialog.reservation.special_requests && (
                <Card sx={{ mb: 3 }}>
                  <CardContent>
                    <Typography variant="h6" fontWeight="600" sx={{ mb: 2 }}>
                      Özel İstekler
                    </Typography>
                    <Typography variant="body1">
                      {detailsDialog.reservation.special_requests}
                    </Typography>
                  </CardContent>
                </Card>
              )}

              {/* Sistem Bilgileri */}
              <Card>
                <CardContent>
                  <Typography variant="h6" fontWeight="600" sx={{ mb: 2 }}>
                    Sistem Bilgileri
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">Rezervasyon ID</Typography>
                      <Typography variant="body1" fontWeight="600">
                        {detailsDialog.reservation.reservation_id}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">Database ID</Typography>
                      <Typography variant="body1" fontWeight="600">
                        {detailsDialog.reservation.id}
                      </Typography>
                    </Grid>
                    {detailsDialog.reservation.session_id_at_booking && (
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary">Chat Session ID</Typography>
                        <Typography variant="body1" fontWeight="600">
                          {detailsDialog.reservation.session_id_at_booking.substring(0, 16)}...
                        </Typography>
                      </Grid>
                    )}
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">Kaynak</Typography>
                      <Typography variant="body1" fontWeight="600">
                        {detailsDialog.reservation.session_id_at_booking ? 'AI Chat' : 'Manuel'}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            variant="outlined"
            startIcon={<PrintIcon />}
            onClick={() => handlePrintReservation(detailsDialog.reservation)}
          >
            Yazdır
          </Button>
          <Button onClick={() => setDetailsDialog({ open: false, reservation: null })}>
            Kapat
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ReservationsAdvanced;
