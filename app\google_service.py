# app/google_service.py (<PERSON>)

import os
import json
from datetime import datetime
from googleapiclient.discovery import build
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from google.auth.transport.requests import Request
import google.generativeai as genai

# Proje içi importlar
from app.database import get_db_connection
from app.config import (
    GEMINI_API_KEY, MODEL_NAME, GOOGLE_CLIENT_ID, 
    GOOGLE_CLIENT_SECRET, GOOGLE_REDIRECT_URI
)

from app.utils import encrypt_data, decrypt_data

# --- Google API Yapılandırması ---
SCOPES = ['https://www.googleapis.com/auth/business.manage']
ACCOUNTS_API_NAME = 'mybusinessaccountmanagement'
ACCOUNTS_API_VERSION = 'v1'
# Business Info API'sini sadece yorumlar için kull<PERSON>ğız
BUSINESS_INFO_API_NAME = 'mybusinessbusinessinformation'
BUSINESS_INFO_API_VERSION = 'v1'


def get_google_auth_flow():
    """Google OAuth 2.0 akış nesnesini (flow) oluşturur ve döndürür."""
    if not all([GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, GOOGLE_REDIRECT_URI]):
        raise ValueError("Google OAuth ayarları (.env dosyasında) eksik.")
    client_config = {"web": {"client_id": GOOGLE_CLIENT_ID, "client_secret": GOOGLE_CLIENT_SECRET, "auth_uri": "https://accounts.google.com/o/oauth2/auth", "token_uri": "https://oauth2.googleapis.com/token", "redirect_uris": [GOOGLE_REDIRECT_URI]}}
    return Flow.from_client_config(client_config=client_config, scopes=SCOPES, redirect_uri=GOOGLE_REDIRECT_URI)


def build_gmb_service(credentials, service_name, version):
    """Verilen kimlik bilgileriyle genel bir Google API servis nesnesi oluşturur."""
    # static_discovery=False parametresi, bazı ortamlarda keşif belgesi sorunlarını çözebilir.
    return build(service_name, version, credentials=credentials, static_discovery=False)



def list_accounts_and_locations(credentials):
    """Verilen kimlik bilgileriyle kullanıcının yönettiği tüm hesapları ve konumları listeler."""
    try:
        # Sadece Hesap Yönetimi API'sini kullanarak hem hesapları hem de konumları almayı deneyeceğiz.
        # Bu, en temel ve standart yöntemdir.
        accounts_service = build_gmb_service(credentials, ACCOUNTS_API_NAME, ACCOUNTS_API_VERSION)
        
        # 1. Adım: Hesapları al
        accounts_list = accounts_service.accounts().list().execute().get('accounts', [])
        if not accounts_list:
            return {"success": False, "error": "Yönetilebilir İşletme Hesabı bulunamadı."}

        all_locations = []
        # 2. Adım: Her bir hesap için konumları al
        for account in accounts_list:
            account_name = account.get('name')
            # Bir hesabın altındaki konumları yine aynı servis üzerinden listeliyoruz.
            locations_list = accounts_service.accounts().locations().list(parent=account_name).execute().get('locations', [])
            
            for loc in locations_list:
                all_locations.append({
                    "account_id": account.get('accountNumber'),
                    "location_id": loc.get('name').split('/')[-1],
                    "location_name": loc.get('locationName'),
                    "address": "Adres bilgisi bu API'de mevcut değil." # Bu API adres döndürmez
                })

        if not all_locations:
            return {"success": False, "error": "Hesaplarınıza bağlı yönetilebilir bir konum (otel) bulunamadı."}

        return {"success": True, "locations": all_locations}
    except Exception as e:
        import traceback
        print(f"HATA (list_accounts_and_locations): {traceback.format_exc()}")
        return {"success": False, "error": f"Konumlar listelenemedi. Detay: {e}"}


def get_credentials_for_hotel(hotel_id_key: str):
    """Belirli bir otelin kaydedilmiş kimlik bilgilerini veritabanından alıp yeniler."""
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT google_refresh_token_encrypted FROM hotels WHERE hotel_id_key = ?", (hotel_id_key,))
    hotel_data = cursor.fetchone()
    conn.close()
    if not hotel_data or not hotel_data['google_refresh_token_encrypted']:
        raise Exception(f"Otel '{hotel_id_key}' için Google kimlik bilgileri bulunamadı.")
    refresh_token = decrypt_data(hotel_data['google_refresh_token_encrypted'])
    credentials = Credentials(token=None, refresh_token=refresh_token, token_uri="https://oauth2.googleapis.com/token", client_id=GOOGLE_CLIENT_ID, client_secret=GOOGLE_CLIENT_SECRET, scopes=SCOPES)
    credentials.refresh(Request())
    return credentials


def sync_reviews_from_google(hotel_id_key: str):
    """Belirli bir otel için Google'dan yeni yorumları çeker ve veritabanıyla senkronize eder."""
    print(f"INFO: '{hotel_id_key}' için yorum senkronizasyonu başlatılıyor...")
    try:
        credentials = get_credentials_for_hotel(hotel_id_key)
        service = build_gmb_service(credentials, BUSINESS_INFO_API_NAME, BUSINESS_INFO_API_VERSION)
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT gmb_location_id FROM hotels WHERE hotel_id_key = ?", (hotel_id_key,))
        hotel_ids = cursor.fetchone()
        if not hotel_ids or not hotel_ids['gmb_location_id']: raise ValueError(f"Otel '{hotel_id_key}' için GMB Konum ID'si veritabanında eksik.")
        parent_for_reviews = f"locations/{hotel_ids['gmb_location_id']}"
        review_list_request = service.locations().reviews().list(parent=parent_for_reviews, pageSize=50)
        response = review_list_request.execute()
        reviews = response.get('reviews', [])
        if not reviews:
            conn.close()
            return {"success": True, "message": "Yeni yorum bulunamadı.", "new_reviews": 0, "updated_reviews": 0}
        new_reviews_count, updated_reviews_count = 0, 0
        for review in reviews:
            review_id = review.get('name').split('/')[-1]
            cursor.execute("SELECT id, update_time FROM google_reviews WHERE review_id = ?", (review_id,))
            existing_review = cursor.fetchone()
            review_comment = review.get('comment', '')
            if review_comment is None: review_comment = ''
            if existing_review:
                if existing_review['update_time'] != review.get('updateTime'):
                    cursor.execute("UPDATE google_reviews SET star_rating = ?, comment = ?, update_time = ?, reply_comment = ?, reply_update_time = ? WHERE review_id = ?", (review.get('starRating'), review_comment, review.get('updateTime'), review.get('reply', {}).get('comment'), review.get('reply', {}).get('updateTime'), review_id))
                    updated_reviews_count += 1
            else:
                cursor.execute("INSERT INTO google_reviews (review_id, reviewer_name, reviewer_photo_url, star_rating, comment, create_time, update_time, reply_comment, reply_update_time, fetched_at, is_replied) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", (review_id, review.get('reviewer', {}).get('displayName'), review.get('reviewer', {}).get('profilePhotoUrl'), review.get('starRating'), review_comment, review.get('createTime'), review.get('updateTime'), review.get('reply', {}).get('comment'), review.get('reply', {}).get('updateTime'), datetime.now().isoformat(), 1 if 'reply' in review else 0))
                new_reviews_count += 1
        conn.commit()
        conn.close()
        message = f"Senkronizasyon tamamlandı. Yeni: {new_reviews_count}, Güncellenen: {updated_reviews_count}."
        print(f"SUCCESS: {message}")
        return {"success": True, "message": message, "new_reviews": new_reviews_count, "updated_reviews": updated_reviews_count}
    except Exception as e:
        print(f"CRITICAL: Google yorumları çekilirken hata: {e}")
        return {"success": False, "error": str(e)}

def post_reply_to_google(hotel_id_key: str, review_id: str, reply_text: str):
    """Belirli bir yoruma Google üzerinde yanıt gönderir ve yerel DB'yi günceller."""
    print(f"INFO: '{hotel_id_key}' için yanıt gönderiliyor. Yorum ID: {review_id}")
    try:
        credentials = get_credentials_for_hotel(hotel_id_key)
        service = build_gmb_service(credentials, BUSINESS_INFO_API_NAME, BUSINESS_INFO_API_VERSION)
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT gmb_location_id FROM hotels WHERE hotel_id_key = ?", (hotel_id_key,))
        hotel_ids = cursor.fetchone()
        if not hotel_ids: raise ValueError("Otel bulunamadı.")
        review_name = f"locations/{hotel_ids['gmb_location_id']}/reviews/{review_id}"
        update_reply_request = service.locations().reviews().updateReply(name=review_name, body={'comment': reply_text})
        response = update_reply_request.execute()
        cursor.execute("UPDATE google_reviews SET is_replied = 1, reply_comment = ?, reply_update_time = ? WHERE review_id = ?", (response.get('comment'), response.get('updateTime'), review_id))
        conn.commit()
        conn.close()
        return {"success": True, "message": "Yorum başarıyla yanıtlandı."}
    except Exception as e:
        print(f"CRITICAL: Google'a yanıt gönderilirken hata: {e}")
        return {"success": False, "error": str(e)}

def generate_ai_response_draft(review_comment: str, star_rating: int, hotel_name: str):
    """AI kullanarak bir yanıt taslağı oluşturur."""
    print(f"INFO: AI yanıt taslağı oluşturuluyor. Puan: {star_rating}")
    if star_rating >= 4: tone_instruction = "Pozitif ve teşekkür dolu bir dil kullan. Misafirin belirttiği olumlu noktalara değin ve onu tekrar davet et."
    elif star_rating == 3: tone_instruction = "Dengeli bir dil kullan. Hem teşekkür et hem de belirttiği olumsuz noktalar için üzgün olduğunu belirt ve iyileştirme sözü ver. Savunmacı olma."
    else: tone_instruction = "Son derece empatik ve özür dileyen bir dil kullan. Sorunu anladığını göster, sorumluluk al ve misafiri durumu telafi etmek için sizinle özelden iletişime geçmeye davet et. Asla bahane üretme veya müşteriyi suçlama."
    prompt = f"""Sen, '{hotel_name}' otelinin müşteri ilişkileri müdürüsün. Aşağıdaki müşteri yorumuna profesyonel bir yanıt taslağı hazırla. Kurallar: 1. Yanıtın tonu şu talimata uygun olmalı: "{tone_instruction}"\n2. Yanıt kısa ve öz olmalı (2-4 cümle).\n3. Yanıtı "Saygılarımızla, {hotel_name} Yönetimi" ile bitir.\n4. SADECE yanıt metnini döndür, başka hiçbir açıklama ekleme.\n\nMüşteri Yorumu (Puan: {star_rating}/5):\n"{review_comment}"\n\nYanıt Taslağı:"""
    try:
        model = genai.GenerativeModel(MODEL_NAME)
        response = model.generate_content(prompt)
        draft = response.text.strip()
        return {"success": True, "draft": draft}
    except Exception as e:
        print(f"CRITICAL: AI yanıt taslağı oluşturulurken hata: {e}")
        return {"success": False, "error": str(e)}