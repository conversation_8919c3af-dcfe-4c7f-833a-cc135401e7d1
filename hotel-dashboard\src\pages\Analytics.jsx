import React from 'react';
import {
  Box,
  Typography,
  Card,
  Card<PERSON>ontent,
  Grid,
  Button
} from '@mui/material';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Responsive<PERSON><PERSON>r,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell
} from 'recharts';

const Analytics = () => {
  // Mock data for charts
  const monthlyData = [
    { month: 'Oca', reservations: 45, chats: 120 },
    { month: 'Şub', reservations: 52, chats: 135 },
    { month: 'Mar', reservations: 48, chats: 128 },
    { month: 'Nis', reservations: 61, chats: 156 },
    { month: 'May', reservations: 55, chats: 142 },
    { month: 'Haz', reservations: 67, chats: 178 }
  ];

  const statusData = [
    { name: '<PERSON><PERSON><PERSON><PERSON>', value: 45, color: '#4caf50' },
    { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', value: 23, color: '#ff9800' },
    { name: '<PERSON>pta<PERSON>', value: 12, color: '#f44336' },
    { name: '<PERSON><PERSON><PERSON><PERSON>', value: 67, color: '#2196f3' }
  ];

  return (
    <Box>
      <Typography variant="h4" gutterBottom fontWeight="bold">
        Analiz & Raporlar
      </Typography>
      <Typography variant="body1" color="text.secondary" mb={3}>
        Detaylı istatistikler ve AI analiz raporları.
      </Typography>

      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Aylık Rezervasyon ve Chat Trendi
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={monthlyData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="month" stroke="#fff" />
                  <YAxis stroke="#fff" />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: '#1a1d3a', 
                      border: '1px solid rgba(255,255,255,0.1)' 
                    }} 
                  />
                  <Bar dataKey="reservations" fill="#2196f3" name="Rezervasyonlar" />
                  <Bar dataKey="chats" fill="#ff9800" name="Chat Oturumları" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Rezervasyon Durumları
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                AI Analiz Raporları
              </Typography>
              <Typography variant="body2" color="text.secondary" mb={2}>
                Son haftalık müşteri etkileşim analizi
              </Typography>
              <Button variant="contained" fullWidth>
                Haftalık Rapor Oluştur
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Performans Metrikleri
              </Typography>
              <Box>
                <Typography variant="body2" gutterBottom>
                  Ortalama Yanıt Süresi: <strong>2.3 dakika</strong>
                </Typography>
                <Typography variant="body2" gutterBottom>
                  Müşteri Memnuniyeti: <strong>4.8/5</strong>
                </Typography>
                <Typography variant="body2" gutterBottom>
                  Dönüşüm Oranı: <strong>43%</strong>
                </Typography>
                <Typography variant="body2">
                  AI Doğruluk Oranı: <strong>94%</strong>
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Analytics;
