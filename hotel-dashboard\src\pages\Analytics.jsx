import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  Chip
} from '@mui/material';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { useApi } from '../contexts/ApiContext';

const Analytics = () => {
  const { getAnalysisReports, getAnalysisReportDetail, triggerWeeklyAnalysis, getDashboardTotals, currentHotelId } = useApi();
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [triggeringAnalysis, setTriggeringAnalysis] = useState(false);
  const [dashboardData, setDashboardData] = useState(null);

  // Mock data for charts (will be replaced with real data)
  const monthlyData = [
    { month: 'Oca', reservations: 45, chats: 120 },
    { month: 'Şub', reservations: 52, chats: 135 },
    { month: 'Mar', reservations: 48, chats: 128 },
    { month: 'Nis', reservations: 61, chats: 156 },
    { month: 'May', reservations: 55, chats: 142 },
    { month: 'Haz', reservations: 67, chats: 178 }
  ];

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      console.log('Fetching analytics data for hotel:', currentHotelId);

      // Get analysis reports
      const reportsData = await getAnalysisReports();
      console.log('Analysis reports:', reportsData);
      setReports(reportsData.reports || []);

      // Get dashboard totals for status data
      const totalsData = await getDashboardTotals(currentHotelId);
      setDashboardData(totalsData);

      setError(null);
    } catch (err) {
      console.error('Analytics data fetch error:', err);
      setError('Analiz verileri yüklenirken bir hata oluştu: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleTriggerAnalysis = async () => {
    try {
      setTriggeringAnalysis(true);
      await triggerWeeklyAnalysis();
      alert('Haftalık analiz başlatıldı. Birkaç dakika sonra raporlar güncellenecek.');
      // Refresh reports after a delay
      setTimeout(fetchAnalyticsData, 5000);
    } catch (err) {
      console.error('Trigger analysis error:', err);
      alert('Analiz başlatılırken hata oluştu: ' + err.message);
    } finally {
      setTriggeringAnalysis(false);
    }
  };

  useEffect(() => {
    fetchAnalyticsData();
    // Sadece sayfa yüklendiğinde çek, otomatik yenileme yok
  }, [currentHotelId]);

  // Generate status data from dashboard data
  const statusData = dashboardData ? [
    { name: 'Onaylandı', value: dashboardData.total_confirmed_reservations, color: '#4caf50' },
    { name: 'AI Başlatılan', value: dashboardData.total_ai_initiated_reservations, color: '#ff9800' },
    { name: 'Toplam Chat', value: dashboardData.total_chat_sessions, color: '#2196f3' }
  ] : [
    { name: 'Onaylandı', value: 45, color: '#4caf50' },
    { name: 'Bekliyor', value: 23, color: '#ff9800' },
    { name: 'İptal', value: 12, color: '#f44336' },
    { name: 'Tamamlandı', value: 67, color: '#2196f3' }
  ];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom fontWeight="bold">
        Analiz & Raporlar
      </Typography>
      <Typography variant="body1" color="text.secondary" mb={3}>
        Detaylı istatistikler ve AI analiz raporları.
      </Typography>

      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Aylık Rezervasyon ve Chat Trendi
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={monthlyData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="month" stroke="#fff" />
                  <YAxis stroke="#fff" />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: '#1a1d3a', 
                      border: '1px solid rgba(255,255,255,0.1)' 
                    }} 
                  />
                  <Bar dataKey="reservations" fill="#2196f3" name="Rezervasyonlar" />
                  <Bar dataKey="chats" fill="#ff9800" name="Chat Oturumları" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Rezervasyon Durumları
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                AI Analiz Raporları
              </Typography>
              <Typography variant="body2" color="text.secondary" mb={2}>
                Son haftalık müşteri etkileşim analizi
              </Typography>

              {reports.length > 0 ? (
                <List dense>
                  {reports.slice(0, 3).map((report) => (
                    <ListItem key={report.id} divider>
                      <ListItemText
                        primary={`${report.report_type} Raporu`}
                        secondary={
                          <Box>
                            <Typography variant="caption" display="block">
                              {new Date(report.generated_at).toLocaleDateString('tr-TR')}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {report.report_summary_text}
                            </Typography>
                          </Box>
                        }
                      />
                      <Chip
                        label="Görüntüle"
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary" mb={2}>
                  Henüz analiz raporu bulunmuyor.
                </Typography>
              )}

              <Button
                variant="contained"
                fullWidth
                disabled={triggeringAnalysis}
                onClick={handleTriggerAnalysis}
              >
                {triggeringAnalysis ? 'Analiz Başlatılıyor...' : 'Haftalık Rapor Oluştur'}
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Performans Metrikleri
              </Typography>
              <Box>
                <Typography variant="body2" gutterBottom>
                  Toplam Chat Oturumu: <strong>{dashboardData?.total_chat_sessions || 0}</strong>
                </Typography>
                <Typography variant="body2" gutterBottom>
                  Onaylı Rezervasyon: <strong>{dashboardData?.total_confirmed_reservations || 0}</strong>
                </Typography>
                <Typography variant="body2" gutterBottom>
                  Dönüşüm Oranı: <strong>
                    {dashboardData?.total_chat_sessions > 0
                      ? Math.round((dashboardData.total_ai_initiated_reservations / dashboardData.total_chat_sessions) * 100)
                      : 0}%
                  </strong>
                </Typography>
                <Typography variant="body2">
                  AI Başlatılan Rezervasyon: <strong>{dashboardData?.total_ai_initiated_reservations || 0}</strong>
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Analytics;
