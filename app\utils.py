# app/utils.py (<PERSON> ve Güncellenmiş Kod - Şifreleme Fonksiyonları Eklendi)

import smtplib
import json
from . import socketio, active_chat_sessions, session_lock, otel_configurations, otel_configs_lock
import time
from email.mime.text import MIMEText
from email.header import Header
from datetime import datetime
#from queue import Full
from dateutil.parser import parse as dateutil_parse
from cryptography.fernet import Fernet
import re
from . import socketio
# Bu istemci, app/__init__.py dosyasında oluşturuldu ve yapılandırıldı.
# Tüm uygulama bu tek istemciyi kullanacak.
from . import redis_client, session_lock
from app import app_logger

# config modülünden SMTP ve Şifreleme ayarlarını içe aktar
from .config import (
    SMTP_SERVER, SMTP_PORT_STR, SMTP_SENDER_EMAIL, 
    SMTP_SENDER_PASSWORD, ENCRYPTION_KEY,SESSION_REDIS_PREFIX
)

# --- Şifreleme Yardımcıları ---
# Şifreleme anahtarı varsa, bir Fernet nesnesi oluştur
cipher_suite = Fernet(ENCRYPTION_KEY.encode()) if ENCRYPTION_KEY else None

# === REDIS OTURUM YÖNETİMİ YARDIMCILARI ===

# Redis'te her oturumu temsil edecek HASH için bir anahtar öneki
SESSION_REDIS_TTL = 3 * 60 * 60  # Oturumları Redis'te 3 saat tut

# === STATELESS ARCHITECTURE İYİLEŞTİRMELERİ ===
# Tüm geçici durum bilgileri Redis'te TTL ile tutulacak
ACTIVE_SESSIONS_TTL = 30 * 60  # Aktif session listesi 30 dakika
WORKER_STATE_TTL = 5 * 60      # Worker durumu 5 dakika
TEMP_DATA_TTL = 10 * 60        # Geçici veriler 10 dakika

def save_session_to_redis(session_id: str, session_data: dict):
    if not redis_client: return
    try:
        redis_key = f"{SESSION_REDIS_PREFIX}{session_id}"
        
        # === YENİ: Oturum durumunu (presence) veriye ekle ===
        # Bir oturum kaydedildiğinde, varsayılan durumu 'active' olsun.
        if 'presence' not in session_data:
            session_data['presence'] = 'active'
        # =================================================

        redis_client.setex(redis_key, SESSION_REDIS_TTL, json.dumps(session_data, default=str))
        app_logger.info(f"REDIS_SAVE: Oturum {session_id[:8]} '{session_data['presence']}' durumuyla kaydedildi/güncellendi.")
    except Exception as e:
        app_logger.exception(f"HATA (save_session_to_redis):")

def get_session_from_redis(session_id: str) -> dict | None:
    if not redis_client: return None
    try:
        redis_key = f"{SESSION_REDIS_PREFIX}{session_id}"
        session_json = redis_client.get(redis_key)
        if session_json:
            redis_client.expire(redis_key, SESSION_REDIS_TTL) # Oturuma erişildi, ömrünü uzat.
            return json.loads(session_json)
        return None
    except Exception as e:
        print(f"HATA (get_session_from_redis): {e}")
        return None

def delete_session_from_redis(session_id: str):
    if not redis_client: return
    try:
        redis_key = f"{SESSION_REDIS_PREFIX}{session_id}"
        redis_client.delete(redis_key)
        print(f"REDIS_DELETE: Oturum {session_id[:8]} Redis'ten silindi.")
    except Exception as e:
        print(f"HATA (delete_session_from_redis): {e}")

# === STATELESS ARCHITECTURE HELPER FUNCTIONS ===

def set_session_presence(session_id: str, presence: str):
    """
    Oturum durumunu (active/passive) günceller - Stateless
    """
    if not redis_client: return
    try:
        redis_key = f"{SESSION_REDIS_PREFIX}{session_id}"
        session_data = get_session_from_redis(session_id)
        if session_data:
            session_data['presence'] = presence
            session_data['last_activity_time'] = time.time()
            redis_client.setex(redis_key, SESSION_REDIS_TTL, json.dumps(session_data, default=str))
            app_logger.info(f"REDIS_UPDATE: Session {session_id[:8]} presence -> {presence}")
    except Exception as e:
        app_logger.exception(f"Error updating session presence: {e}")

def cleanup_expired_sessions():
    """
    Süresi dolmuş oturumları temizler - Stateless
    Redis TTL otomatik temizlik yapar ama bu fonksiyon manuel temizlik için
    """
    if not redis_client: return
    try:
        pattern = f"{SESSION_REDIS_PREFIX}*"
        keys = redis_client.keys(pattern)

        current_time = time.time()
        cleaned_count = 0

        for key in keys:
            try:
                session_data = json.loads(redis_client.get(key) or '{}')
                last_activity = session_data.get('last_activity_time', 0)

                # 3 saatten eski oturumları temizle
                if current_time - last_activity > SESSION_REDIS_TTL:
                    redis_client.delete(key)
                    cleaned_count += 1
            except:
                # Bozuk veri varsa sil
                redis_client.delete(key)
                cleaned_count += 1

        if cleaned_count > 0:
            app_logger.info(f"CLEANUP: {cleaned_count} expired session cleaned")

    except Exception as e:
        app_logger.exception(f"Error in cleanup_expired_sessions: {e}")

def get_worker_id():
    """
    Worker ID'sini döndürür - Stateless architecture için
    """
    import os
    return f"worker_{os.getpid()}"

def register_worker_heartbeat():
    """
    Worker'ın canlı olduğunu Redis'e bildirir - Stateless
    """
    if not redis_client: return
    try:
        worker_id = get_worker_id()
        redis_key = f"worker_heartbeat:{worker_id}"
        heartbeat_data = {
            'worker_id': worker_id,
            'last_heartbeat': time.time(),
            'status': 'active'
        }
        redis_client.setex(redis_key, WORKER_STATE_TTL, json.dumps(heartbeat_data))
    except Exception as e:
        app_logger.exception(f"Error registering worker heartbeat: {e}")

def store_temp_data(key: str, data: dict, ttl: int = TEMP_DATA_TTL):
    """
    Geçici veriyi Redis'te saklar - Stateless
    Fallback: Redis yoksa SQLite'ta geçici tablo kullan
    """
    if redis_client:
        try:
            redis_key = f"temp_data:{key}"
            redis_client.setex(redis_key, ttl, json.dumps(data, default=str))
            return
        except Exception as e:
            app_logger.exception(f"Redis temp data store error: {e}")

    # Fallback: SQLite geçici tablo
    try:
        from app.database import get_db_connection
        conn = get_db_connection()
        cursor = conn.cursor()

        # Geçici tablo oluştur (yoksa)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS temp_data (
                key TEXT PRIMARY KEY,
                data TEXT,
                expires_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Veriyi kaydet
        import datetime
        expires_at = datetime.datetime.now() + datetime.timedelta(seconds=ttl)
        cursor.execute("""
            INSERT OR REPLACE INTO temp_data (key, data, expires_at)
            VALUES (?, ?, ?)
        """, (key, json.dumps(data, default=str), expires_at))

        conn.commit()
        conn.close()
        app_logger.info(f"Temp data stored in SQLite fallback: {key}")

    except Exception as e:
        app_logger.exception(f"SQLite temp data fallback error: {e}")

def get_temp_data(key: str) -> dict:
    """
    Geçici veriyi Redis'ten alır - Stateless
    Fallback: Redis yoksa SQLite'tan al
    """
    if redis_client:
        try:
            redis_key = f"temp_data:{key}"
            data = redis_client.get(redis_key)
            return json.loads(data) if data else {}
        except Exception as e:
            app_logger.exception(f"Redis temp data get error: {e}")

    # Fallback: SQLite geçici tablo
    try:
        from app.database import get_db_connection
        conn = get_db_connection()
        cursor = conn.cursor()

        # Süresi dolmuş verileri temizle
        cursor.execute("DELETE FROM temp_data WHERE expires_at < CURRENT_TIMESTAMP")

        # Veriyi al
        cursor.execute("SELECT data FROM temp_data WHERE key = ? AND expires_at > CURRENT_TIMESTAMP", (key,))
        row = cursor.fetchone()

        conn.close()

        if row:
            return json.loads(row[0])
        return {}

    except Exception as e:
        app_logger.exception(f"SQLite temp data fallback error: {e}")
        return {}

def check_production_readiness():
    """
    Production hazırlık durumunu kontrol eder
    """
    issues = []

    # Redis kontrolü
    if not redis_client:
        issues.append("Redis connection not available - using SQLite fallback")

    # Database kontrolü
    try:
        from app.database import get_db_connection
        conn = get_db_connection()
        conn.close()
    except Exception as e:
        issues.append(f"Database connection error: {e}")

    # Environment kontrolü
    import os
    if os.getenv('FLASK_ENV') != 'production':
        issues.append("FLASK_ENV not set to 'production'")

    return issues

def split_text_into_sentences(text: str) -> list[str]:
    """
    Bir metni, karşılaştığı tüm yaygın noktalama işaretlerine göre böler.
    Her parçanın sonunda kendi noktalama işaretini korur.
    Örnek: "Merhaba, nasılsınız? Ben iyiyim." -> ["Merhaba,", "nasılsınız?", "Ben iyiyim."]
    """
    if not text:
        return []

    # Metindeki çoklu boşlukları ve satır sonlarını tek boşluğa indirge
    text = re.sub(r'\s+', ' ', text).strip()
    
    # Eğer metin boşsa, boş liste döndür
    if not text:
        return []

    # Regex Deseni Açıklaması:
    # (?<=[.?!,;:]) : Bu bir "pozitif lookbehind"dır. Anlamı: "şunlardan biriyle biten bir konumu bul".
    #                  Noktalama işaretinin kendisini bölme işlemine dahil etmez, sadece konum olarak kullanır.
    # \s*             : Bu konumdan sonra gelen sıfır veya daha fazla boşluk karakterini yakalar.
    # Bu desen, metni her bir noktalama işaretinin hemen sonrasından böler.
    
    # Önce tüm potansiyel bölme noktalarını bulalım
    # Regex, metni bu desenlere göre böler ve deseni de listeye dahil eder.
    # Örn: "Merhaba, nasılsınız?" -> ["Merhaba", ",", " nasılsınız?"]
    chunks = re.split(r'([.?!,;:])', text)

    # Şimdi bu parçaları anlamlı bir şekilde birleştirelim
    result = []
    # i'yi 2'şer adımla ilerletiyoruz çünkü her metin parçasını takip eden bir noktalama işareti var.
    for i in range(0, len(chunks) - 1, 2):
        # Metin parçası + hemen ardından gelen noktalama işareti
        sentence = (chunks[i] + chunks[i+1]).strip()
        if sentence:
            result.append(sentence)

    # Eğer listenin sonunda tek bir metin parçası kaldıysa (noktalama ile bitmeyen)
    if len(chunks) % 2 == 1 and chunks[-1].strip():
        result.append(chunks[-1].strip())
        
    # Eğer regex hiçbir şey bulamadıysa (noktalama işareti yoksa), orijinal metni döndür
    return result if result else [text]

def encrypt_data(data: str) -> bytes:
    """Verilen metni şifreler."""
    if not cipher_suite:
        raise ValueError("Şifreleme anahtarı (ENCRYPTION_KEY) ayarlanmamış.")
    return cipher_suite.encrypt(data.encode())


def decrypt_data(encrypted_data: bytes) -> str:
    """Şifrelenmiş veriyi çözer."""
    if not cipher_suite:
        raise ValueError("Şifreleme anahtarı (ENCRYPTION_KEY) ayarlanmamış.")
    return cipher_suite.decrypt(encrypted_data).decode()



def broadcast_socketio_event(event_type: str, data: dict):
    """Merkezi Socket.IO yayın fonksiyonu."""
    socketio.emit(event_type, data) # namespace belirtmeye gerek yok, default'a gönderir.
    print(f"SOCKET.IO BROADCAST -> Event: {event_type}")

def broadcast_active_chats_update():
    """
    Redis'teki SADECE 'aktif' durumdaki sohbet oturumlarını tarar ve TAM listeyi
    tüm istemcilere yayınlar. 'passive' durumdakiler listeye dahil edilmez.
    """
    if not redis_client: return

    active_list = []
    try:
        session_keys = redis_client.keys(f"{SESSION_REDIS_PREFIX}*")
        
        if not session_keys:
            socketio.emit('active_chats_updated', {"active_chats": [], "count": 0})
            app_logger.info("SOCKET_EMIT: Aktif sohbet yok, boş liste gönderildi.")
            return

        session_data_list = redis_client.mget(session_keys)
        for i, session_json in enumerate(session_data_list):
            if session_json:
                try:
                    s_data = json.loads(session_json)
                    
                    # === KRİTİK FİLTRELEME ===
                    # Sadece 'presence' durumu 'active' olanları listeye ekle.
                    if s_data.get('presence') == 'active':
                        session_id = session_keys[i].replace(SESSION_REDIS_PREFIX, "")
                        
                        # Sadece web widget'tan gelenleri listelemeye devam edelim
                        if s_data.get('source') == 'web_widget':
                             active_list.append({
                                "session_id": session_id,
                                "source": s_data.get('source'),
                                "hotel_name": s_data.get('hotelName'),
                                "start_time": s_data.get('start_time'),
                                "hotel_config_key": s_data.get('hotel_config_key')
                            })
                except Exception as e:
                    app_logger.exception(f"HATA: broadcast_active_chats_update içinde Redis verisi parse edilemedi:")
    except Exception as e:
        app_logger.exception(f"HATA (broadcast):")

    payload = {"active_chats": active_list, "count": len(active_list)}
    socketio.emit('active_chats_updated', payload)
    app_logger.info(f"SOCKET_EMIT: 'active_chats_updated' gönderildi. {len(active_list)} aktif sohbet.")

# --- Tarih Formatlama ---
def validate_and_format_date(date_str: str) -> str | None:
    # ... (Bu fonksiyon değişmedi) ...
    if not date_str:
        return None
    try:
        return datetime.strptime(date_str, '%Y-%m-%d').strftime('%Y-%m-%d')
    except ValueError:
        try:
            return dateutil_parse(date_str).strftime('%Y-%m-%d')
        except:
            return None

# --- E-posta Gönderimi ---
def send_reservation_email(to_email: str, guest_name: str, reservation_details: dict):
    # ... (Bu fonksiyon değişmedi) ...
    if not to_email:
        print(f"UYARI: Rez. {reservation_details.get('reservation_id')} için e-posta adresi yok, gönderilemedi.")
        return False
    if not all([SMTP_SERVER, SMTP_PORT_STR, SMTP_SENDER_EMAIL, SMTP_SENDER_PASSWORD]):
        print("UYARI: SMTP ayarları .env dosyasında eksik. E-posta gönderilemiyor.")
        return False
    
    try:
        smtp_port = int(SMTP_PORT_STR)
    except ValueError:
        print(f"UYARI: SMTP_PORT ('{SMTP_PORT_STR}') geçersiz bir sayı.")
        return False

    sender_name = reservation_details.get("hotel_name", "Otelimiz")
    subject = f"{sender_name} - Rezervasyon Talebiniz Alındı ({reservation_details['reservation_id']})"

    reservation_time_str = "Bilinmiyor"
    if reservation_details.get('reservation_time'):
        try:
            reservation_time_str = datetime.fromisoformat(reservation_details['reservation_time']).strftime('%Y-%m-%d %H:%M:%S')
        except (ValueError, TypeError):
            reservation_time_str = reservation_details['reservation_time'] # Parse edilemezse ham veriyi göster

    body = (
        f"Sayın {guest_name},\n\n"
        f"{reservation_details['hotel_name']} otelimizde yaptığınız rezervasyon talebiniz için teşekkür ederiz.\n"
        "Ekibimiz rezervasyonunuzu teyit etmek için en kısa sürede sizinle iletişime geçecektir.\n\n"
        "Rezervasyon Detayları:\n"
        f"------------------------\n"
        f"Geçici Onay No: {reservation_details['reservation_id']}\n"
        f"Oda Tipi: {reservation_details['room_type']}\n"
        f"Giriş Tarihi: {reservation_details['check_in_date']}\n"
        f"Çıkış Tarihi: {reservation_details['check_out_date']} ({reservation_details.get('num_nights', '?')} gece)\n"
        f"Kişi Sayısı: {reservation_details['num_adults']} Yetişkin, {reservation_details.get('num_children', 0)} Çocuk\n"
        f"Özel İstekler: {reservation_details.get('special_requests', 'Yok')}\n"
        f"Tahmini Tutar: {reservation_details.get('total_price_at_booking', 'N/A')}\n"
        f"Talep Zamanı: {datetime.fromisoformat(reservation_details['reservation_time_str']).strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        f"Saygılarımızla,\n{sender_name} Ekibi"
    )
    
    msg = MIMEText(body, 'plain', 'utf-8')
    msg['Subject'] = Header(subject, 'utf-8')
    msg['From'] = f"{Header(sender_name, 'utf-8').encode()} <{SMTP_SENDER_EMAIL}>"
    msg['To'] = to_email

    try:
        if smtp_port == 465:
            with smtplib.SMTP_SSL(SMTP_SERVER, smtp_port) as server:
                server.login(SMTP_SENDER_EMAIL, SMTP_SENDER_PASSWORD)
                server.sendmail(SMTP_SENDER_EMAIL, [to_email], msg.as_string())
        else: 
            with smtplib.SMTP(SMTP_SERVER, smtp_port) as server:
                server.starttls()
                server.login(SMTP_SENDER_EMAIL, SMTP_SENDER_PASSWORD)
                server.sendmail(SMTP_SENDER_EMAIL, [to_email], msg.as_string())
        
        print(f"E-POSTA: {to_email} adresine rezervasyon talebi gönderildi (Rez ID: {reservation_details['reservation_id']}).")
        return True
    except Exception as e:
        print(f"HATA (E-posta gönderimi): {type(e).__name__}: {e}")
        return False
    
    