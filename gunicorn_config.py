# === PRODUCTION GUNICORN CONFIGURATION ===
# Stateless Architecture için Gunicorn ayarları

import multiprocessing
import os

# === WORKER CONFIGURATION ===
# Stateless worker'lar - her worker bağımsız
workers = multiprocessing.cpu_count() * 2 + 1  # CPU core sayısı * 2 + 1
worker_class = "eventlet"  # Socket.IO için eventlet
worker_connections = 1000
max_requests = 1000  # Worker'ı yeniden başlat (memory leak önleme)
max_requests_jitter = 50  # Rastgele jitter
preload_app = True  # Uygulama kodunu önceden yükle

# === NETWORK CONFIGURATION ===
bind = "0.0.0.0:5000"
backlog = 2048
timeout = 30
keepalive = 2

# === LOGGING CONFIGURATION ===
accesslog = "logs/gunicorn_access.log"
errorlog = "logs/gunicorn_error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# === PROCESS CONFIGURATION ===
daemon = False  # Systemd ile çalıştırılacak
pidfile = "gunicorn.pid"
user = None  # Systemd user'ı kullan
group = None
tmp_upload_dir = None

# === STATELESS ARCHITECTURE SETTINGS ===
# Worker'lar arası durum paylaşımı yok
# Tüm durum Redis'te tutulacak

def when_ready(server):
    """Server hazır olduğunda çalışır"""
    server.log.info("=== PRODUCTION SERVER READY ===")
    server.log.info(f"Workers: {workers}")
    server.log.info(f"Worker class: {worker_class}")
    server.log.info("Stateless architecture active")

def worker_int(worker):
    """Worker interrupt signal"""
    worker.log.info(f"Worker {worker.pid} received INT signal")

def pre_fork(server, worker):
    """Worker fork öncesi"""
    server.log.info(f"Worker {worker.pid} forked")

def post_fork(server, worker):
    """Worker fork sonrası - Stateless initialization"""
    worker.log.info(f"Worker {worker.pid} started - Stateless mode")
    
    # Her worker için Redis bağlantısı kontrol et
    try:
        from app.utils import redis_client
        if redis_client:
            worker.log.info(f"Worker {worker.pid} - Redis connection OK")
        else:
            worker.log.warning(f"Worker {worker.pid} - Redis connection FAILED")
    except Exception as e:
        worker.log.error(f"Worker {worker.pid} - Redis check error: {e}")

def worker_abort(worker):
    """Worker abort"""
    worker.log.info(f"Worker {worker.pid} aborted")

def pre_exec(server):
    """Exec öncesi"""
    server.log.info("Pre-exec hook")

# === ENVIRONMENT VARIABLES ===
raw_env = [
    'FLASK_ENV=production',
    'REDIS_URL=redis://localhost:6379/0',
    'DATABASE_URL=sqlite:///hotel_system.db'
]

# === SECURITY SETTINGS ===
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# === PERFORMANCE TUNING ===
# Stateless architecture için optimize edilmiş ayarlar
worker_tmp_dir = "/dev/shm"  # RAM disk kullan (Linux)
forwarded_allow_ips = "*"  # Reverse proxy için
secure_scheme_headers = {
    'X-FORWARDED-PROTOCOL': 'ssl',
    'X-FORWARDED-PROTO': 'https',
    'X-FORWARDED-SSL': 'on'
}
