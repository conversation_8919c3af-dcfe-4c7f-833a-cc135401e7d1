# app/ai/core.py (<PERSON><PERSON> ve <PERSON>amen Stateless Hali)

import time
import json
import traceback
from google.generativeai import protos
from app import socketio, app_logger, redis_client # redis_client'ı da import edelim (hen<PERSON><PERSON> kullanılmıyor ama ilerde gerekebilir)
from app.database import save_chat_message_to_db
from app.ai.executors import tool_executors
from google.api_core import exceptions as google_exceptions 

def _get_expression_from_text(text: str) -> str | None:
    # ... Bu yardımcı fonksiyon aynı kalabilir, değişiklik yok ...
    text_lower = text.lower()
    if any(word in text_lower for word in ["harika", "mükemmel", "çok güzel", "sevindim", "onaylandı"]): return "smile"
    if any(word in text_lower for word in ["üzgünüm", "maalesef", "yapamıyorum", "hata", "sorun"]): return "sad"
    if any(word in text_lower for word in ["inanmıyorum", "ciddi misin", "wow"]): return "surprised"
    if any(word in text_lower for word in ["merhaba", "hoş geldiniz", "tabii ki", "memnuniyetle", "yardımcı olabilirim", "elbette"]): return "greeting"
    if text.strip().endswith('?') or any(word in text_lower for word in ["nasıl yani", "anlamadım", "emin misiniz"]): return "confused"
    return None

def handle_gemini_response_and_tool_calls(chat_session_obj, user_message: str,
                                          session_config: dict, session_id: str, source: str, hotel_id_key: str) -> tuple[str, dict | None]:
    """
    Kullanıcı mesajını alır, Gemini'ye gönderir, potansiyel araç çağrılarını
    yürütür ve son olarak kullanıcıya gösterilecek nihai metin yanıtını döndürür.
    Google API hatalarını akıllıca yönetir ve tüm işlemleri loglar.
    """
    
    app_logger.info(f"GEMINI_INPUT ({source}|{session_id[:8]}): '{user_message}'")
    
    # Varsayılan hata mesajımızı daha kullanıcı dostu yapalım.
    ai_final_text_response = "Üzgünüm, bir sorunla karşılaştım ve yanıt üretemedim."
    special_message_data_for_widget = {}

    try:
        # --- 2. GEMINI'YE İSTEK ---
        response = chat_session_obj.send_message(user_message)
        
        function_call_to_execute = None
        if response.candidates and response.candidates[0].content and response.candidates[0].content.parts:
            for part in response.candidates[0].content.parts:
                if part.function_call and part.function_call.name:
                    function_call_to_execute = part.function_call
                    break
        
        # --- 3. ARAÇ ÇAĞRISI İŞLEME ---
        if function_call_to_execute:
            tool_name = function_call_to_execute.name
            raw_tool_args = dict(function_call_to_execute.args) if function_call_to_execute.args else {}
            app_logger.info(f"TOOL_REQUEST ({source}|{session_id[:8]}): {tool_name} | Args: {raw_tool_args}")

            if tool_name in tool_executors:
                try:
                    tool_result = tool_executors[tool_name](**raw_tool_args, session_config=session_config, session_id=session_id)
                    app_logger.info(f"TOOL_RESULT ({source}|{session_id[:8]}): {json.dumps(tool_result, ensure_ascii=False, default=str)[:200]}...")
                    
                    part_for_gemini = protos.Part(function_response=protos.FunctionResponse(name=tool_name, response=tool_result))
                    
                    # Araç çalıştıktan sonraki ikinci Gemini çağrısı
                    second_response = chat_session_obj.send_message(part_for_gemini)
                    
                    ai_raw_text_from_gemini_after_tool = "".join(p.text for p in second_response.candidates[0].content.parts if hasattr(p, 'text') and p.text)
                    
                    if isinstance(tool_result, dict) and tool_result.get("message_to_user"):
                        ai_final_text_response = tool_result.get("message_to_user")
                    else:
                        ai_final_text_response = ai_raw_text_from_gemini_after_tool or "İşlem başarıyla tamamlandı."
                    
                    if isinstance(tool_result, dict) and tool_result.get("special_render_data"):
                        special_message_data_for_widget.update(tool_result.get("special_render_data"))

                    if tool_name == 'create_reservation' and isinstance(tool_result, dict) and tool_result.get("success") is True:
                        special_message_data_for_widget['trigger_animation'] = 'victory'

                    # Mesajı veritabanına kaydet
                    save_chat_message_to_db(
                        session_id=session_id, sender="AI", message=ai_final_text_response,
                        socketio_instance=socketio, tool_name=tool_name, tool_args=raw_tool_args,
                        tool_response=tool_result, source=source, hotel_id_key=hotel_id_key
                    )
                
                except Exception as e_tool:
                    ai_final_text_response = f"'{tool_name}' aracı işlenirken bir hata oluştu. Lütfen tekrar deneyin."
                    app_logger.exception(f"HATA (Araç Çalıştırma - {tool_name}):")
            
            else:
                ai_final_text_response = f"'{tool_name}' adlı bir araç sistemde tanımlı değil."
        
        # --- 4. NORMAL METİN YANITI ---
        else: 
            ai_final_text_response = "".join(p.text for p in response.candidates[0].content.parts if hasattr(p, 'text') and p.text)
            if not ai_final_text_response.strip():
                ai_final_text_response = "Bunu nasıl yanıtlayacağımı bilemedim, lütfen farklı bir şekilde ifade eder misiniz?"
            
            save_chat_message_to_db(
                session_id=session_id, sender="AI", message=ai_final_text_response,
                socketio_instance=socketio, source=source, hotel_id_key=hotel_id_key
            )

    # === AKILLI HATA YÖNETİMİ BURADA ===
    except google_exceptions.InternalServerError as e_gemini_500:
        # Google'dan 500 - Dahili Sunucu Hatası gelirse, bu spesifik, kibar mesajı ver.
        # Bu hata, bizim kodumuzla ilgili değildir ve genellikle geçicidir.
        app_logger.warning(f"GEMINI_API_FAIL (500 Internal Server Error): {e_gemini_500}")
        ai_final_text_response = "Çok üzgünüm, şu anda yapay zeka servislerinde geçici bir yoğunluk yaşanıyor. Lütfen birkaç saniye bekleyip mesajınızı tekrar gönderir misiniz? Yanıtlamak için hazır olacağım."
        # Bu durumda veritabanına bir "SYSTEM" hatası kaydetmek, sorunu takip etmek için faydalı olabilir.
        save_chat_message_to_db(
            session_id=session_id, sender="SYSTEM", message="Gemini API 500 Internal Server Error",
            socketio_instance=socketio, source=source, hotel_id_key=hotel_id_key
        )

    except Exception as e_gemini_main:
        # Diğer tüm beklenmedik hatalar için (ağ bağlantısı kopması, bilinmeyen API hataları vb.)
        # daha genel bir hata mesajı ver.
        app_logger.exception(f"KRİTİK HATA (handle_gemini_response):")
        ai_final_text_response = "Gösterilen yğun ilgiden dolayı mesajınız işlenirken bir sistem hatası oluştu. Lütfen tekrar söyleyin"
        save_chat_message_to_db(
            session_id=session_id, sender="SYSTEM", message=f"Unhandled Exception: {type(e_gemini_main).__name__}",
            socketio_instance=socketio, source=source, hotel_id_key=hotel_id_key
        )
    # ==================================

    # --- 5. NİHAİ PAKETLEME VE ÇIKIŞ ---
    expression = _get_expression_from_text(ai_final_text_response)
    if expression:
        special_message_data_for_widget['set_expression'] = expression
        
    app_logger.info(f"GEMINI_OUTPUT_TEXT ({source}|{session_id[:8]}): '{ai_final_text_response[:100]}...'")
    app_logger.info(f"GEMINI_OUTPUT_DATA ({source}|{session_id[:8]}): {special_message_data_for_widget}")

    return ai_final_text_response, special_message_data_for_widget if special_message_data_for_widget else None