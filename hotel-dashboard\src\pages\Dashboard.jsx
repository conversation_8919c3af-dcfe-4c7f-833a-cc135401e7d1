import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Avatar,
  Chip,
  LinearProgress,
  Button
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Chat as ChatIcon,
  EventNote as ReservationsIcon,
  Analytics as AnalyticsIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
  Star as StarIcon,
  Schedule as ScheduleIcon,
  Refresh as RefreshIcon,
  ArrowForward as ArrowIcon
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from 'recharts';
import { useNavigate } from 'react-router-dom';
import { useApi } from '../contexts/ApiContext';
import { useAuth } from '../contexts/AuthContext';

const MetricCard = ({ title, value, icon, color, subtitle, trend }) => (
  <Card
    sx={{
      height: '100%',
      background: 'linear-gradient(135deg, #1a1d3a 0%, #2a2d5a 100%)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      transition: 'transform 0.2s ease-in-out',
      '&:hover': {
        transform: 'translateY(-4px)',
        boxShadow: '0 8px 25px rgba(0, 0, 0, 0.3)'
      }
    }}
  >
    <CardContent>
      <Box display="flex" alignItems="center" justifyContent="space-between">
        <Box>
          <Typography color="text.secondary" gutterBottom variant="body2">
            {title}
          </Typography>
          <Typography variant="h4" component="div" fontWeight="bold">
            {value}
          </Typography>
          {subtitle && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              {subtitle}
            </Typography>
          )}
          {trend && (
            <Box display="flex" alignItems="center" mt={1}>
              <TrendingUpIcon fontSize="small" color="success" />
              <Typography variant="body2" color="success.main" sx={{ ml: 0.5 }}>
                {trend}
              </Typography>
            </Box>
          )}
        </Box>
        <Avatar
          sx={{
            bgcolor: color,
            width: 56,
            height: 56,
            background: `linear-gradient(135deg, ${color} 0%, ${color}dd 100%)`
          }}
        >
          {icon}
        </Avatar>
      </Box>
    </CardContent>
  </Card>
);

const Dashboard = () => {
  const { getDashboardTotals, getHotelMetrics, currentHotelId } = useApi();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [metrics, setMetrics] = useState(null);
  const [hotelMetrics, setHotelMetrics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  // Otel yöneticisi için kritik KPI'lar
  const kpiCards = [
    {
      title: 'Bugünkü Gelir',
      value: metrics?.today_revenue ? `₺${metrics.today_revenue.toLocaleString()}` : '₺0',
      change: '+12.5%',
      trend: 'up',
      icon: <MoneyIcon />,
      color: '#4caf50'
    },
    {
      title: 'Doluluk Oranı',
      value: metrics?.occupancy_rate ? `%${metrics.occupancy_rate}` : '%0',
      change: '****%',
      trend: 'up',
      icon: <ReservationsIcon />,
      color: '#2196f3'
    },
    {
      title: 'AI Dönüşüm',
      value: metrics?.conversion_rate ? `%${metrics.conversion_rate}` : '%0',
      change: '****%',
      trend: 'up',
      icon: <TrendingUpIcon />,
      color: '#ff9800'
    },
    {
      title: 'Müşteri Memnuniyeti',
      value: metrics?.customer_satisfaction ? `${metrics.customer_satisfaction}/5` : '0/5',
      change: '+0.3',
      trend: 'up',
      icon: <StarIcon />,
      color: '#9c27b0'
    }
  ];

  // AI Performans metrikleri
  const aiMetrics = [
    {
      label: 'Ortalama Yanıt Süresi',
      value: metrics?.avg_response_time ? `${metrics.avg_response_time}s` : '0s',
      target: '< 3s',
      progress: metrics?.avg_response_time ? Math.min(100, (3 / metrics.avg_response_time) * 100) : 0
    },
    {
      label: 'Aktif Chat Oturumları',
      value: metrics?.total_chat_sessions || 0,
      target: '200+',
      progress: metrics?.total_chat_sessions ? Math.min(100, (metrics.total_chat_sessions / 200) * 100) : 0
    },
    {
      label: 'Onaylı Rezervasyonlar',
      value: metrics?.total_confirmed_reservations || 0,
      target: '100+',
      progress: metrics?.total_confirmed_reservations ? Math.min(100, (metrics.total_confirmed_reservations / 100) * 100) : 0
    }
  ];

  const fetchDashboardData = async () => {
    try {
      setRefreshing(true);

      // Ana dashboard metrikleri
      const dashboardData = await getDashboardTotals(currentHotelId);
      setMetrics(dashboardData);

      // Otel performans metrikleri
      const hotelData = await getHotelMetrics(currentHotelId, '7d');
      setHotelMetrics(hotelData);

      setError(null);
    } catch (err) {
      console.error('Dashboard data fetch error:', err);
      setError('Dashboard verileri yüklenirken bir hata oluştu.');

      // Demo data for fallback
      setMetrics({
        total_chat_sessions: 156,
        total_confirmed_reservations: 89,
        total_ai_initiated_reservations: 67,
        today_revenue: 12500,
        occupancy_rate: 78.5,
        avg_response_time: 2.3,
        customer_satisfaction: 4.6,
        conversion_rate: 23.4
      });
    } finally {
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    await fetchDashboardData();
  };

  useEffect(() => {
    setLoading(true);
    fetchDashboardData().finally(() => setLoading(false));

    // Otomatik yenileme her 2 dakikada bir (otel metrikleri için)
    const refreshInterval = setInterval(() => {
      fetchDashboardData();
    }, 120000); // 2 dakika

    return () => clearInterval(refreshInterval);
  }, [currentHotelId]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Modern Header */}
      <Box mb={4}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Box>
            <Typography variant="h4" fontWeight="700" sx={{ mb: 1 }}>
              Otel Dashboard
            </Typography>
            <Typography variant="body1" color="text.secondary">
              MyPlus Hotel Ataşehir • {new Date().toLocaleDateString('tr-TR', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </Typography>
          </Box>
          <Box display="flex" gap={2} alignItems="center">
            <Chip
              label="Canlı"
              color="success"
              variant="outlined"
              sx={{
                animation: 'pulse 2s infinite',
                '@keyframes pulse': {
                  '0%': { opacity: 1 },
                  '50%': { opacity: 0.5 },
                  '100%': { opacity: 1 }
                }
              }}
            />
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleRefresh}
              disabled={refreshing}
              size="small"
            >
              {refreshing ? 'Yenileniyor...' : 'Yenile'}
            </Button>
          </Box>
        </Box>
      </Box>

      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error} (Demo verileri gösteriliyor)
        </Alert>
      )}

      {/* KPI Cards - Otel Kritik Metrikleri */}
      <Grid container spacing={3} mb={4}>
        {kpiCards.map((kpi, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card
              sx={{
                height: '100%',
                background: 'linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.02) 100%)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255,255,255,0.1)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 8px 32px rgba(0,0,0,0.3)'
                }
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                  <Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      {kpi.title}
                    </Typography>
                    <Typography variant="h4" fontWeight="700" sx={{ mb: 0.5 }}>
                      {kpi.value}
                    </Typography>
                    <Box display="flex" alignItems="center">
                      <Typography
                        variant="caption"
                        sx={{
                          color: kpi.trend === 'up' ? '#4caf50' : '#f44336',
                          fontWeight: 600
                        }}
                      >
                        {kpi.change}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                        vs geçen hafta
                      </Typography>
                    </Box>
                  </Box>
                  <Avatar
                    sx={{
                      bgcolor: kpi.color,
                      width: 48,
                      height: 48
                    }}
                  >
                    {kpi.icon}
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* AI Performans ve Hızlı Erişim */}
      <Grid container spacing={3} mb={4}>
        {/* AI Performans Metrikleri */}
        <Grid item xs={12} md={8}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                🤖 AI Asistan Performansı
              </Typography>
              <Grid container spacing={3}>
                {aiMetrics.map((metric, index) => (
                  <Grid item xs={12} sm={4} key={index}>
                    <Box>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Typography variant="body2" color="text.secondary">
                          {metric.label}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Hedef: {metric.target}
                        </Typography>
                      </Box>
                      <Typography variant="h5" fontWeight="600" sx={{ mb: 1 }}>
                        {metric.value}
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={metric.progress}
                        sx={{
                          height: 6,
                          borderRadius: 3,
                          backgroundColor: 'rgba(255,255,255,0.1)',
                          '& .MuiLinearProgress-bar': {
                            borderRadius: 3,
                            background: metric.progress > 80 ? '#4caf50' : metric.progress > 60 ? '#ff9800' : '#f44336'
                          }
                        }}
                      />
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Hızlı Erişim */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                ⚡ Hızlı Erişim
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<ChatIcon />}
                    onClick={() => navigate('/chat')}
                    sx={{
                      py: 1.5,
                      background: 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)',
                      fontSize: '0.8rem'
                    }}
                  >
                    Canlı Chat
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<ReservationsIcon />}
                    onClick={() => navigate('/reservations')}
                    sx={{
                      py: 1.5,
                      background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
                      fontSize: '0.8rem'
                    }}
                  >
                    Rezervasyonlar
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<AnalyticsIcon />}
                    onClick={() => navigate('/analytics')}
                    sx={{
                      py: 1.5,
                      background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
                      fontSize: '0.8rem'
                    }}
                  >
                    Analiz
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<StarIcon />}
                    onClick={() => navigate('/reviews')}
                    sx={{
                      py: 1.5,
                      background: 'linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%)',
                      fontSize: '0.8rem'
                    }}
                  >
                    Yorumlar
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Otel Performans Grafikleri */}
      <Grid container spacing={3} mb={4}>
        {/* Gelir Trendi */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                📈 7 Günlük Gelir ve Rezervasyon Trendi
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={hotelMetrics?.revenue_trend || []}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis
                    dataKey="date"
                    stroke="#fff"
                    tickFormatter={(value) => new Date(value).toLocaleDateString('tr-TR', { day: '2-digit', month: '2-digit' })}
                  />
                  <YAxis stroke="#fff" />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#1a1d3a',
                      border: '1px solid rgba(255,255,255,0.1)',
                      borderRadius: '8px'
                    }}
                    labelFormatter={(value) => new Date(value).toLocaleDateString('tr-TR')}
                    formatter={(value, name) => [
                      name === 'revenue' ? `₺${value.toLocaleString()}` : value,
                      name === 'revenue' ? 'Gelir' : 'Rezervasyon'
                    ]}
                  />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="revenue"
                    stroke="#4caf50"
                    fill="#4caf50"
                    fillOpacity={0.3}
                    name="Günlük Gelir (₺)"
                  />
                  <Area
                    type="monotone"
                    dataKey="bookings"
                    stroke="#2196f3"
                    fill="#2196f3"
                    fillOpacity={0.3}
                    name="Rezervasyon Sayısı"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Doluluk Oranı */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                🏨 Doluluk Oranı Trendi
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={hotelMetrics?.occupancy_trend || []}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis
                    dataKey="date"
                    stroke="#fff"
                    tickFormatter={(value) => new Date(value).toLocaleDateString('tr-TR', { day: '2-digit' })}
                  />
                  <YAxis stroke="#fff" domain={[0, 100]} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#1a1d3a',
                      border: '1px solid rgba(255,255,255,0.1)',
                      borderRadius: '8px'
                    }}
                    formatter={(value) => [`%${value}`, 'Doluluk Oranı']}
                  />
                  <Line
                    type="monotone"
                    dataKey="rate"
                    stroke="#ff9800"
                    strokeWidth={3}
                    dot={{ fill: '#ff9800', strokeWidth: 2, r: 4 }}
                    name="Doluluk %"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* System Status & Activities */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                24 Saatlik Aktivite
              </Typography>
              <ResponsiveContainer width="100%" height={200}>
                <LineChart data={hourlyData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="hour" stroke="#fff" />
                  <YAxis stroke="#fff" />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#1a1d3a',
                      border: '1px solid rgba(255,255,255,0.1)',
                      borderRadius: '8px'
                    }}
                  />
                  <Line
                    type="monotone"
                    dataKey="activity"
                    stroke="#ff9800"
                    strokeWidth={3}
                    dot={{ fill: '#ff9800', strokeWidth: 2, r: 4 }}
                    name="Aktivite"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Sistem Durumu
              </Typography>
              <Box mb={2}>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">AI Asistan</Typography>
                  <Chip label="Aktif" color="success" size="small" />
                </Box>
                <LinearProgress variant="determinate" value={98} color="success" />
              </Box>
              <Box mb={2}>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">Redis Cache</Typography>
                  <Chip label="Bağlı" color="success" size="small" />
                </Box>
                <LinearProgress variant="determinate" value={95} color="success" />
              </Box>
              <Box mb={2}>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">Google Entegrasyonu</Typography>
                  <Chip label="Bağlı" color="success" size="small" />
                </Box>
                <LinearProgress variant="determinate" value={92} color="success" />
              </Box>
              <Box>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">Facebook Entegrasyonu</Typography>
                  <Chip label="Uyarı" color="warning" size="small" />
                </Box>
                <LinearProgress variant="determinate" value={85} color="warning" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
