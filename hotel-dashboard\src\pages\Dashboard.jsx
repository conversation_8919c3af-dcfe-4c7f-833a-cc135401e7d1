import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Avatar,
  Chip,
  LinearProgress
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Chat as ChatIcon,
  EventNote as ReservationsIcon,
  Analytics as AnalyticsIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
  Star as StarIcon,
  Schedule as ScheduleIcon,
  Refresh as RefreshIcon,
  ArrowForward as ArrowIcon
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from 'recharts';
import { useNavigate } from 'react-router-dom';
import { useApi } from '../contexts/ApiContext';
import { useAuth } from '../contexts/AuthContext';

const MetricCard = ({ title, value, icon, color, subtitle, trend }) => (
  <Card
    sx={{
      height: '100%',
      background: 'linear-gradient(135deg, #1a1d3a 0%, #2a2d5a 100%)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      transition: 'transform 0.2s ease-in-out',
      '&:hover': {
        transform: 'translateY(-4px)',
        boxShadow: '0 8px 25px rgba(0, 0, 0, 0.3)'
      }
    }}
  >
    <CardContent>
      <Box display="flex" alignItems="center" justifyContent="space-between">
        <Box>
          <Typography color="text.secondary" gutterBottom variant="body2">
            {title}
          </Typography>
          <Typography variant="h4" component="div" fontWeight="bold">
            {value}
          </Typography>
          {subtitle && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              {subtitle}
            </Typography>
          )}
          {trend && (
            <Box display="flex" alignItems="center" mt={1}>
              <TrendingUpIcon fontSize="small" color="success" />
              <Typography variant="body2" color="success.main" sx={{ ml: 0.5 }}>
                {trend}
              </Typography>
            </Box>
          )}
        </Box>
        <Avatar
          sx={{
            bgcolor: color,
            width: 56,
            height: 56,
            background: `linear-gradient(135deg, ${color} 0%, ${color}dd 100%)`
          }}
        >
          {icon}
        </Avatar>
      </Box>
    </CardContent>
  </Card>
);

const Dashboard = () => {
  const { getDashboardTotals, currentHotelId } = useApi();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [metrics, setMetrics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [liveStats, setLiveStats] = useState({
    activeSessions: 3,
    onlineUsers: 12,
    lastActivity: new Date().toLocaleTimeString()
  });

  // Mock data for charts - gerçek API'den gelecek
  const weeklyData = [
    { day: 'Pzt', chats: 12, reservations: 8, revenue: 2400 },
    { day: 'Sal', chats: 19, reservations: 12, revenue: 3600 },
    { day: 'Çar', chats: 15, reservations: 9, revenue: 2700 },
    { day: 'Per', chats: 22, reservations: 15, revenue: 4500 },
    { day: 'Cum', chats: 28, reservations: 18, revenue: 5400 },
    { day: 'Cmt', chats: 35, reservations: 22, revenue: 6600 },
    { day: 'Paz', chats: 25, reservations: 16, revenue: 4800 }
  ];

  const hourlyData = [
    { hour: '00', activity: 2 },
    { hour: '06', activity: 5 },
    { hour: '12', activity: 25 },
    { hour: '18', activity: 35 },
    { hour: '24', activity: 8 }
  ];

  const statusData = [
    { name: 'Onaylı', value: metrics?.total_confirmed_reservations || 0, color: '#4caf50' },
    { name: 'AI Başlatılan', value: metrics?.total_ai_initiated_reservations || 0, color: '#2196f3' },
    { name: 'Bekleyen', value: Math.max(0, (metrics?.total_chat_sessions || 0) - (metrics?.total_confirmed_reservations || 0) - (metrics?.total_ai_initiated_reservations || 0)), color: '#ff9800' }
  ];

  const fetchDashboardData = async () => {
    try {
      setRefreshing(true);
      const data = await getDashboardTotals(currentHotelId);
      setMetrics(data);
      setError(null);
    } catch (err) {
      console.error('Dashboard data fetch error:', err);
      setError('Dashboard verileri yüklenirken bir hata oluştu.');
      // Mock data for demo
      setMetrics({
        total_chat_sessions: 156,
        total_confirmed_reservations: 89,
        total_ai_initiated_reservations: 67
      });
    } finally {
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    await fetchDashboardData();
    setLiveStats({
      activeSessions: Math.floor(Math.random() * 8) + 1,
      onlineUsers: Math.floor(Math.random() * 20) + 5,
      lastActivity: new Date().toLocaleTimeString()
    });
  };

  useEffect(() => {
    setLoading(true);
    fetchDashboardData().finally(() => setLoading(false));

    // Simulate live updates every 30 seconds (sadece dashboard'da)
    const liveInterval = setInterval(() => {
      setLiveStats({
        activeSessions: Math.floor(Math.random() * 8) + 1,
        onlineUsers: Math.floor(Math.random() * 20) + 5,
        lastActivity: new Date().toLocaleTimeString()
      });
    }, 30000);

    return () => clearInterval(liveInterval);
  }, [currentHotelId]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Welcome Section */}
      <Box mb={4}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box>
            <Typography variant="h4" gutterBottom fontWeight="bold">
              Hoş Geldiniz, {user?.username}! 👋
            </Typography>
            <Typography variant="body1" color="text.secondary">
              MyPlus Hotel Ataşehir - Canlı Dashboard
            </Typography>
          </Box>
          <Box display="flex" gap={2}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleRefresh}
              disabled={refreshing}
            >
              {refreshing ? 'Yenileniyor...' : 'Yenile'}
            </Button>
            <Typography variant="caption" color="text.secondary" sx={{ alignSelf: 'center' }}>
              Son güncelleme: {liveStats.lastActivity}
            </Typography>
          </Box>
        </Box>
      </Box>

      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error} (Demo verileri gösteriliyor)
        </Alert>
      )}

      {/* Quick Access Buttons */}
      <Grid container spacing={2} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Button
            fullWidth
            variant="contained"
            startIcon={<ChatIcon />}
            endIcon={<ArrowIcon />}
            onClick={() => navigate('/chat')}
            sx={{
              py: 2,
              background: 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
              }
            }}
          >
            Canlı Chat
          </Button>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Button
            fullWidth
            variant="contained"
            startIcon={<ReservationsIcon />}
            endIcon={<ArrowIcon />}
            onClick={() => navigate('/reservations')}
            sx={{
              py: 2,
              background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #388e3c 0%, #2e7d32 100%)',
              }
            }}
          >
            Rezervasyonlar
          </Button>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Button
            fullWidth
            variant="contained"
            startIcon={<AnalyticsIcon />}
            endIcon={<ArrowIcon />}
            onClick={() => navigate('/analytics')}
            sx={{
              py: 2,
              background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #f57c00 0%, #ef6c00 100%)',
              }
            }}
          >
            AI Analiz
          </Button>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Button
            fullWidth
            variant="contained"
            startIcon={<StarIcon />}
            endIcon={<ArrowIcon />}
            onClick={() => navigate('/reviews')}
            sx={{
              py: 2,
              background: 'linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #7b1fa2 0%, #6a1b9a 100%)',
              }
            }}
          >
            Yorumlar
          </Button>
        </Grid>
      </Grid>

      {/* Main Metrics */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Toplam Chat Oturumu"
            value={metrics?.total_chat_sessions || 0}
            icon={<ChatIcon />}
            color="#2196f3"
            subtitle="Bu ay"
            trend="+12%"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Onaylı Rezervasyonlar"
            value={metrics?.total_confirmed_reservations || 0}
            icon={<ReservationsIcon />}
            color="#4caf50"
            subtitle="Bu ay"
            trend="+8%"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="AI Başlatılan Rezervasyonlar"
            value={metrics?.total_ai_initiated_reservations || 0}
            icon={<AnalyticsIcon />}
            color="#ff9800"
            subtitle="Bu ay"
            trend="+15%"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Dönüşüm Oranı"
            value={metrics ? `${Math.round((metrics.total_ai_initiated_reservations / metrics.total_chat_sessions) * 100)}%` : '0%'}
            icon={<TrendingUpIcon />}
            color="#9c27b0"
            subtitle="Chat'ten rezervasyona"
            trend="+3%"
          />
        </Grid>
      </Grid>

      {/* Additional Metrics */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Aktif Chat Oturumları"
            value={liveStats.activeSessions}
            icon={<PeopleIcon />}
            color="#00bcd4"
            subtitle={`Son güncelleme: ${liveStats.lastActivity}`}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Online Kullanıcılar"
            value={liveStats.onlineUsers}
            icon={<MoneyIcon />}
            color="#4caf50"
            subtitle="Şu anda aktif"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Müşteri Memnuniyeti"
            value="4.8"
            icon={<StarIcon />}
            color="#ffc107"
            subtitle="5 üzerinden"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Ortalama Yanıt Süresi"
            value="2.3 dk"
            icon={<ScheduleIcon />}
            color="#e91e63"
            subtitle="AI asistan"
          />
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Haftalık Performans Trendi
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={weeklyData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="day" stroke="#fff" />
                  <YAxis stroke="#fff" />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#1a1d3a',
                      border: '1px solid rgba(255,255,255,0.1)',
                      borderRadius: '8px'
                    }}
                  />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="chats"
                    stackId="1"
                    stroke="#2196f3"
                    fill="#2196f3"
                    fillOpacity={0.6}
                    name="Chat Oturumları"
                  />
                  <Area
                    type="monotone"
                    dataKey="reservations"
                    stackId="1"
                    stroke="#4caf50"
                    fill="#4caf50"
                    fillOpacity={0.6}
                    name="Rezervasyonlar"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Rezervasyon Dağılımı
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* System Status & Activities */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                24 Saatlik Aktivite
              </Typography>
              <ResponsiveContainer width="100%" height={200}>
                <LineChart data={hourlyData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="hour" stroke="#fff" />
                  <YAxis stroke="#fff" />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#1a1d3a',
                      border: '1px solid rgba(255,255,255,0.1)',
                      borderRadius: '8px'
                    }}
                  />
                  <Line
                    type="monotone"
                    dataKey="activity"
                    stroke="#ff9800"
                    strokeWidth={3}
                    dot={{ fill: '#ff9800', strokeWidth: 2, r: 4 }}
                    name="Aktivite"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Sistem Durumu
              </Typography>
              <Box mb={2}>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">AI Asistan</Typography>
                  <Chip label="Aktif" color="success" size="small" />
                </Box>
                <LinearProgress variant="determinate" value={98} color="success" />
              </Box>
              <Box mb={2}>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">Redis Cache</Typography>
                  <Chip label="Bağlı" color="success" size="small" />
                </Box>
                <LinearProgress variant="determinate" value={95} color="success" />
              </Box>
              <Box mb={2}>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">Google Entegrasyonu</Typography>
                  <Chip label="Bağlı" color="success" size="small" />
                </Box>
                <LinearProgress variant="determinate" value={92} color="success" />
              </Box>
              <Box>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">Facebook Entegrasyonu</Typography>
                  <Chip label="Uyarı" color="warning" size="small" />
                </Box>
                <LinearProgress variant="determinate" value={85} color="warning" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
