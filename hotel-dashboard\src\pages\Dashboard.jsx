import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Chip,
  LinearProgress,
  Button
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  SentimentSatisfied as SentimentSatisfiedIcon,
  SentimentDissatisfied as SentimentDissatisfiedIcon,
  SentimentNeutral as SentimentNeutralIcon,
  Lightbulb as LightbulbIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useApi } from '../contexts/ApiContext';



const Dashboard = () => {
  const { getDashboardTotals, currentHotelId } = useApi();
  const navigate = useNavigate();
  const [metrics, setMetrics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);



  // Müşteri memnuniyet durumu
  const getSentimentIcon = () => {
    if (!metrics) return <SentimentNeutralIcon />;

    const sentiment = metrics.customer_sentiment || 0;
    if (sentiment >= 80) return <SentimentSatisfiedIcon sx={{ color: '#4caf50' }} />;
    if (sentiment >= 60) return <SentimentNeutralIcon sx={{ color: '#ff9800' }} />;
    return <SentimentDissatisfiedIcon sx={{ color: '#f44336' }} />;
  };

  const getSentimentText = () => {
    if (!metrics) return 'Veri Yok';

    const sentiment = metrics.customer_sentiment || 0;
    if (sentiment >= 80) return 'POZİTİF';
    if (sentiment >= 60) return 'NÖTR';
    return 'NEGATİF';
  };



  const fetchDashboardData = useCallback(async () => {
    try {
      setRefreshing(true);
      const dashboardData = await getDashboardTotals(currentHotelId);

      console.log('Backend Data:', dashboardData);

      // Backend'den gelen verilerle demo verileri birleştir
      const enrichedData = {
        // Backend'den gelen gerçek veriler
        ...dashboardData,
        // Demo veriler (backend'de henüz olmayan alanlar için)
        pending_reservations: dashboardData.pending_reservations || 4,
        today_checkins: dashboardData.today_checkins || 12,
        today_checkouts: dashboardData.today_checkouts || 8,
        today_occupancy: dashboardData.today_occupancy || 85,
        today_revenue: dashboardData.today_revenue || 42500,
        yesterday_occupancy: dashboardData.yesterday_occupancy || 80,
        yesterday_revenue: dashboardData.yesterday_revenue || 44600,
        active_chats: dashboardData.active_chats || 3,
        abandoned_chats_1h: dashboardData.abandoned_chats_1h || 1,
        customer_sentiment: dashboardData.customer_sentiment || 82,
        avg_response_time: dashboardData.avg_response_time || 2.3,
        top_keywords: dashboardData.top_keywords || [
          { word: 'JAKUZİ', count: 45 },
          { word: 'FİYAT', count: 38 },
          { word: 'KAHVALTI', count: 32 },
          { word: 'ULAŞIM', count: 28 },
          { word: 'FENERBAHÇE', count: 24 },
          { word: 'KONSER', count: 18 }
        ],
        competitor_prices: dashboardData.competitor_prices || [
          { name: 'Siz (Standart Oda)', price: 2000, change: 0 },
          { name: 'Rakip X Oteli', price: 2250, change: 1 },
          { name: 'Rakip Y Oteli', price: 1900, change: -1 }
        ],
        ai_insight: dashboardData.ai_insight || "Son 24 saatte 4 farklı kişi 'toplantı odası' hakkında soru sordu. Web sitenizin ana sayfasına 'İş Toplantıları İçin İdealiz' gibi bir başlık eklemek, bu talebi rezervasyona çevirebilir."
      };

      console.log('Enriched Data:', enrichedData);
      setMetrics(enrichedData);
      setError(null);
    } catch (err) {
      console.error('Dashboard data fetch error:', err);
      setError('Dashboard verileri yüklenirken bir hata oluştu.');

      // Demo data for fallback - Otel operasyonel verileri
      setMetrics({
        total_chat_sessions: 156,
        total_confirmed_reservations: 89,
        total_ai_initiated_reservations: 67,
        pending_reservations: 4,
        today_checkins: 12,
        today_checkouts: 8,
        today_occupancy: 85,
        today_revenue: 42500,
        yesterday_occupancy: 80,
        yesterday_revenue: 44600,
        active_chats: 3,
        abandoned_chats_1h: 1,
        customer_sentiment: 82,
        avg_response_time: 2.3,
        top_keywords: [
          { word: 'JAKUZİ', count: 45 },
          { word: 'FİYAT', count: 38 },
          { word: 'KAHVALTI', count: 32 },
          { word: 'ULAŞIM', count: 28 },
          { word: 'FENERBAHÇE', count: 24 },
          { word: 'KONSER', count: 18 }
        ],
        competitor_prices: [
          { name: 'Siz (Standart Oda)', price: 2000, change: 0 },
          { name: 'Rakip X Oteli', price: 2250, change: 1 },
          { name: 'Rakip Y Oteli', price: 1900, change: -1 }
        ],
        ai_insight: "Son 24 saatte 4 farklı kişi 'toplantı odası' hakkında soru sordu. Web sitenizin ana sayfasına 'İş Toplantıları İçin İdealiz' gibi bir başlık eklemek, bu talebi rezervasyona çevirebilir."
      });
    } finally {
      setRefreshing(false);
    }
  }, [getDashboardTotals, currentHotelId]);

  const handleRefresh = async () => {
    await fetchDashboardData();
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await fetchDashboardData();
      setLoading(false);
    };

    loadData();

    // Otomatik yenileme her 2 dakikada bir (otel metrikleri için)
    const refreshInterval = setInterval(() => {
      fetchDashboardData();
    }, 120000); // 2 dakika

    return () => clearInterval(refreshInterval);
  }, [fetchDashboardData]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header - Minimal */}
      <Box mb={3}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h5" fontWeight="600">
            Bugün Ne Oluyor? • {new Date().toLocaleDateString('tr-TR', {
              weekday: 'long',
              day: 'numeric',
              month: 'long'
            })}
          </Typography>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            disabled={refreshing}
            size="small"
          >
            {refreshing ? 'Yenileniyor...' : 'Yenile'}
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error} (Demo verileri gösteriliyor)
        </Alert>
      )}

      {/* ÜST BÖLÜM - ONAY BEKLEYEN REZERVASYONLAR */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12}>
          <Card
            sx={{
              background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)',
              color: 'white',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 32px rgba(255, 107, 107, 0.4)'
              }
            }}
            onClick={() => navigate('/reservations?filter=pending')}
          >
            <CardContent sx={{ p: 4 }}>
              <Grid container alignItems="center" spacing={3}>
                <Grid item xs={12} md={3} textAlign="center">
                  <Typography variant="h2" fontWeight="700" sx={{ mb: 1 }}>
                    {metrics?.pending_reservations || 0}
                  </Typography>
                  <Chip
                    label="ACIL"
                    sx={{
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      color: 'white',
                      fontWeight: 'bold'
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="h5" fontWeight="600" sx={{ mb: 1 }}>
                    ONAY BEKLEYEN REZERVASYONLAR
                  </Typography>
                  <Typography variant="body1" sx={{ opacity: 0.9 }}>
                    Misafirler yanıt bekliyor • Hemen aksiyon alın!
                  </Typography>
                </Grid>
                <Grid item xs={12} md={3} textAlign="center">
                  <Typography variant="h6" sx={{ opacity: 0.8 }}>
                    Ortalama Bekleme
                  </Typography>
                  <Typography variant="h4" fontWeight="600">
                    2.3 saat
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* ORTA BÖLÜM - ANA METRİKLER */}
      <Grid container spacing={3} mb={3}>
        {/* Sol Taraf - Operasyonel Metrikler */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                📊 Bugünkü Operasyonlar
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box textAlign="center" p={2} sx={{ backgroundColor: 'rgba(76, 175, 80, 0.1)', borderRadius: 2 }}>
                    <Typography variant="h3" fontWeight="700" color="success.main">
                      {metrics?.today_checkins || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      Bugünkü Girişler
                    </Typography>
                    <Chip label="▲ %12" size="small" color="success" />
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box textAlign="center" p={2} sx={{ backgroundColor: 'rgba(33, 150, 243, 0.1)', borderRadius: 2 }}>
                    <Typography variant="h3" fontWeight="700" color="primary">
                      {metrics?.today_checkouts || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      Bugünkü Çıkışlar
                    </Typography>
                    <Chip label="▼ %5" size="small" color="warning" />
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box textAlign="center" p={2} sx={{ backgroundColor: 'rgba(255, 152, 0, 0.1)', borderRadius: 2 }}>
                    <Typography variant="h3" fontWeight="700" color="warning.main">
                      %{metrics?.today_occupancy || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      Doluluk Oranı
                    </Typography>
                    <Chip label="▲ %8" size="small" color="success" />
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box textAlign="center" p={2} sx={{ backgroundColor: 'rgba(156, 39, 176, 0.1)', borderRadius: 2 }}>
                    <Typography variant="h4" fontWeight="700" color="secondary.main">
                      ₺{(metrics?.today_revenue || 0).toLocaleString('tr-TR')}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      Bugünkü Gelir
                    </Typography>
                    <Chip label="▼ %5" size="small" color="error" />
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Sağ Taraf - AI ve Chat Metrikleri */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                🤖 AI & Chat Performansı
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} mb={2}>
                  <Card
                    sx={{
                      background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
                      color: 'white'
                    }}
                  >
                    <CardContent sx={{ p: 2 }}>
                      <Box display="flex" alignItems="center" mb={1}>
                        <LightbulbIcon sx={{ mr: 1, fontSize: 20 }} />
                        <Typography variant="subtitle2" fontWeight="600">
                          GÜNÜN FIRSATI
                        </Typography>
                      </Box>
                      <Typography variant="body2" sx={{ lineHeight: 1.4 }}>
                        {(metrics?.ai_insight || "AI analizi yükleniyor...").substring(0, 120)}...
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={4}>
                  <Box textAlign="center" p={1.5} sx={{ backgroundColor: 'rgba(33, 150, 243, 0.1)', borderRadius: 2 }}>
                    <Typography variant="h4" fontWeight="700" color="primary">
                      {metrics?.active_chats || 0}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Aktif Chat
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={4}>
                  <Box textAlign="center" p={1.5} sx={{ backgroundColor: 'rgba(244, 67, 54, 0.1)', borderRadius: 2 }}>
                    <Typography variant="h4" fontWeight="700" color="error.main">
                      {metrics?.abandoned_chats_1h || 0}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Kayıp Fırsat
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={4}>
                  <Box textAlign="center" p={1.5} sx={{ backgroundColor: 'rgba(76, 175, 80, 0.1)', borderRadius: 2 }}>
                    <Typography variant="h4" fontWeight="700" color="success.main">
                      %{((metrics?.total_ai_initiated_reservations || 0) / (metrics?.total_chat_sessions || 1) * 100).toFixed(0)}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      AI Dönüşüm
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* GRAFİKLER BÖLÜMÜ - SOL VE SAĞ */}
      <Grid container spacing={3}>
        {/* SOL GRAFİK - MÜŞTERİ MEMNUNİYETİ VE TREND */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                📊 Müşteri Memnuniyet Trendi
              </Typography>

              {/* Memnuniyet Barometresi */}
              <Box mb={3}>
                <Box display="flex" alignItems="center" justifyContent="center" mb={2}>
                  {getSentimentIcon()}
                  <Typography variant="h4" fontWeight="700" sx={{ ml: 2 }}>
                    {getSentimentText()}
                  </Typography>
                </Box>
                <Typography variant="body1" textAlign="center" color="text.secondary" mb={2}>
                  Son 24 Saat: %{metrics?.customer_sentiment || 0} Olumlu
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={metrics?.customer_sentiment || 0}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'rgba(0,0,0,0.1)',
                    '& .MuiLinearProgress-bar': {
                      borderRadius: 4,
                      background: (metrics?.customer_sentiment || 0) >= 80 ? '#4caf50' :
                                 (metrics?.customer_sentiment || 0) >= 60 ? '#ff9800' : '#f44336'
                    }
                  }}
                />
              </Box>

              {/* En Çok Konuşulanlar */}
              <Box>
                <Typography variant="subtitle2" fontWeight="600" sx={{ mb: 2 }}>
                  💭 En Çok Konuşulan Konular
                </Typography>
                <Box display="flex" flexWrap="wrap" gap={1}>
                  {metrics?.top_keywords?.map((keyword, index) => (
                    <Chip
                      key={index}
                      label={`${keyword.word} (${keyword.count})`}
                      size="small"
                      sx={{
                        fontSize: Math.max(0.7, Math.min(1.1, keyword.count / 50)) + 'rem',
                        backgroundColor: `hsl(${index * 60}, 70%, 85%)`,
                        color: `hsl(${index * 60}, 70%, 25%)`
                      }}
                    />
                  ))}
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* SAĞ GRAFİK - RAKİP ANALİZİ VE PERFORMANS */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                💰 Fiyat Rekabeti & Performans
              </Typography>

              {/* Rakip Fiyat Karşılaştırması */}
              <Box mb={3}>
                <Typography variant="subtitle2" fontWeight="600" sx={{ mb: 2 }}>
                  Rakip Fiyat Radarı
                </Typography>
                {metrics?.competitor_prices?.map((competitor, index) => (
                  <Box
                    key={index}
                    display="flex"
                    justifyContent="space-between"
                    alignItems="center"
                    py={1.5}
                    px={2}
                    sx={{
                      backgroundColor: index === 0 ? 'rgba(33, 150, 243, 0.1)' : 'transparent',
                      borderRadius: 1,
                      mb: 1,
                      border: index === 0 ? '1px solid rgba(33, 150, 243, 0.3)' : '1px solid rgba(255,255,255,0.1)'
                    }}
                  >
                    <Typography variant="body2" sx={{ fontWeight: index === 0 ? 600 : 400 }}>
                      {competitor.name}
                    </Typography>
                    <Box display="flex" alignItems="center">
                      <Typography variant="h6" fontWeight="600" sx={{ mr: 1 }}>
                        ₺{competitor.price.toLocaleString('tr-TR')}
                      </Typography>
                      {competitor.change !== 0 && (
                        <Chip
                          label={competitor.change > 0 ? `+${competitor.change}%` : `${competitor.change}%`}
                          size="small"
                          color={competitor.change > 0 ? 'error' : 'success'}
                          sx={{ fontSize: '0.7rem' }}
                        />
                      )}
                    </Box>
                  </Box>
                ))}
              </Box>

              {/* Performans Özeti */}
              <Box>
                <Typography variant="subtitle2" fontWeight="600" sx={{ mb: 2 }}>
                  📈 Bu Ay Performans Özeti
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={1} sx={{ backgroundColor: 'rgba(76, 175, 80, 0.1)', borderRadius: 1 }}>
                      <Typography variant="h6" fontWeight="700" color="success.main">
                        %{((metrics?.total_ai_initiated_reservations || 0) / (metrics?.total_chat_sessions || 1) * 100).toFixed(1)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        AI Dönüşüm Oranı
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={1} sx={{ backgroundColor: 'rgba(255, 152, 0, 0.1)', borderRadius: 1 }}>
                      <Typography variant="h6" fontWeight="700" color="warning.main">
                        {metrics?.avg_response_time || 0}s
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Ortalama Yanıt
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
