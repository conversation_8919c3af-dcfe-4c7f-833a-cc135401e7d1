import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Avatar,
  Chip,
  LinearProgress,
  Button
} from '@mui/material';
import {
  EventNote as ReservationsIcon,
  AttachMoney as MoneyIcon,
  Refresh as RefreshIcon,
  Login as LoginIcon,
  Logout as LogoutIcon,
  SentimentSatisfied as SentimentSatisfiedIcon,
  SentimentDissatisfied as SentimentDissatisfiedIcon,
  SentimentNeutral as SentimentNeutralIcon,
  ArrowUpward,
  ArrowDownward,
  Lightbulb as LightbulbIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useApi } from '../contexts/ApiContext';



const Dashboard = () => {
  const { getDashboardTotals, currentHotelId } = useApi();
  const navigate = useNavigate();
  const [metrics, setMetrics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  // Bugünkü KPI'lar için hesaplamalar
  const getTodayKPIs = () => {
    if (!metrics) return [];

    const occupancyChange = metrics.today_occupancy - metrics.yesterday_occupancy;
    const revenueChange = metrics.today_revenue - metrics.yesterday_revenue;

    return [
      {
        title: 'Bugünkü Girişler',
        value: metrics.today_checkins,
        subtitle: 'Check-in',
        icon: <LoginIcon />,
        color: '#4caf50'
      },
      {
        title: 'Bugünkü Çıkışlar',
        value: metrics.today_checkouts,
        subtitle: 'Check-out',
        icon: <LogoutIcon />,
        color: '#2196f3'
      },
      {
        title: 'Bugünkü Doluluk',
        value: `%${metrics.today_occupancy}`,
        subtitle: `Düne göre ${occupancyChange >= 0 ? '▲' : '▼'}%${Math.abs(occupancyChange)}`,
        icon: <ReservationsIcon />,
        color: occupancyChange >= 0 ? '#4caf50' : '#f44336',
        trend: occupancyChange >= 0 ? 'up' : 'down'
      },
      {
        title: 'Bugünkü Gelir',
        value: `₺${metrics.today_revenue.toLocaleString()}`,
        subtitle: `Düne göre ${revenueChange >= 0 ? '▲' : '▼'}₺${Math.abs(revenueChange).toLocaleString()}`,
        icon: <MoneyIcon />,
        color: revenueChange >= 0 ? '#4caf50' : '#f44336',
        trend: revenueChange >= 0 ? 'up' : 'down'
      }
    ];
  };

  // Müşteri memnuniyet durumu
  const getSentimentIcon = () => {
    if (!metrics) return <SentimentNeutralIcon />;

    if (metrics.customer_sentiment >= 80) return <SentimentSatisfiedIcon sx={{ color: '#4caf50' }} />;
    if (metrics.customer_sentiment >= 60) return <SentimentNeutralIcon sx={{ color: '#ff9800' }} />;
    return <SentimentDissatisfiedIcon sx={{ color: '#f44336' }} />;
  };

  const getSentimentText = () => {
    if (!metrics) return 'Veri Yok';

    if (metrics.customer_sentiment >= 80) return 'POZİTİF';
    if (metrics.customer_sentiment >= 60) return 'NÖTR';
    return 'NEGATİF';
  };



  const fetchDashboardData = useCallback(async () => {
    try {
      setRefreshing(true);
      const dashboardData = await getDashboardTotals(currentHotelId);
      setMetrics(dashboardData);
      setError(null);
    } catch (err) {
      console.error('Dashboard data fetch error:', err);
      setError('Dashboard verileri yüklenirken bir hata oluştu.');

      // Demo data for fallback - Otel operasyonel verileri
      setMetrics({
        total_chat_sessions: 156,
        total_confirmed_reservations: 89,
        total_ai_initiated_reservations: 67,
        pending_reservations: 4,
        today_checkins: 12,
        today_checkouts: 8,
        today_occupancy: 85,
        today_revenue: 42500,
        yesterday_occupancy: 80,
        yesterday_revenue: 44600,
        active_chats: 3,
        abandoned_chats_1h: 1,
        customer_sentiment: 82,
        avg_response_time: 2.3,
        top_keywords: [
          { word: 'JAKUZİ', count: 45 },
          { word: 'FİYAT', count: 38 },
          { word: 'KAHVALTI', count: 32 },
          { word: 'ULAŞIM', count: 28 },
          { word: 'FENERBAHÇE', count: 24 },
          { word: 'KONSER', count: 18 }
        ],
        competitor_prices: [
          { name: 'Siz (Standart Oda)', price: 2000, change: 0 },
          { name: 'Rakip X Oteli', price: 2250, change: 1 },
          { name: 'Rakip Y Oteli', price: 1900, change: -1 }
        ],
        ai_insight: "Son 24 saatte 4 farklı kişi 'toplantı odası' hakkında soru sordu. Web sitenizin ana sayfasına 'İş Toplantıları İçin İdealiz' gibi bir başlık eklemek, bu talebi rezervasyona çevirebilir."
      });
    } finally {
      setRefreshing(false);
    }
  }, [getDashboardTotals, currentHotelId]);

  const handleRefresh = async () => {
    await fetchDashboardData();
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await fetchDashboardData();
      setLoading(false);
    };

    loadData();

    // Otomatik yenileme her 2 dakikada bir (otel metrikleri için)
    const refreshInterval = setInterval(() => {
      fetchDashboardData();
    }, 120000); // 2 dakika

    return () => clearInterval(refreshInterval);
  }, [fetchDashboardData]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header - Minimal */}
      <Box mb={3}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h5" fontWeight="600">
            Bugün Ne Oluyor? • {new Date().toLocaleDateString('tr-TR', {
              weekday: 'long',
              day: 'numeric',
              month: 'long'
            })}
          </Typography>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            disabled={refreshing}
            size="small"
          >
            {refreshing ? 'Yenileniyor...' : 'Yenile'}
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error} (Demo verileri gösteriliyor)
        </Alert>
      )}

      {/* Ana Layout: Sol - Bugün Ne Oluyor, Sağ - Ufukta Ne Var */}
      <Grid container spacing={4}>
        {/* SOL TARAF - BUGÜN NE OLUYOR? */}
        <Grid item xs={12} md={7}>
          {/* EN ÖNEMLİ: ONAY BEKLEYEN REZERVASYONLAR */}
          <Card
            sx={{
              mb: 3,
              background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)',
              color: 'white',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 32px rgba(255, 107, 107, 0.4)'
              }
            }}
            onClick={() => navigate('/reservations?filter=pending')}
          >
            <CardContent sx={{ p: 4, textAlign: 'center' }}>
              <Typography variant="h2" fontWeight="700" sx={{ mb: 1 }}>
                {metrics?.pending_reservations || 0}
              </Typography>
              <Typography variant="h5" fontWeight="600" sx={{ mb: 1 }}>
                ONAY BEKLEYEN REZERVASYONLAR
              </Typography>
              <Typography variant="body1" sx={{ opacity: 0.9 }}>
                Misafirler Yanıt Bekliyor • Hemen Aksiyon Alın!
              </Typography>
              <Box mt={2}>
                <Chip
                  label="ACIL"
                  sx={{
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    color: 'white',
                    fontWeight: 'bold'
                  }}
                />
              </Box>
            </CardContent>
          </Card>

          {/* BUGÜNKÜ KPI'LAR */}
          <Grid container spacing={2} mb={3}>
            {getTodayKPIs().map((kpi, index) => (
              <Grid item xs={6} sm={3} key={index}>
                <Card sx={{ height: '100%' }}>
                  <CardContent sx={{ p: 2, textAlign: 'center' }}>
                    <Avatar
                      sx={{
                        bgcolor: kpi.color,
                        width: 40,
                        height: 40,
                        mx: 'auto',
                        mb: 1
                      }}
                    >
                      {kpi.icon}
                    </Avatar>
                    <Typography variant="h6" fontWeight="600">
                      {kpi.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                      {kpi.title}
                    </Typography>
                    <Typography
                      variant="caption"
                      sx={{
                        color: kpi.trend === 'up' ? '#4caf50' : kpi.trend === 'down' ? '#f44336' : 'text.secondary',
                        fontWeight: 600
                      }}
                    >
                      {kpi.subtitle}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* ANLIK SOHBET AKTİVİTESİ */}
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 2 }}>
                💬 Anlık Sohbet Aktivitesi
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={6}>
                  <Box
                    sx={{
                      p: 2,
                      backgroundColor: 'rgba(33, 150, 243, 0.1)',
                      borderRadius: 2,
                      cursor: 'pointer'
                    }}
                    onClick={() => navigate('/chat')}
                  >
                    <Typography variant="h4" fontWeight="700" color="primary">
                      {metrics?.active_chats || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Aktif Sohbetler
                    </Typography>
                    <Typography variant="caption" color="primary">
                      Şu anda canlı →
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box
                    sx={{
                      p: 2,
                      backgroundColor: 'rgba(255, 152, 0, 0.1)',
                      borderRadius: 2
                    }}
                  >
                    <Typography variant="h4" fontWeight="700" color="warning.main">
                      {metrics?.abandoned_chats_1h || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Kayıp Fırsat
                    </Typography>
                    <Typography variant="caption" color="warning.main">
                      Son 1 saatte terk edildi
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* SAĞ TARAF - UFUKTA NE VAR? */}
        <Grid item xs={12} md={5}>
          {/* EN KRİTİK AI ANALİZİ: GÜNÜN FIRSATI */}
          <Card
            sx={{
              mb: 3,
              background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
              color: 'white'
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box display="flex" alignItems="center" mb={2}>
                <LightbulbIcon sx={{ mr: 1, fontSize: 28 }} />
                <Typography variant="h6" fontWeight="600">
                  GÜNÜN FIRSATI
                </Typography>
              </Box>
              <Typography variant="body1" sx={{ lineHeight: 1.6 }}>
                {metrics?.ai_insight || "AI analizi yükleniyor..."}
              </Typography>
            </CardContent>
          </Card>

          {/* MÜŞTERİ MEMNUNİYET BAROMETRESİ */}
          <Card sx={{ mb: 3 }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 2 }}>
                📊 Müşteri Memnuniyet Barometresi
              </Typography>
              <Box display="flex" alignItems="center" justifyContent="center" mb={2}>
                {getSentimentIcon()}
                <Typography variant="h4" fontWeight="700" sx={{ ml: 2 }}>
                  {getSentimentText()}
                </Typography>
              </Box>
              <Typography variant="body1" textAlign="center" color="text.secondary">
                Son 24 Saat: %{metrics?.customer_sentiment || 0} Olumlu
              </Typography>
              <LinearProgress
                variant="determinate"
                value={metrics?.customer_sentiment || 0}
                sx={{
                  mt: 2,
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: 'rgba(0,0,0,0.1)',
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 4,
                    background: metrics?.customer_sentiment >= 80 ? '#4caf50' :
                               metrics?.customer_sentiment >= 60 ? '#ff9800' : '#f44336'
                  }
                }}
              />
            </CardContent>
          </Card>

          {/* EN ÇOK KONUŞULANLAR KELİME BULUTU */}
          <Card sx={{ mb: 3 }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 2 }}>
                💭 En Çok Konuşulanlar
              </Typography>
              <Box display="flex" flexWrap="wrap" gap={1}>
                {metrics?.top_keywords?.map((keyword, index) => (
                  <Chip
                    key={index}
                    label={`${keyword.word} (${keyword.count})`}
                    size="small"
                    sx={{
                      fontSize: Math.max(0.7, Math.min(1.2, keyword.count / 50)) + 'rem',
                      backgroundColor: `hsl(${index * 60}, 70%, 85%)`,
                      color: `hsl(${index * 60}, 70%, 25%)`
                    }}
                  />
                ))}
              </Box>
            </CardContent>
          </Card>

          {/* MİNİ RAKİP FİYAT RADARI */}
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 2 }}>
                💰 Rakip Fiyat Radarı
              </Typography>
              {metrics?.competitor_prices?.map((competitor, index) => (
                <Box
                  key={index}
                  display="flex"
                  justifyContent="space-between"
                  alignItems="center"
                  py={1}
                  sx={{
                    borderBottom: index < metrics.competitor_prices.length - 1 ? '1px solid rgba(255,255,255,0.1)' : 'none'
                  }}
                >
                  <Typography variant="body2" sx={{ fontWeight: index === 0 ? 600 : 400 }}>
                    {competitor.name}
                  </Typography>
                  <Box display="flex" alignItems="center">
                    <Typography variant="body2" fontWeight="600">
                      ₺{competitor.price.toLocaleString()}
                    </Typography>
                    {competitor.change !== 0 && (
                      <Box ml={1}>
                        {competitor.change > 0 ? (
                          <ArrowUpward sx={{ fontSize: 16, color: '#f44336' }} />
                        ) : (
                          <ArrowDownward sx={{ fontSize: 16, color: '#4caf50' }} />
                        )}
                      </Box>
                    )}
                  </Box>
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>
      </Grid>



    </Box>
  );
};

export default Dashboard;
