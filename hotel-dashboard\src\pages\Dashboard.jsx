import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Avatar,
  Chip,
  LinearProgress
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Chat as ChatIcon,
  EventNote as ReservationsIcon,
  Analytics as AnalyticsIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
  Star as StarIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { useApi } from '../contexts/ApiContext';
import { useAuth } from '../contexts/AuthContext';

const MetricCard = ({ title, value, icon, color, subtitle, trend }) => (
  <Card
    sx={{
      height: '100%',
      background: 'linear-gradient(135deg, #1a1d3a 0%, #2a2d5a 100%)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      transition: 'transform 0.2s ease-in-out',
      '&:hover': {
        transform: 'translateY(-4px)',
        boxShadow: '0 8px 25px rgba(0, 0, 0, 0.3)'
      }
    }}
  >
    <CardContent>
      <Box display="flex" alignItems="center" justifyContent="space-between">
        <Box>
          <Typography color="text.secondary" gutterBottom variant="body2">
            {title}
          </Typography>
          <Typography variant="h4" component="div" fontWeight="bold">
            {value}
          </Typography>
          {subtitle && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              {subtitle}
            </Typography>
          )}
          {trend && (
            <Box display="flex" alignItems="center" mt={1}>
              <TrendingUpIcon fontSize="small" color="success" />
              <Typography variant="body2" color="success.main" sx={{ ml: 0.5 }}>
                {trend}
              </Typography>
            </Box>
          )}
        </Box>
        <Avatar
          sx={{
            bgcolor: color,
            width: 56,
            height: 56,
            background: `linear-gradient(135deg, ${color} 0%, ${color}dd 100%)`
          }}
        >
          {icon}
        </Avatar>
      </Box>
    </CardContent>
  </Card>
);

const Dashboard = () => {
  const { getDashboardTotals, currentHotelId } = useApi();
  const { user } = useAuth();
  const [metrics, setMetrics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const data = await getDashboardTotals(currentHotelId);
        setMetrics(data);
        setError(null);
      } catch (err) {
        console.error('Dashboard data fetch error:', err);
        setError('Dashboard verileri yüklenirken bir hata oluştu.');
        // Mock data for demo
        setMetrics({
          total_chat_sessions: 156,
          total_confirmed_reservations: 89,
          total_ai_initiated_reservations: 67
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [getDashboardTotals, currentHotelId]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Welcome Section */}
      <Box mb={4}>
        <Typography variant="h4" gutterBottom fontWeight="bold">
          Hoş Geldiniz, {user?.username}! 👋
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Otel yönetim sisteminizin genel durumunu buradan takip edebilirsiniz.
        </Typography>
      </Box>

      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error} (Demo verileri gösteriliyor)
        </Alert>
      )}

      {/* Main Metrics */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Toplam Chat Oturumu"
            value={metrics?.total_chat_sessions || 0}
            icon={<ChatIcon />}
            color="#2196f3"
            subtitle="Bu ay"
            trend="+12%"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Onaylı Rezervasyonlar"
            value={metrics?.total_confirmed_reservations || 0}
            icon={<ReservationsIcon />}
            color="#4caf50"
            subtitle="Bu ay"
            trend="+8%"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="AI Başlatılan Rezervasyonlar"
            value={metrics?.total_ai_initiated_reservations || 0}
            icon={<AnalyticsIcon />}
            color="#ff9800"
            subtitle="Bu ay"
            trend="+15%"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Dönüşüm Oranı"
            value={metrics ? `${Math.round((metrics.total_ai_initiated_reservations / metrics.total_chat_sessions) * 100)}%` : '0%'}
            icon={<TrendingUpIcon />}
            color="#9c27b0"
            subtitle="Chat'ten rezervasyona"
            trend="+3%"
          />
        </Grid>
      </Grid>

      {/* Additional Metrics */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Aktif Müşteriler"
            value="234"
            icon={<PeopleIcon />}
            color="#00bcd4"
            subtitle="Son 30 gün"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Ortalama Gelir"
            value="₺2,450"
            icon={<MoneyIcon />}
            color="#4caf50"
            subtitle="Rezervasyon başına"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Müşteri Memnuniyeti"
            value="4.8"
            icon={<StarIcon />}
            color="#ffc107"
            subtitle="5 üzerinden"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Ortalama Yanıt Süresi"
            value="2.3 dk"
            icon={<ScheduleIcon />}
            color="#e91e63"
            subtitle="AI asistan"
          />
        </Grid>
      </Grid>

      {/* Quick Stats */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Sistem Durumu
              </Typography>
              <Box mb={2}>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">AI Asistan</Typography>
                  <Chip label="Aktif" color="success" size="small" />
                </Box>
                <LinearProgress variant="determinate" value={98} color="success" />
              </Box>
              <Box mb={2}>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">Google Entegrasyonu</Typography>
                  <Chip label="Bağlı" color="success" size="small" />
                </Box>
                <LinearProgress variant="determinate" value={95} color="success" />
              </Box>
              <Box>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2">Facebook Entegrasyonu</Typography>
                  <Chip label="Bağlı" color="warning" size="small" />
                </Box>
                <LinearProgress variant="determinate" value={85} color="warning" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Son Aktiviteler
              </Typography>
              <Box>
                <Box display="flex" alignItems="center" mb={2}>
                  <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32, mr: 2 }}>
                    <ChatIcon fontSize="small" />
                  </Avatar>
                  <Box>
                    <Typography variant="body2">Yeni chat oturumu başlatıldı</Typography>
                    <Typography variant="caption" color="text.secondary">2 dakika önce</Typography>
                  </Box>
                </Box>
                <Box display="flex" alignItems="center" mb={2}>
                  <Avatar sx={{ bgcolor: 'success.main', width: 32, height: 32, mr: 2 }}>
                    <ReservationsIcon fontSize="small" />
                  </Avatar>
                  <Box>
                    <Typography variant="body2">Rezervasyon onaylandı</Typography>
                    <Typography variant="caption" color="text.secondary">5 dakika önce</Typography>
                  </Box>
                </Box>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'warning.main', width: 32, height: 32, mr: 2 }}>
                    <AnalyticsIcon fontSize="small" />
                  </Avatar>
                  <Box>
                    <Typography variant="body2">Haftalık analiz tamamlandı</Typography>
                    <Typography variant="caption" color="text.secondary">1 saat önce</Typography>
                  </Box>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
