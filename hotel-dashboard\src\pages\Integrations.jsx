import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Switch,
  FormControlLabel,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Google as GoogleIcon,
  Facebook as FacebookIcon,
  Settings as SettingsIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { useApi } from '../contexts/ApiContext';

const Integrations = () => {
  const { getGoogleReviews, syncGoogleReviews, getFacebookComments, currentHotelId } = useApi();
  const [integrations, setIntegrations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [syncing, setSyncing] = useState(null);
  const [googleStats, setGoogleStats] = useState(null);
  const [facebookStats, setFacebookStats] = useState(null);

  const fetchIntegrationData = async () => {
    try {
      setLoading(true);
      console.log('Fetching integration data for hotel:', currentHotelId);

      // Try to get Google reviews to check connection
      try {
        const googleData = await getGoogleReviews(currentHotelId);
        console.log('Google reviews data:', googleData);
        setGoogleStats({
          reviewCount: googleData.reviews?.length || 0,
          lastSync: new Date().toLocaleString('tr-TR')
        });
      } catch (googleErr) {
        console.log('Google integration not available:', googleErr.message);
        setGoogleStats(null);
      }

      // Try to get Facebook comments to check connection
      try {
        const facebookData = await getFacebookComments(currentHotelId);
        console.log('Facebook comments data:', facebookData);
        setFacebookStats({
          commentCount: facebookData.comments?.length || 0,
          lastSync: new Date().toLocaleString('tr-TR')
        });
      } catch (facebookErr) {
        console.log('Facebook integration not available:', facebookErr.message);
        setFacebookStats(null);
      }

      // Set integration status based on API responses
      setIntegrations([
        {
          id: 'google',
          name: 'Google My Business',
          description: 'Google yorumlarını senkronize edin ve yanıtlayın',
          icon: <GoogleIcon />,
          connected: !!googleStats,
          status: googleStats ? 'active' : 'error',
          lastSync: googleStats?.lastSync || 'Hiç senkronize edilmedi'
        },
        {
          id: 'facebook',
          name: 'Facebook',
          description: 'Facebook sayfa yorumlarını ve mesajlarını yönetin',
          icon: <FacebookIcon />,
          connected: !!facebookStats,
          status: facebookStats ? 'active' : 'warning',
          lastSync: facebookStats?.lastSync || 'Hiç senkronize edilmedi'
        }
      ]);

      setError(null);
    } catch (err) {
      console.error('Integration data fetch error:', err);
      setError('Entegrasyon verileri yüklenirken bir hata oluştu: ' + err.message);

      // Set default integrations with error status
      setIntegrations([
        {
          id: 'google',
          name: 'Google My Business',
          description: 'Google yorumlarını senkronize edin ve yanıtlayın',
          icon: <GoogleIcon />,
          connected: false,
          status: 'error',
          lastSync: 'Bağlantı hatası'
        },
        {
          id: 'facebook',
          name: 'Facebook',
          description: 'Facebook sayfa yorumlarını ve mesajlarını yönetin',
          icon: <FacebookIcon />,
          connected: false,
          status: 'error',
          lastSync: 'Bağlantı hatası'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleSync = async (integrationType) => {
    try {
      setSyncing(integrationType);
      if (integrationType === 'google') {
        await syncGoogleReviews(currentHotelId);
        alert('Google yorumları senkronizasyonu başlatıldı!');
      }
      // Refresh data after sync
      setTimeout(fetchIntegrationData, 2000);
    } catch (err) {
      console.error('Sync error:', err);
      alert('Senkronizasyon hatası: ' + err.message);
    } finally {
      setSyncing(null);
    }
  };

  useEffect(() => {
    fetchIntegrationData();

    // Auto refresh every 5 minutes
    const interval = setInterval(fetchIntegrationData, 300000);
    return () => clearInterval(interval);
  }, [currentHotelId]);

  const getStatusChip = (status) => {
    const statusMap = {
      'active': { label: 'Aktif', color: 'success', icon: <CheckIcon /> },
      'warning': { label: 'Uyarı', color: 'warning', icon: <ErrorIcon /> },
      'error': { label: 'Hata', color: 'error', icon: <ErrorIcon /> }
    };
    
    const statusInfo = statusMap[status] || { label: 'Bilinmiyor', color: 'default' };
    return (
      <Chip
        label={statusInfo.label}
        color={statusInfo.color}
        size="small"
        icon={statusInfo.icon}
      />
    );
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom fontWeight="bold">
        Entegrasyonlar
      </Typography>
      <Typography variant="body1" color="text.secondary" mb={3}>
        Üçüncü taraf servisleri ile entegrasyonlarınızı yönetin.
      </Typography>

      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Bağlı Servisler
              </Typography>
              <List>
                {integrations.map((integration) => (
                  <ListItem key={integration.id} divider>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        {integration.icon}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={integration.name}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {integration.description}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Son senkronizasyon: {integration.lastSync}
                          </Typography>
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      <Box display="flex" alignItems="center" gap={2}>
                        {getStatusChip(integration.status)}
                        <FormControlLabel
                          control={
                            <Switch
                              checked={integration.connected}
                              color="primary"
                            />
                          }
                          label=""
                        />
                        <Button
                          variant="outlined"
                          size="small"
                          startIcon={<SettingsIcon />}
                          disabled={syncing === integration.id}
                          onClick={() => handleSync(integration.id)}
                        >
                          {syncing === integration.id ? 'Senkronize Ediliyor...' : 'Senkronize Et'}
                        </Button>
                      </Box>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Google My Business
              </Typography>
              <Typography variant="body2" color="text.secondary" mb={2}>
                Google yorumlarını otomatik olarak senkronize edin ve AI ile yanıtlar oluşturun.
              </Typography>
              <Box mb={2}>
                <Typography variant="body2" gutterBottom>
                  <strong>Durum:</strong>
                </Typography>
                {googleStats ? (
                  <>
                    <Typography variant="body2">• {googleStats.reviewCount} yorum bulundu</Typography>
                    <Typography variant="body2">• Son senkronizasyon: {googleStats.lastSync}</Typography>
                    <Typography variant="body2">• Durum: Aktif</Typography>
                  </>
                ) : (
                  <Typography variant="body2" color="error">• Bağlantı kurulamadı</Typography>
                )}
              </Box>
              <Button
                variant="contained"
                fullWidth
                disabled={syncing === 'google'}
                onClick={() => handleSync('google')}
              >
                {syncing === 'google' ? 'Senkronize Ediliyor...' : 'Yorumları Senkronize Et'}
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Facebook
              </Typography>
              <Typography variant="body2" color="text.secondary" mb={2}>
                Facebook sayfa yorumlarını ve özel mesajlarını yönetin.
              </Typography>
              <Box mb={2}>
                <Typography variant="body2" gutterBottom>
                  <strong>Durum:</strong>
                </Typography>
                {facebookStats ? (
                  <>
                    <Typography variant="body2">• {facebookStats.commentCount} yorum bulundu</Typography>
                    <Typography variant="body2">• Son senkronizasyon: {facebookStats.lastSync}</Typography>
                    <Typography variant="body2">• Durum: Aktif</Typography>
                  </>
                ) : (
                  <Typography variant="body2" color="error">• Bağlantı kurulamadı</Typography>
                )}
              </Box>
              <Button
                variant="contained"
                fullWidth
                disabled={syncing === 'facebook'}
                onClick={() => handleSync('facebook')}
              >
                {syncing === 'facebook' ? 'Senkronize Ediliyor...' : 'Mesajları Kontrol Et'}
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Integrations;
