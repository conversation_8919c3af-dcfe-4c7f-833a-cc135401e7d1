import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Switch,
  FormControlLabel,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Google as GoogleIcon,
  Facebook as FacebookIcon,
  Settings as SettingsIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon
} from '@mui/icons-material';

const Integrations = () => {
  const integrations = [
    {
      id: 'google',
      name: 'Google My Business',
      description: 'Google yorumlarını senkronize edin ve yanıtlayın',
      icon: <GoogleIcon />,
      connected: true,
      status: 'active',
      lastSync: '2 saat önce'
    },
    {
      id: 'facebook',
      name: 'Facebook',
      description: 'Facebook sayfa yorumlarını ve mesajlarını yönetin',
      icon: <FacebookIcon />,
      connected: true,
      status: 'warning',
      lastSync: '1 gün önce'
    }
  ];

  const getStatusChip = (status) => {
    const statusMap = {
      'active': { label: 'Aktif', color: 'success', icon: <CheckIcon /> },
      'warning': { label: 'Uyarı', color: 'warning', icon: <ErrorIcon /> },
      'error': { label: 'Hata', color: 'error', icon: <ErrorIcon /> }
    };
    
    const statusInfo = statusMap[status] || { label: 'Bilinmiyor', color: 'default' };
    return (
      <Chip
        label={statusInfo.label}
        color={statusInfo.color}
        size="small"
        icon={statusInfo.icon}
      />
    );
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom fontWeight="bold">
        Entegrasyonlar
      </Typography>
      <Typography variant="body1" color="text.secondary" mb={3}>
        Üçüncü taraf servisleri ile entegrasyonlarınızı yönetin.
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Bağlı Servisler
              </Typography>
              <List>
                {integrations.map((integration) => (
                  <ListItem key={integration.id} divider>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        {integration.icon}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={integration.name}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {integration.description}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Son senkronizasyon: {integration.lastSync}
                          </Typography>
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      <Box display="flex" alignItems="center" gap={2}>
                        {getStatusChip(integration.status)}
                        <FormControlLabel
                          control={
                            <Switch
                              checked={integration.connected}
                              color="primary"
                            />
                          }
                          label=""
                        />
                        <Button
                          variant="outlined"
                          size="small"
                          startIcon={<SettingsIcon />}
                        >
                          Ayarlar
                        </Button>
                      </Box>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Google My Business
              </Typography>
              <Typography variant="body2" color="text.secondary" mb={2}>
                Google yorumlarını otomatik olarak senkronize edin ve AI ile yanıtlar oluşturun.
              </Typography>
              <Box mb={2}>
                <Typography variant="body2" gutterBottom>
                  <strong>Son 30 gün:</strong>
                </Typography>
                <Typography variant="body2">• 23 yeni yorum</Typography>
                <Typography variant="body2">• 18 yanıt gönderildi</Typography>
                <Typography variant="body2">• Ortalama puan: 4.6/5</Typography>
              </Box>
              <Button variant="contained" fullWidth>
                Yorumları Senkronize Et
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Facebook
              </Typography>
              <Typography variant="body2" color="text.secondary" mb={2}>
                Facebook sayfa yorumlarını ve özel mesajlarını yönetin.
              </Typography>
              <Box mb={2}>
                <Typography variant="body2" gutterBottom>
                  <strong>Son 30 gün:</strong>
                </Typography>
                <Typography variant="body2">• 15 yeni yorum</Typography>
                <Typography variant="body2">• 32 özel mesaj</Typography>
                <Typography variant="body2">• 12 yanıt gönderildi</Typography>
              </Box>
              <Button variant="contained" fullWidth>
                Mesajları Kontrol Et
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Integrations;
