# app/ai/tools.py (<PERSON>ksik<PERSON>)
# <PERSON><PERSON> <PERSON><PERSON>, Gemini'ye sunulacak olan fonksi<PERSON><PERSON>n (araçların) sadece
# bildirimlerini (FunctionDeclaration) içerir. AI'nın ne yapabileceğini
# anlamasını sağlayan şema buradadır.

import google.generativeai as genai
from google.generativeai import types as genai_types

# Müsaitlik kontrolü için araç tanımı
get_room_availability_declaration = genai_types.FunctionDeclaration(
    name="get_room_availability",
    description="Belirli bir oda tipi için belirtilen tarihler arasında müsaitlik, fiyat ve temel özellikleri kontrol eder. Kullanıcıdan giriş tarihi, çıkış tarihi VEYA giriş tarihi ve konaklama süresi (kaç gece) alınmalıdır.",
    parameters={
        "type": "object",
        "properties": {
            "room_type": {"type": "string"},
            "check_in_date_str": {"type": "string", "description": "Giriş tarihi. LLM, kullanıcının doğal dil ifadelerini bugünün tarihini baz alarak YYYY-AA-GG formatına çevirmelidir."},
            "check_out_date_str": {"type": "string", "description": "Çıkış tarihi. LLM, kullanıcının verdiği çıkış tarihini veya (giriş tarihi + konaklama süresi) hesabını YYYY-AA-GG formatına çevirmelidir."}
        },
        "required": ["room_type", "check_in_date_str", "check_out_date_str"]
    }
)

# Oda detayları için araç tanımı
get_room_details_declaration = genai_types.FunctionDeclaration(
    name="get_room_details",
    description="Belirli bir oda tipinin kapasitesi, yatak tipi, manzarası, m² büyüklüğü, odaya özel olanaklar, genel fiyat aralığı ve otelde bu tipten kaç adet olduğu gibi detaylı özelliklerini getirir. Bu araç, oda resimlerinin varlığı hakkında bilgi (`has_images`, `image_urls_count`) de döndürebilir.",
    parameters={
        "type": "object",
        "properties": {"room_type": {"type": "string", "description": "Detayları istenen oda tipi (örn: 'Aile Odası')."}},
        "required": ["room_type"]
    }
)

# Oda resimlerini göstermek için araç tanımı
show_room_images_declaration = genai_types.FunctionDeclaration(
    name="show_room_images",
    description="Belirli bir oda tipine ait kaydedilmiş görselleri (resim URL'lerini) kullanıcıya sunmak üzere hazırlar. Kullanıcı bir odanın fotoğraflarını, resimlerini veya görsellerini görmek istediğinde bu araç çağrılmalıdır.",
    parameters={
        "type": "object",
        "properties": {"room_type": {"type": "string", "description": "Resimleri gösterilecek oda tipi."}},
        "required": ["room_type"]
    }
)

# Rezervasyon oluşturmak için araç tanımı
create_reservation_declaration = genai_types.FunctionDeclaration(
    name="create_reservation",
    description="Müsaitlik kontrolü yapıldıktan ve misafir onayladıktan sonra yeni bir rezervasyon oluşturur.",
    parameters={
        "type": "object",
        "properties": {
            "guest_name": {"type": "string", "description": "Misafirin adı."},
            "guest_surname": {"type": "string", "description": "Misafirin soyadı."},
            "guest_phone": {"type": "string", "description": "Misafirin telefon numarası."},
            "num_adults": {"type": "integer", "description": "Yetişkin sayısı."},
            "guest_email": {"type": "string", "description": "Misafirin e-posta adresi (isteğe bağlı, misafir vermek istemezse boş geçilebilir)."},
            "num_children": {"type": "integer", "description": "Çocuk sayısı (isteğe bağlı, varsayılan 0)."},
            "special_requests": {"type": "string", "description": "Misafirin özel istekleri veya notları (isteğe bağlı)."}
        },
        "required": ["guest_name", "guest_surname", "guest_phone", "num_adults"]
    }
)

# find_nearby_places aracını güncelleyelim
find_nearby_places_declaration = genai_types.FunctionDeclaration(
    name="find_nearby_places",
    description="Kullanıcı otelin yakınlarındaki bir yeri sorduğunda kullanılır. Örneğin: 'en yakın eczane', 'yakındaki restoranlar'.",
    parameters={
        "type": "object",
        "properties": {
            "place_type": {"type": "string", "description": "Aranacak yerin kategorisi. (örn: 'pharmacy', 'hospital', 'restaurant')."},
            "keyword": {"type": "string", "description": "Spesifik bir arama kelimesi. 'place_type' ile birlikte kullanılabilir."},
            "rank_by_distance": {"type": "boolean", "description": "Kullanıcı 'en yakın' gibi bir ifade kullanırsa True olarak ayarlanmalıdır. Bu, sonuçları uzaklığa göre sıralar."}
        },
    }
)

# /// YENİ ARAÇ: Yol Tarifi ///
get_directions_declaration = genai_types.FunctionDeclaration(
    name="get_directions",
    description="Kullanıcı otelden belirli bir yere (örn: 'Kız Kulesi', 'Acıbadem Hastanesi') nasıl gideceğini sorduğunda yol tarifi almak için kullanılır.",
    parameters={
        "type": "object",
        "properties": {
            "destination": {"type": "string", "description": "Gidilecek yerin adı veya adresi. Örneğin: 'Taksim Meydanı, İstanbul'"},
            "travel_mode": {"type": "string", "description": "Ulaşım türü. 'driving' (araç), 'walking' (yürüme), 'transit' (toplu taşıma) olabilir. Varsayılan 'driving'dir.", "enum": ["driving", "walking", "transit"]}
        },
        "required": ["destination"]
    }
)


# Tüm araç bildirimlerini bir listede topla
tools_list_for_gemini = [
    get_room_availability_declaration,
    get_room_details_declaration,
    show_room_images_declaration,
    create_reservation_declaration,
    find_nearby_places_declaration,
    get_directions_declaration
]

# Gemini'ye verilecek son Tool nesnesini oluştur
GEMINI_TOOLS_CONFIG = [genai_types.Tool(function_declarations=tools_list_for_gemini)] if tools_list_for_gemini else None