<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LiveChatAI Widget Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-info {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .status {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .status-item {
            padding: 10px;
            border-radius: 5px;
            flex: 1;
            text-align: center;
        }
        .status-loading { background: #fff3cd; color: #856404; }
        .status-success { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        
        .debug-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 LiveChatAI Widget Test</h1>
            <p>Chat widget'ının yüklenmesini ve çalışmasını test edin</p>
        </div>

        <div class="test-info">
            <h3>Test Bilgileri</h3>
            <p><strong>Hotel ID:</strong> <span id="hotel-id">myplushotelataşehir_f40e4c3f</span></p>
            <p><strong>Server URL:</strong> <span id="server-url">http://***********:5000</span></p>
            <p><strong>Widget Status:</strong> <span id="widget-status">Yükleniyor...</span></p>
        </div>

        <div class="status">
            <div class="status-item status-loading" id="css-status">
                CSS: Yükleniyor...
            </div>
            <div class="status-item status-loading" id="socketio-status">
                Socket.IO: Yükleniyor...
            </div>
            <div class="status-item status-loading" id="widget-status-item">
                Widget: Yükleniyor...
            </div>
        </div>

        <div class="debug-section">
            <h3>Debug Kontrolleri</h3>
            <button onclick="checkDependencies()">Bağımlılıkları Kontrol Et</button>
            <button onclick="testServerConnection()">Server Bağlantısını Test Et</button>
            <button onclick="clearConsole()">Console'u Temizle</button>
            <button onclick="reloadWidget()">Widget'ı Yeniden Yükle</button>
        </div>

        <div class="console-output" id="console-output">
            <div>LiveChatAI Widget Test Console</div>
            <div>================================</div>
        </div>
    </div>

    <!-- LiveChatAI Widget Script -->
    <script id="livechatai-loader-script"
            src="http://***********:5000/static/js/loader.js"
            data-hotel-id="myplushotelataşehir_f40e4c3f"
            defer>
    </script>

    <script>
        // Console override for debugging
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };

        const consoleOutput = document.getElementById('console-output');

        function addToConsole(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.style.color = type === 'error' ? '#ff6b6b' : 
                             type === 'warn' ? '#ffd93d' : 
                             type === 'info' ? '#74c0fc' : '#00ff00';
            div.textContent = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        // Override console methods
        console.log = function(...args) {
            originalConsole.log.apply(console, args);
            addToConsole('log', args.join(' '));
        };

        console.error = function(...args) {
            originalConsole.error.apply(console, args);
            addToConsole('error', args.join(' '));
        };

        console.warn = function(...args) {
            originalConsole.warn.apply(console, args);
            addToConsole('warn', args.join(' '));
        };

        console.info = function(...args) {
            originalConsole.info.apply(console, args);
            addToConsole('info', args.join(' '));
        };

        // Status update functions
        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `status-item status-${status}`;
            element.textContent = message;
        }

        // Debug functions
        function checkDependencies() {
            console.log('=== Bağımlılık Kontrolü ===');
            
            // CSS kontrolü
            const cssLink = document.querySelector('link[href*="my-chat-widget.css"]');
            console.log('CSS yüklü:', cssLink ? 'Evet' : 'Hayır');
            
            // Socket.IO kontrolü
            console.log('Socket.IO yüklü:', typeof window.io !== 'undefined' ? 'Evet' : 'Hayır');
            
            // Three.js kontrolü
            console.log('Three.js import map:', document.querySelector('script[type="importmap"]') ? 'Evet' : 'Hayır');
            
            // Widget ayarları kontrolü
            console.log('Widget ayarları:', window.myChatWidgetSettings ? 'Evet' : 'Hayır');
            
            // Widget instance kontrolü
            console.log('Widget instance:', window.myChatWidget ? 'Evet' : 'Hayır');
        }

        async function testServerConnection() {
            console.log('=== Server Bağlantı Testi ===');
            const serverUrl = 'http://***********:5000';
            
            try {
                // Health check
                const healthResponse = await fetch(`${serverUrl}/health`);
                console.log('Health check:', healthResponse.ok ? 'Başarılı' : 'Başarısız');
                
                // CSS dosyası kontrolü
                const cssResponse = await fetch(`${serverUrl}/static/css/my-chat-widget.css`);
                console.log('CSS dosyası:', cssResponse.ok ? 'Erişilebilir' : 'Erişilemiyor');
                
                // Widget.js kontrolü
                const widgetResponse = await fetch(`${serverUrl}/static/js/widget.js`);
                console.log('Widget.js:', widgetResponse.ok ? 'Erişilebilir' : 'Erişilemiyor');
                
            } catch (error) {
                console.error('Server bağlantı hatası:', error.message);
            }
        }

        function clearConsole() {
            consoleOutput.innerHTML = `
                <div>LiveChatAI Widget Test Console</div>
                <div>================================</div>
            `;
        }

        function reloadWidget() {
            console.log('Widget yeniden yükleniyor...');
            location.reload();
        }

        // Widget yükleme durumunu izle
        let checkInterval = setInterval(() => {
            // CSS kontrolü
            if (document.querySelector('link[href*="my-chat-widget.css"]')) {
                updateStatus('css-status', 'success', 'CSS: Yüklendi ✓');
            }
            
            // Socket.IO kontrolü
            if (typeof window.io !== 'undefined') {
                updateStatus('socketio-status', 'success', 'Socket.IO: Yüklendi ✓');
            }
            
            // Widget kontrolü
            if (window.myChatWidget) {
                updateStatus('widget-status-item', 'success', 'Widget: Aktif ✓');
                document.getElementById('widget-status').textContent = 'Aktif ve Çalışıyor';
                clearInterval(checkInterval);
            }
        }, 1000);

        // 30 saniye sonra timeout
        setTimeout(() => {
            if (!window.myChatWidget) {
                updateStatus('widget-status-item', 'error', 'Widget: Timeout ✗');
                document.getElementById('widget-status').textContent = 'Yükleme Başarısız';
                clearInterval(checkInterval);
            }
        }, 30000);

        console.log('Test sayfası hazır. Widget yüklenmesi bekleniyor...');
    </script>
</body>
</html>
