#// Do<PERSON>a Adı: app/__init__.py
#// --- <PERSON><PERSON><PERSON> ve Tam Olarak Güncellenmiş Kod ---

import os
import threading
import json
import sqlite3
import redis 
from flask import Flask, session 
from flask_cors import CORS
# SocketIO import Redis client tanımından sonra yapılacak
from dotenv import load_dotenv
from flask_session import Session 
import logging

gunicorn_logger = logging.getLogger('gunicorn.error')
app_logger = logging.getLogger('app') # Uygulamamız için ayrı bir logger
app_logger.handlers = gunicorn_logger.handlers
app_logger.setLevel(gunicorn_logger.level)
# .env dosyasındaki ortam değişkenlerini yükle
load_dotenv()

# config modülünden gerekli yapılandırma değerlerini içe aktar
from .config import FLASK_SECRET_KEY, CONFIG_DIR, REDIS_HOST, REDIS_PORT,SESSION_REDIS_PREFIX

# === "KURŞUN GEÇİRMEZ" YOL TANIMLAMA ===

# 1. Bu dosyanın (__init__.py) bulunduğu dizinin tam yolunu al.
# Örn: C:/Users/<USER>/Desktop/proje_klasoru/app
current_dir = os.path.dirname(os.path.abspath(__file__))

# 2. Projenin kök dizinini bul (yani 'app' klasörünün bir üstü).
# Örn: C:/Users/<USER>/Desktop/proje_klasoru
base_dir = os.path.abspath(os.path.join(current_dir, '..'))

# 3. 'static' ve 'templates' klasörlerinin mutlak yollarını oluştur.
# Örn: C:/Users/<USER>/Desktop/proje_klasoru/static
static_folder_path = os.path.join(base_dir, 'static')
template_folder_path = os.path.join(base_dir, 'templates')

# --- Hata Ayıklama için Ekrana Yazdır (Sunucu başlarken kontrol et) ---
app_logger.info(f"[*] Proje Kök Dizini: {base_dir}")
app_logger.info(f"[*] Static Klasör Yolu: {static_folder_path}")
app_logger.info(f"[*] Template Klasör Yolu: {template_folder_path}")
# ----------------------------------------------------------------------

# 4. Flask uygulamasını bu MUTLAK yollarla oluştur.
# static_url_path='/static' kalmalı, bu tarayıcının kullanacağı URL'dir.
app = Flask(__name__,
            static_folder=static_folder_path,
            template_folder=template_folder_path,
            static_url_path='/static')

app.config['SECRET_KEY'] = FLASK_SECRET_KEY

# Sunucu tarafında session tutmak için Flask-Session'ı yapılandır.
app.config['SESSION_TYPE'] = 'filesystem'
app.config['SESSION_FILE_DIR'] = './.flask_session'
app.config['SESSION_PERMANENT'] = False
app.config['SESSION_USE_SIGNER'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'

# Session nesnesini başlat
Session(app)

# === MERKEZİ MESAJLAŞMA SİSTEMİ (REDIS) ===
# Gunicorn'un farklı worker'ları arasında iletişim kurmak için Redis Pub/Sub kullanacağız.
# 'gthread' worker'ı ile de uyumludur, ancak çoklu process worker'ları için zorunludur.
message_queue = f"redis://{REDIS_HOST}:{REDIS_PORT}/0"

# CORS ayarlarını daha esnek hale getirelim.
# '*' tüm domainlere izin verir, bu test için iyidir.
# Üretimde daha spesifik olmak isterseniz aşağıdaki listeyi kullanabilirsiniz.
allowed_origins = "*" 

# Alternatif (Daha Güvenli):
# allowed_origins = [
#     "https://resepsiyonapi.rehberim360.com",
#     "https://www.rehberim360.com",
#     "http://www.rehberim360.com",
#     
#     # myplushotelatasehir için TÜM varyasyonlar
#     "https://www.myplushotelatasehir.com",
#     "https://myplushotelatasehir.com",      
#     "http://www.myplushotelatasehir.com",
#     "http://myplushotelatasehir.com",
#     "http://127.0.0.1:5500" # Yerel testler için (Live Server gibi)
# ]

# SocketIO konfigürasyonu Redis client tanımından sonra yapılacak

# --- Paylaşılan Veri ve Kilitler ---
# Bu bölümde, uygulama genelinde birden fazla thread'in aynı anda erişebileceği
# global değişkenleri ve bu erişimi güvenli hale getiren kilitleri tanımlıyoruz.

# Otel konfigürasyonlarını RAM'de tutar (hızlı erişim için)
otel_configurations = {}
otel_configs_lock = threading.Lock()

# Aktif sohbet oturumlarını (Gemini Chat objesi, konfigürasyon vb.) tutar.
# Key: session_id, Value: session_data (dict)
active_chat_sessions = {}

# /// YENİ VE KRİTİK EKLEME ///
# 'active_chat_sessions' sözlüğüne aynı anda birden fazla Socket.IO isteğinin 
# erişmesini engellemek için bir kilit (Lock) nesnesi oluşturuyoruz.
# Bu, 'AttributeError: 'dict' object has no attribute 'lock'' hatasını çözer.
session_lock = threading.Lock()

# === REDIS İSTEMCİSİ OLUŞTURMA ===
# Mesajlarını Redis'e göndermek için kullanılacak istemci.
# `decode_responses=True` sayesinde Redis'ten gelen yanıtlar otomatik olarak string'e çevrilir.
try:
    redis_client = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, decode_responses=True)
    redis_client.ping() # Bağlantıyı test et
    app_logger.info(f"[*] Redis'e başarıyla bağlandı: {REDIS_HOST}:{REDIS_PORT}")
except redis.exceptions.ConnectionError as e:
    app_logger.info(f"[!!!] KRİTİK HATA: Redis'e bağlanılamadı: {e}")
    app_logger.info("[!!!] Socket.IO bildirimleri çoklu worker ortamında ÇALIŞMAYACAKTIR!")
    redis_client = None # Hata durumunda istemciyi None yap

# === SOCKET.IO KONFIGÜRASYONU (Redis client tanımından sonra) ===
# SocketIO'yu Redis fallback ile başlat
from flask_socketio import SocketIO

try:
    if redis_client and message_queue:
        # Redis varsa, çoklu worker desteği için message_queue kullan
        socketio = SocketIO(
            app,
            cors_allowed_origins=allowed_origins,
            message_queue=message_queue,
            async_mode='threading',
            logger=False,  # Redis hatalarını azaltmak için
            engineio_logger=False
        )
        app_logger.info("SocketIO Redis message queue ile başlatıldı (çoklu worker desteği).")
    else:
        raise Exception("Redis not available")

except Exception as e:
    # Redis bağlantı hatası veya Redis yoksa, basit in-memory mode
    app_logger.warning(f"Redis message queue başlatılamadı: {e}")
    socketio = SocketIO(
        app,
        cors_allowed_origins=allowed_origins,
        async_mode='threading',
        logger=False,  # Hata loglarını azalt
        engineio_logger=False
    )
    app_logger.info("SocketIO basit mode ile başlatıldı (tek worker, Redis fallback).")

# === STATELESS ARCHITECTURE: WORKER HEARTBEAT ===
# Her worker düzenli olarak Redis'e heartbeat gönderir
def setup_worker_heartbeat():
    """Worker heartbeat sistemini başlatır"""
    if redis_client:
        from app.utils import register_worker_heartbeat, cleanup_expired_sessions
        import threading
        import time

        def heartbeat_worker():
            while True:
                try:
                    register_worker_heartbeat()
                    cleanup_expired_sessions()  # Periyodik temizlik
                    time.sleep(60)  # Her dakika heartbeat
                except Exception as e:
                    app_logger.exception("Worker heartbeat error")
                    time.sleep(60)

        # Background thread olarak başlat
        heartbeat_thread = threading.Thread(target=heartbeat_worker, daemon=True)
        heartbeat_thread.start()
        app_logger.info("Worker heartbeat system started")

# Uygulama başlatıldığında heartbeat'i başlat
setup_worker_heartbeat()

# --- Uygulama Başlangıç Fonksiyonları ---

# Veritabanını başlatan ve konfigürasyonları yükleyen fonksiyonları içe aktar
from .database import init_db

def load_otel_configurations_from_files():
    # Bu fonksiyonun içeriğinde bir değişiklik yok, olduğu gibi kalabilir.
    # ... (sizin mevcut load_otel_configurations_from_files kodunuz) ...
    from .database import get_db_connection 
    from datetime import datetime

    if not os.path.exists(CONFIG_DIR):
        os.makedirs(CONFIG_DIR)
        app_logger.info(f"Konfigürasyon klasörü '{CONFIG_DIR}' oluşturuldu.")
        return
    
    conn_inv = None
    try:
        conn_inv = get_db_connection()
        cursor_inv = conn_inv.cursor()
        current_time_iso = datetime.now().isoformat(timespec='seconds')
        for filename in os.listdir(CONFIG_DIR):
            if filename.endswith(".json"):
                config_key = filename[:-5]
                filepath = os.path.join(CONFIG_DIR, filename)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    with otel_configs_lock:
                        otel_configurations[config_key] = config_data
                    app_logger.info(f"CONFIG YÜKLENDİ (Başlangıç): '{config_key}' dosyadan RAM'e yüklendi.")
                    if 'roomTypes' in config_data and isinstance(config_data['roomTypes'], list):
                        for rt_data in config_data['roomTypes']:
                            if isinstance(rt_data, dict) and rt_data.get('name'):
                                rt_name = rt_data.get('name')
                                rt_total_qty = int(rt_data.get('totalRoomsOfType', 0))
                                cursor_inv.execute("SELECT id FROM room_types_inventory WHERE hotel_config_key = ? AND room_type_name = ?", (config_key, rt_name))
                                if cursor_inv.fetchone():
                                    cursor_inv.execute("UPDATE room_types_inventory SET total_quantity = ?, last_updated = ? WHERE hotel_config_key = ? AND room_type_name = ?", (rt_total_qty, current_time_iso, config_key, rt_name))
                                else:
                                    cursor_inv.execute("INSERT INTO room_types_inventory (hotel_config_key, room_type_name, total_quantity, last_updated) VALUES (?, ?, ?, ?)", (config_key, rt_name, rt_total_qty, current_time_iso))
                        conn_inv.commit()
                except Exception as e_load:
                    app_logger.info(f"HATA: '{filepath}' yüklenirken sorun oluştu: {e_load}")
    finally:
        if conn_inv:
            conn_inv.close()

from . import routes, database, utils, ai
# Veritabanını başlat ve yapılandırmaları yükle
init_db()
load_otel_configurations_from_files()

# Route'ları ve WebSocket olay işleyicilerini içe aktararak kaydet
# Bu import'ların, app ve diğer değişkenler tanımlandıktan SONRA yapılması kritiktir.
from .routes import http_routes, socketio_handlers

# Başlangıç mesajları
app_logger.info(f"Otel Resepsiyon Asistanı Backend Başlatılıyor...")
from .config import GEMINI_API_KEY, MODEL_NAME, FLASK_RUN_HOST, FLASK_RUN_PORT, FLASK_DEBUG_MODE
if GEMINI_API_KEY:
    app_logger.info("Gemini API Anahtarı yüklendi.")
else:
    app_logger.info("UYARI: Gemini API Anahtarı YÜKLENEMEDİ. .env dosyasını kontrol edin.")
app_logger.info(f"Kullanılan Gemini Modeli: {MODEL_NAME}")
app_logger.info(f"SocketIO sunucusu http://{FLASK_RUN_HOST}:{FLASK_RUN_PORT} adresinde çalışıyor (Debug Modu: {FLASK_DEBUG_MODE})")