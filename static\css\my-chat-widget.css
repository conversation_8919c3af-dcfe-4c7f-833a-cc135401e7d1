/* =================================================================
   MyChatWidget - V3 (Performans & Estetik Odaklı)
   ================================================================= */

/* === 1. TEMEL DEĞİŞKENLER VE AYARLAR === */
:root {
    /* Renk Paleti ve Temel Değişkenler */
    --widget-primary-color: #007AFF;
    --widget-header-text-color: #ffffff;
    --widget-background-color: #F7F7F7;
    --widget-text-color: #1c1c1e;
    --widget-border-radius: 18px;
    --widget-bubble-ai-bg: #E9E9EB;
    --widget-bubble-ai-text: #1c1c1e;
    --widget-bubble-user-bg: var(--widget-primary-color);
    --widget-bubble-user-text: #ffffff;
    --widget-input-bg: #FFFFFF;
    --widget-input-border-color: #E0E0E0;
    --widget-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
    --font-size-body: 15px; /* Mesajlar için temel boyut */
    --font-size-small: 12px; /* Zaman damgaları için */
    --font-size-header: 16px; /* Başlık için */
    --widget-avatar-size: 32px;
    --widget-header-height: 55px;

    /* Yeni Değişkenler */
    --widget-soft-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* === 2. TEMEL WIDGET YAPISI VE LAUNCHER === */



.my-chat-launcher {
    background-color: var(--widget-primary-color);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 28px;
    border: none;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    transition: transform 0.2s, box-shadow 0.2s, opacity 0.3s;
}

.my-chat-launcher:hover { transform: scale(1.1); }

.my-chat-window {
    width: 370px;
    height: 650px;
    max-height: calc(100vh - 100px);
    background-color: var(--widget-background-color);
    border-radius: var(--widget-border-radius);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: opacity 0.3s ease, transform 0.3s ease;
    transform-origin: bottom right;
    position: absolute;
    bottom: calc(100% + 10px);
    right: 0;
}

.my-chat-window.my-chat-hidden {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
    pointer-events: none;
    visibility: hidden;
}


/* === 3. MOBİL ÖNCELİKLİ TASARIM (@media) === */
@media (max-width: 480px) {
    .my-chat-window {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        max-height: 100%;
        border-radius: 0;
        box-shadow: none;
        z-index: 2147483647;
    }

    /* Pencere açıkken launcher'ı gizle */
    .my-chat-window:not(.my-chat-hidden) + .my-chat-launcher {
        opacity: 0;
        transform: scale(0.8);
        pointer-events: none;
    }
}

/* === 4. EKRAN GÖRÜNÜMLERİ (Welcome, Chat, Avatar) === */

.my-chat-welcome-screen,
.my-chat-main-container,
.my-chat-avatar-scene-container {
    display: none; /* JS tarafından yönetilecek, başlangıçta gizli */
    flex-direction: column;
    height: 100%;
    width: 100%;
    overflow: hidden;
}

/* Karşılama Ekranı Stilleri */
.my-chat-welcome-screen {
    justify-content: center;
    align-items: center;
    padding: 40px 20px;
    box-sizing: border-box;
    text-align: center;
}

.my-chat-welcome-logo {
    width: 80px; height: 80px; border-radius: 50%;
    object-fit: cover; margin-bottom: 20px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
}
.my-chat-welcome-title {
    font-size: 1.4em; font-weight: 600;
    color: var(--widget-text-color); margin-bottom: 10px;
}
.my-chat-welcome-message {
    font-size: 0.95em; color: #666;
    line-height: 1.5; margin-bottom: 30px; max-width: 90%;
}
.my-chat-welcome-options {
    display: flex; flex-direction: column;
    gap: 15px; width: 100%; max-width: 250px;
}
.my-chat-welcome-button {
    width: 100%; padding: 12px; font-size: 1em;
    font-weight: 500; border-radius: 12px; border: none;
    cursor: pointer; transition: all 0.2s ease;
}
.my-chat-welcome-button:hover { transform: translateY(-2px); }
.my-chat-welcome-button.primary { background-color: var(--widget-primary-color); color: var(--widget-header-text-color); }
.my-chat-welcome-button.secondary { background-color: #fff; color: var(--widget-text-color); border: 1px solid var(--widget-input-border-color); }

/* === 5. ORTAK BİLEŞENLER (Header, Footer) === */

.my-chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    height: var(--widget-header-height);
    background-color: var(--widget-primary-color);
    color: var(--widget-header-text-color);
    flex-shrink: 0;
    z-index: 10;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}
.my-chat-header-left-content, .my-chat-header-right {
    display: flex; align-items: center; gap: 10px;
}
.my-chat-header-logo { height: calc(var(--widget-header-height) * 0.6); width: auto; }
.my-chat-header-title-text { font-weight: 600; font-size: 16px; }
.my-chat-header-button {
    background: none; border: none; color: inherit; font-size: 22px;
    cursor: pointer; padding: 5px; line-height: 1; display: flex;
    align-items: center; justify-content: center;
}

.my-chat-footer {
    text-align: center; padding: 8px 0; font-size: 0.7em;
    color: #AEAEB2; background-color: var(--widget-input-bg);
    border-top: 1px solid var(--widget-input-border-color); flex-shrink: 0;
}

/* === 6. ANA SOHBET EKRANI (Mesajlar, Input) === */

.my-chat-message-area {
    flex-grow: 1;
    overflow-y: auto;
    padding: 20px 15px;
    display: flex;
    flex-direction: column;
    -webkit-overflow-scrolling: touch; /* iOS'ta yumuşak kaydırma */
}
.my-chat-message-area::-webkit-scrollbar { width: 6px; }
.my-chat-message-area::-webkit-scrollbar-track { background: transparent; }
.my-chat-message-area::-webkit-scrollbar-thumb { background-color: rgba(0,0,0,0.15); border-radius: 3px; }

/* Mesaj Baloncukları ve Performanslı Animasyon */
.my-chat-message-entry {
    display: flex;
    flex-direction: column;
    margin-bottom: 12px;
    max-width: 95%;
    will-change: transform, opacity; /* PERFORMANS ARTIŞI */
}
.my-chat-message-entry.new-message-animation {
    animation: fade-in-up 0.5s cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
}
@keyframes fade-in-up {
    from { opacity: 0; transform: translateY(15px); }
    to { opacity: 1; transform: translateY(0); }
}

.my-chat-message-entry.user { align-self: flex-end; align-items: flex-end; }
.my-chat-message-entry.ai { align-self: flex-start; align-items: flex-start; }

.my-chat-message-wrapper { display: flex; align-items: flex-end; gap: 10px; }
.my-chat-message-avatar {
    width: var(--widget-avatar-size); height: var(--widget-avatar-size);
    border-radius: 50%; flex-shrink: 0; overflow: hidden;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}
.my-chat-message-avatar img { width: 100%; height: 100%; object-fit: cover; }

.my-chat-message {
    padding: 10px 15px; border-radius: var(--widget-border-radius);
    line-height: 1.2; font-size: 0.85em;
    box-shadow: var(--widget-soft-shadow);
    word-break: break-word;
}
.my-chat-message-user {
    background: var(--widget-bubble-user-bg); color: var(--widget-bubble-user-text);
    border-bottom-right-radius: 6px;
}
.my-chat-message-ai {
    background: var(--widget-bubble-ai-bg); color: var(--widget-bubble-ai-text);
    border-bottom-left-radius: 6px;
}

/* Zaman Damgası ve Sistem Mesajı */
.my-chat-timestamp-container {
    font-size: 0.7em; color: #999; margin-top: 4px; padding: 0 5px;
}
.my-chat-message-system {
    width: calc(100% - 30px); align-self: center; background: transparent;
    color: #555; text-align: center; font-size: 0.85em;
    padding: 8px 0; box-shadow: none; margin: 5px 0;
}

/* Yazıyor Göstergesi */
.my-chat-typing-indicator {
    display: flex; align-items: center; padding: 10px 20px;
    height: 40px; /* Sabit yükseklik, zıplamayı önler */
}
.my-chat-typing-indicator.my-chat-hidden { display: none; }
.my-chat-typing-indicator .typing-dot {
    width: 8px; height: 8px; background-color: #ccc;
    border-radius: 50%; margin: 0 2px;
    will-change: transform; /* PERFORMANS ARTIŞI */
    animation: typing-bounce 1.4s infinite ease-in-out both;
}
@keyframes typing-bounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1.0); }
}
.my-chat-typing-indicator .typing-dot:nth-child(1) { animation-delay: -0.32s; }
.my-chat-typing-indicator .typing-dot:nth-child(2) { animation-delay: -0.16s; }

/* Mobil Uyumlu Input Alanı */
.my-chat-input-area textarea {
    flex-grow: 1;
    border: none;
    padding: 8px 15px; /* Dikey padding'i azalt */
    /* Gönder butonu için sağda boşluk bırak */
    padding-right: 45px; 
    resize: none;
    min-height: 36px; /* YENİ İNCE YÜKSEKLİK */
    max-height: 100px;
    font-size: 16px;
    line-height: 1.4;
    outline: none;
    background-color: #f0f2f5; /* Facebook Messenger benzeri bir renk */
    border-radius: 20px; /* Daha yuvarlak */
    color: var(--widget-text-color);
    box-sizing: border-box;
    transition: padding 0.2s ease;
}
.my-chat-input-area textarea {
    flex-grow: 1; border: none; padding: 10px; resize: none;
    min-height: 20px; max-height: 100px;
    font-size: 16px; line-height: 1.4;
    outline: none; background-color: #f4f4f4;
    border-radius: 18px; color: var(--widget-text-color);
}
/* Gereksiz wrapper'ı gizle (eğer içinde başka bir şey kalmadıysa) */
.my-chat-input-icons-wrapper {
    display: none; /* Artık bu wrapper'a ihtiyacımız yok, her şey absolute */
}
.my-chat-input-area .input-icon {
    background: none; border: none; font-size: 24px;
    color: #8A8A8E; cursor: pointer; padding: 8px;
    transition: color 0.2s;
}
.my-chat-input-area .input-icon:hover { color: var(--widget-primary-color); }
.my-chat-input-area .send-button-icon {
    position: absolute;
    right: 15px;
    bottom: 11px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: var(--widget-primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    font-size: 16px; /* SVG veya font ikonu için boyut */
    
    /* Başlangıçta gizli ve küçük */
    opacity: 0;
    transform: scale(0.5);
    pointer-events: none;
    
    transition: opacity 0.2s ease, transform 0.2s ease;
}
/* JS ile eklenecek 'has-text' sınıfı ile butonu görünür yap */
.my-chat-input-area.has-text .send-button-icon {
    opacity: 1;
    transform: scale(1);
    pointer-events: all;
}
/* Chat ekranındaki mikrofonu tamamen gizle */
.my-chat-main-container .my-chat-input-area .mic-button {
    display: none !important;
}


/* === 7. AVATAR SAHNESİ VE BİLEŞENLERİ === */

.my-chat-avatar-canvas-wrapper { flex-grow: 1; position: relative; }
#my-chat-avatar-canvas { width: 100%; height: 100%; display: block; }

/* Yükleme Spinner'ı */
.my-chat-loader-overlay {
    position: absolute; top: 0; left: 0; width: 100%; height: 100%;
    background-color: rgba(0, 0, 0, 0.5); display: flex;
    justify-content: center; align-items: center;
    z-index: 10; opacity: 0; visibility: hidden;
    transition: opacity 0.3s, visibility 0s 0.3s;
}
.my-chat-loader-overlay.visible { opacity: 1; visibility: visible; transition: opacity 0.3s; }
.my-chat-spinner {
    width: 50px; height: 50px; border: 4px solid #f3f3f3;
    border-top: 4px solid var(--widget-primary-color);
    border-radius: 50%; animation: spin 1s linear infinite;
}
@keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }

.my-chat-connecting-screen {
    display: none; /* Varsayılan olarak gizli */
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    background-color: var(--widget-widgetBackgroundColor, #F7F7F7);
}
.my-chat-connecting-text {
    margin-top: 15px;
    font-size: 14px;
    color: var(--widget-textColor, #1c1c1e);
}
/* === 7. AVATAR SAHNESİ VE BİLEŞENLERİ === */
/* ... (diğer avatar stilleri) ... */

/* Toggle Butonunun Sarmalayıcısı */
.my-chat-ptt-wrapper {
    position: absolute;
    bottom: 30px;
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    pointer-events: none; /* Sadece içindeki buton tıklanabilir olsun */
}
/* =================================================================
   MyChatWidget - V4 (Modern UI/UX & İzolasyonlu)
   ================================================================= */

/* === 1. TEMEL DEĞİŞKENLER VE TİPOGRAFİ === */
.my-chat-widget-container {
    /* Renk Paleti */
    --widget-primary-color: #007AFF;
    --widget-header-text-color: #ffffff;
    --widget-background-color: #F7F7F7;
    --widget-text-color: #1c1c1e;
    --widget-border-radius: 18px;
    --widget-bubble-ai-bg: #E9E9EB;
    --widget-bubble-user-bg: var(--widget-primary-color);
    --widget-bubble-user-text: #ffffff;
    --widget-input-bg: #F0F2F5; /* Modern gri arka plan */
    --widget-input-border-color: #E0E0E0;
    --widget-soft-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

    /* Yazı Tipi ve Boyutları */
    --widget-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
    --font-size-body: 15px;      /* Mesajlar için temel boyut */
    --font-size-small: 12px;     /* Zaman damgaları ve footer için */
    --font-size-header: 0.85rem;    /* Başlık için */
    --font-size-input: 16px;     /* Input alanı için */

    /* Diğer Ölçüler */
    --widget-avatar-size: 32px;
    --widget-header-height: 55px;
}

/* === 2. YENİ LAUNCHER (AÇMA BUTONU) TASARIMI === */
.my-chat-widget-container .my-chat-launcher {
    background-color: #FFFFFF;
    color: #000000;
    width: auto; /* Genişliği içeriğe göre ayarla */
    height: 56px;
    border-radius: 28px; /* Kapsül şekli */
    font-size: 16px;
    border: 1px solid #E5E5E5;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 20px 0 10px; /* İç boşluklar */
    gap: 12px; /* Elemanlar arası boşluk */
    transition: transform 0.2s, box-shadow 0.2s;
}

.my-chat-widget-container .my-chat-launcher:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.my-chat-widget-container .launcher-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}
.my-chat-widget-container .launcher-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.my-chat-widget-container .launcher-text {
    font-weight: 600;
    font-size: 15px;
    white-space: nowrap;
}

/* === 3. ANA WIDGET YAPISI === */
.my-chat-widget-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 2147483646; /* Lightbox'tan bir düşük olsun */
}

.my-chat-widget-container .my-chat-window {
    width: 370px;
    height: 70vh;
    min-height: 450px;
    max-height: 650px;
    background-color: var(--widget-background-color);
    border-radius: var(--widget-border-radius);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: opacity 0.3s ease, transform 0.3s ease;
    transform-origin: bottom right;
    position: absolute;
    bottom: calc(100% + 10px);
    right: 0;
}
.my-chat-widget-container .my-chat-window.my-chat-hidden {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
    pointer-events: none;
    visibility: hidden;
}

/* === 4. MOBİL UYUMLULUK === */
@media (max-width: 480px) {
    .my-chat-widget-container .my-chat-window {
        position: fixed;
        top: 0; left: 0;
        width: 100%; height: 100%;
        min-height: 100%; max-height: 100%;
        border-radius: 0;
        box-shadow: none;
    }
    .my-chat-widget-container .my-chat-window:not(.my-chat-hidden) + .my-chat-launcher {
        display: none;
    }
}

/* === 5. EKRAN GÖRÜNÜMLERİ (Welcome, Chat, Avatar) === */
.my-chat-widget-container .my-chat-welcome-screen,
.my-chat-widget-container .my-chat-main-container,
.my-chat-widget-container .my-chat-avatar-scene-container {
    display: none; /* JS tarafından yönetilecek */
    flex-direction: column;
    height: 100%;
    width: 100%;
    background-color: var(--widget-background-color);
}

/* Karşılama Ekranı Stilleri (Mevcut hali iyi, küçük ayarlar) */
.my-chat-widget-container .my-chat-welcome-title { font-size: 22px; }
.my-chat-widget-container .my-chat-welcome-message { font-size: var(--font-size-body); }

/* === 6. HEADER (MODERN İKONLARLA) === */
.my-chat-widget-container .my-chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px 0 15px;
    height: var(--widget-header-height);
    background-color: #FFFFFF; /* Arka planı beyaz yapalım */
    color: var(--widget-text-color); /* Metin rengi siyah */
    border-bottom: 1px solid var(--widget-input-border-color); /* İnce bir çizgi */
    flex-shrink: 0;
    z-index: 10;
}
.my-chat-widget-container .my-chat-header-title-text {
    font-weight: 600;
    font-size: var(--font-size-header);
}
.my-chat-widget-container .my-chat-header-button {
    background: none; border: none;
    color: #888888; /* İkon rengi */
    cursor: pointer;
    padding: 8px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s;
}
.my-chat-widget-container .my-chat-header-button:hover {
    color: var(--widget-primary-color);
}
.my-chat-widget-container .my-chat-header-button svg {
    width: 22px;
    height: 22px;
}

/* === 7. MESAJ ALANI === */
.my-chat-widget-container .my-chat-message-area {
    flex-grow: 1;
    overflow-y: auto;
    padding: 20px 15px;
}
.my-chat-widget-container .my-chat-message {
    font-size: var(--font-size-body);
}
.my-chat-widget-container .my-chat-timestamp-container {
    font-size: var(--font-size-small);
}

/* === 8. YENİ INPUT ALANI TASARIMI === */
.my-chat-widget-container .my-chat-input-area {
    /* Konumlandırmanın çalışması için referans noktası (anchor) olarak ayarla */
    position: relative;
    padding: 8px 50px 8px 8px;
    background-color: #FFFFFF;
    border-top: 1px solid var(--widget-input-border-color);
    flex-shrink: 0;
}
.my-chat-widget-container .my-chat-textarea-wrapper {
    flex-grow: 1; /* Büyüyecek olan eleman bu sarmalayıcıdır. */
    display: flex; /* İçindeki textarea'nın tam oturması için */
}
.my-chat-widget-container .my-chat-input-area textarea {
    width: 100%; border: none;
    padding: 8px 12px 8px 12px;
    resize: none; /* Kullanıcı tarafından yeniden boyutlandırılamaz */
    
    /* Dinamik yükseklik için başlangıç değerleri */
    min-height: 36px; /* Başlangıç yüksekliği (padding dahil) */
    max-height: 120px; /* Büyüme limiti, sonrası scroll olur */
    overflow-y: auto; /* max-height'e ulaşınca scrollbar görünsün */

    font-size: var(--font-size-input);
    line-height: 1.35;
    outline: none;
    background-color: var(--widget-input-bg);
    border-radius: 18px; /* Yuvarlak köşeler */
    color: var(--widget-text-color);
    box-sizing: border-box;
    font-family: var(--widget-font-family);
    transition: all 0.1s ease-in-out; /* Yumuşak geçiş */
}
.my-chat-widget-container .my-chat-input-area .send-button-icon {
    position: absolute;
    right: 12px;
    bottom: 15px;
    
    width: 33px;
    height: 33px;
    border-radius: 50%;
    background-color: var(--widget-primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;

    opacity: 1;
    transform: scale(1);
    pointer-events: all;
    
}

/* JS ile `.has-text` sınıfı eklendiğinde butonu göster */
.my-chat-widget-container .my-chat-input-area.has-text .send-button-icon {
    opacity: 1;
    transform: scale(1);
    pointer-events: all;
}
.my-chat-widget-container .my-chat-footer a {
    color: #8E8E93;
    text-decoration: none;
    font-weight: 500;
}
.my-chat-widget-container .my-chat-footer a:hover {
    text-decoration: underline;
}

/* Klavye çıktığında footer'ı gizle (mobil için) */
@media (max-height: 500px) and (max-width: 480px) {
    .my-chat-widget-container .my-chat-footer {
        display: none;
    }
}
.my-chat-widget-container .my-chat-avatar-scene-container {
    position: relative; /* Footer'ı konumlandırmak için */
}
.my-chat-widget-container .my-chat-avatar-scene-container .my-chat-footer {
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.2); /* Sahnede daha iyi görünmesi için */
    border-top: none;
    color: rgba(255,255,255,0.7);
    text-align: center; padding: 8px 0; font-size: 0.7em;
    border-top: 1px solid var(--widget-input-border-color); flex-shrink: 0;
}
.my-chat-widget-container .my-chat-avatar-scene-container .my-chat-footer a {
     color: rgba(255,255,255,0.9);
}
/* === 10. AVATAR EKRANI DÜZELTMELERİ === */
.my-chat-widget-container #my-chat-avatar-canvas {
    width: 100%;
    height: 100%;
    display: block;
    touch-action: none; /* KAYDIRMAYI ENGELLE */
}

/* Yeni "Aç/Kapat" (Toggle) Butonu */
.my-chat-toggle-button {
    width: 64px;
    height: 64px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    position: relative;
    pointer-events: all;
    transition: background-color 0.3s ease, transform 0.2s ease;
    
    /* Başlangıç durumu (Kayıt Dışı) */
    background-color: var(--widget-primary-color);
}
.my-chat-toggle-button:hover {
    transform: scale(1.05);
}

/* İkonların Yönetimi */
.my-chat-toggle-button svg {
    width: 28px;
    height: 28px;
    fill: white;
    position: absolute; /* İkonları üst üste bindir */
    transition: opacity 0.2s, transform 0.2s;
}

.my-chat-toggle-button .mic-icon { opacity: 1; transform: scale(1); }
.my-chat-toggle-button .stop-icon { opacity: 0; transform: scale(0.5); }


/* === KAYIT DURUMU (.is-recording) === */

.my-chat-toggle-button.is-recording {
    /* Kayıt sırasında kırmızı renk */
    background-color: #F44336; 
}

/* Kayıt sırasında ikonları değiştir */
.my-chat-toggle-button.is-recording .mic-icon { opacity: 0; transform: scale(0.5); }
.my-chat-toggle-button.is-recording .stop-icon { opacity: 1; transform: scale(1); }

/* Kayıt sırasında dalgalanma animasyonu */
.my-chat-toggle-button.is-recording::before {
    content: '';
    position: absolute;
    top: 0; left: 0;
    width: 100%; height: 100%;
    border-radius: 50%;
    background-color: #F44336;
    animation: ptt-ripple-effect 1.5s infinite ease-out;
    z-index: -1;
}

@keyframes ptt-ripple-effect {
    from { transform: scale(1); opacity: 0.6; }
    to { transform: scale(1.8); opacity: 0; }
}

.my-chat-widget-container .my-chat-ptt-wrapper {
    /* 1. Tüm grubun konumunu buradan kontrol edin */
    position: absolute;
    bottom: 50px; /* BUTON + YAZI grubunu alttan 80px yukarı taşı. Değeri buradan ayarlayın. */
    left: 50%;
    transform: translateX(-50%); /* Yatayda ortala */

    /* 2. İçerik düzenini flexbox ile ayarlayın */
    display: flex;
    flex-direction: column; /* Elemanları dikey olarak (alt alta) sırala */
    align-items: center;   /* Dikey sütunda yatay olarak ortala */
    
    /* 3. Buton ve yazı arasındaki boşluğu buradan kontrol edin */
    gap: 8px; /* Buton ve altındaki yazı arasına 8px boşluk bırak */
    
    pointer-events: none; /* Bu doğru, sadece içindeki buton tıklanabilir olmalı */
    width: 100%; /* Ortalama için genişliğin olması iyidir */
}

/* Durum Metni Stilleri */
.my-chat-ptt-status {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9em;
    font-weight: 500;
    text-shadow: 0 1px 3px rgba(0,0,0,0.5);
    pointer-events: none;
    transition: color 0.3s ease;
}

.is-recording + .my-chat-ptt-status {
    color: #F44336; /* Kayıt sırasında metin rengini de değiştir */
}

/* body'ye eklendiğinde tüm sayfa kaydırmasını engeller
   mikrofon butonuna basıldığında pencere kaymasını engellemek için*/
.no-scroll {
    overflow: hidden !important;
}

/* === 8. RESİM GALERİSİ VE LIGHTBOX === */

/* Genel Link Stilleri */
.my-chat-message a { color: var(--widget-primary-color); text-decoration: underline; }
.my-chat-message-user a { color: inherit; }

.my-chat-image-gallery { display: flex; flex-wrap: wrap; gap: 8px; margin-top: 10px; }
.my-chat-gallery-item { margin: 0; flex: 1 1 calc(50% - 4px); max-width: calc(50% - 4px); }
.my-chat-image-gallery:has(.my-chat-gallery-item:nth-child(1):nth-last-child(1)) .my-chat-gallery-item {
    flex-basis: 100%; max-width: 100%;
}
.my-chat-gallery-item img {
    width: 100%; height: auto; aspect-ratio: 1 / 1; object-fit: cover;
    border-radius: 12px; display: block; cursor: pointer;
}
.my-chat-gallery-item figcaption { font-size: 0.8em; color: #555; text-align: center; padding-top: 4px; }
/* === 11. LIGHTBOX DÜZELTMESİ === */
/* Bu CSS bloğunu kodunuzun en sonuna, diğerlerinden sonra ekleyin */
.my-chat-lightbox-overlay {
    position: fixed;
    top: 0; left: 0;
    width: 100%; height: 100%;
    background: rgba(0, 0, 0, 0.85);
    display: flex;
    justify-content: center;
    align-items: center;
    /* En üstte olmasını garantile */
    z-index: 2147483647; 
    opacity: 1;
    transition: opacity 0.3s ease;
}
.my-chat-lightbox-overlay.my-chat-hidden {
    opacity: 0;
    pointer-events: none;
}
.my-chat-lightbox-image {
    max-width: 90vw;
    max-height: 90vh;
}
.my-chat-lightbox-close {
    position: absolute;
    top: 15px; right: 15px;
    font-size: 32px;
    color: white;
}

/* === YENİ: SPLASH SCREEN STİLLERİ === */
.my-chat-splash-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100; /* Diğer her şeyin üzerinde olmalı */
    display: flex; /* Varsayılan olarak flex, JS ile kontrol edilecek */
    flex-direction: column;
    justify-content: center; /* Dikeyde ortala */
    align-items: center;     /* Yatayda ortala */
    background: #1c1c1e; /* Koyu, modern bir arka plan */
    color: #ffffff;
    padding: 20px;
    box-sizing: border-box;
    opacity: 1;
    transition: opacity 0.5s ease-in-out; /* Yumuşak kaybolma efekti için */
}

/* Geçiş efekti için class */
.my-chat-splash-screen.fade-out {
    opacity: 0;
    pointer-events: none; /* Kaybolurken tıklamaları engelle */
}

.splash-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex-grow: 1;
    justify-content: center;
    animation: fadeIn 0.8s ease-out forwards; /* İçerik de yumuşakça görünsün */
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.splash-logo {
    width: 70px;
    height: 70px;
    border-radius: 16px; /* Hafif yuvarlak köşe */
    margin-bottom: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    /* Sürekli atan kalp gibi bir animasyon */
    animation: pulse 2.5s infinite ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.splash-title {
    font-size: 22px;
    font-weight: 600;
    margin: 0;
}

.splash-subtitle {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.7);
    margin: 4px 0 30px 0;
    min-height: 20px; /* Otel adı gelene kadar boşluk bırakır */
}

.splash-status-text {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 15px; /* Noktalardan sonraki boşluk */
    letter-spacing: 0.5px;
}

/* "Bağlanıyor..." animasyonlu noktaları */
.splash-status {
    display: flex;
}
.splash-dot {
    width: 8px;
    height: 8px;
    margin: 0 4px;
    background-color: #fff;
    border-radius: 50%;
    animation: bounce 1.4s infinite ease-in-out both;
}
.splash-dot:nth-child(1) { animation-delay: -0.32s; }
.splash-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1.0); }
}

.splash-footer {
    width: 100%;
    text-align: center;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.4);
    padding: 10px 0;
}
.splash-footer a {
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    font-weight: 500;
}
.splash-footer a:hover {
    text-decoration: underline;
}