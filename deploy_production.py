#!/usr/bin/env python3
"""
Production Deployment Script
Stateless Architecture için production ortamı hazırlığı
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Logging setup
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProductionDeployer:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.logs_dir = self.project_root / "logs"
        self.required_dirs = ["logs", "static", "templates"]
        
    def check_system_requirements(self):
        """Sistem gereksinimlerini kontrol et"""
        logger.info("=== SYSTEM REQUIREMENTS CHECK ===")
        
        # Python version
        python_version = sys.version_info
        if python_version.major < 3 or python_version.minor < 8:
            logger.error(f"Python 3.8+ required, found {python_version.major}.{python_version.minor}")
            return False
        logger.info(f"✓ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # Required packages
        required_packages = [
            "gunicorn", "eventlet", "redis", "flask", "flask-socketio"
        ]
        
        for package in required_packages:
            try:
                __import__(package.replace("-", "_"))
                logger.info(f"✓ {package}")
            except ImportError:
                logger.error(f"✗ {package} not found")
                return False
        
        return True
    
    def setup_directories(self):
        """Gerekli dizinleri oluştur"""
        logger.info("=== DIRECTORY SETUP ===")
        
        for dir_name in self.required_dirs:
            dir_path = self.project_root / dir_name
            dir_path.mkdir(exist_ok=True)
            logger.info(f"✓ {dir_name}/ directory ready")
    
    def check_redis_connection(self):
        """Redis bağlantısını kontrol et"""
        logger.info("=== REDIS CONNECTION CHECK ===")
        
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
            r.ping()
            logger.info("✓ Redis connection successful")
            
            # Redis info
            info = r.info()
            logger.info(f"✓ Redis version: {info.get('redis_version')}")
            logger.info(f"✓ Redis memory: {info.get('used_memory_human')}")
            
            return True
        except Exception as e:
            logger.error(f"✗ Redis connection failed: {e}")
            logger.error("Please install and start Redis server:")
            logger.error("  Ubuntu/Debian: sudo apt install redis-server")
            logger.error("  CentOS/RHEL: sudo yum install redis")
            logger.error("  Windows: Download from https://redis.io/download")
            return False
    
    def check_database(self):
        """Database bağlantısını kontrol et"""
        logger.info("=== DATABASE CHECK ===")
        
        try:
            from app.database import get_db_connection
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Test query
            cursor.execute("SELECT COUNT(*) FROM reservations")
            count = cursor.fetchone()[0]
            logger.info(f"✓ Database connection successful")
            logger.info(f"✓ Reservations table: {count} records")
            
            conn.close()
            return True
        except Exception as e:
            logger.error(f"✗ Database check failed: {e}")
            return False
    
    def generate_systemd_service(self):
        """Systemd service dosyası oluştur"""
        logger.info("=== SYSTEMD SERVICE GENERATION ===")
        
        service_content = f"""[Unit]
Description=Hotel Reception AI System
After=network.target redis.service
Requires=redis.service

[Service]
Type=notify
User=www-data
Group=www-data
WorkingDirectory={self.project_root}
Environment=PATH={self.project_root}/venv/bin
ExecStart={self.project_root}/venv/bin/gunicorn --config gunicorn_config.py app:app
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=always
RestartSec=10

# Stateless Architecture Settings
# No shared memory between workers
# All state in Redis

[Install]
WantedBy=multi-user.target
"""
        
        service_file = self.project_root / "hotel-ai.service"
        with open(service_file, 'w') as f:
            f.write(service_content)
        
        logger.info(f"✓ Systemd service file created: {service_file}")
        logger.info("To install:")
        logger.info(f"  sudo cp {service_file} /etc/systemd/system/")
        logger.info("  sudo systemctl daemon-reload")
        logger.info("  sudo systemctl enable hotel-ai")
        logger.info("  sudo systemctl start hotel-ai")
    
    def generate_nginx_config(self):
        """Nginx konfigürasyonu oluştur"""
        logger.info("=== NGINX CONFIGURATION ===")
        
        nginx_content = """server {
    listen 80;
    server_name your-domain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL Configuration
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/key.pem;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # Gzip compression
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
    
    # Static files
    location /static/ {
        alias /path/to/hotel-ai/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Socket.IO
    location /socket.io/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Main application
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
"""
        
        nginx_file = self.project_root / "nginx-hotel-ai.conf"
        with open(nginx_file, 'w') as f:
            f.write(nginx_content)
        
        logger.info(f"✓ Nginx config created: {nginx_file}")
        logger.info("To install:")
        logger.info(f"  sudo cp {nginx_file} /etc/nginx/sites-available/hotel-ai")
        logger.info("  sudo ln -s /etc/nginx/sites-available/hotel-ai /etc/nginx/sites-enabled/")
        logger.info("  sudo nginx -t && sudo systemctl reload nginx")
    
    def run_production_check(self):
        """Production hazırlık kontrolü"""
        logger.info("=== PRODUCTION READINESS CHECK ===")
        
        checks = [
            ("System Requirements", self.check_system_requirements),
            ("Directory Setup", lambda: (self.setup_directories(), True)[1]),
            ("Redis Connection", self.check_redis_connection),
            ("Database Check", self.check_database),
        ]
        
        all_passed = True
        for check_name, check_func in checks:
            try:
                result = check_func()
                if result:
                    logger.info(f"✓ {check_name}: PASSED")
                else:
                    logger.error(f"✗ {check_name}: FAILED")
                    all_passed = False
            except Exception as e:
                logger.error(f"✗ {check_name}: ERROR - {e}")
                all_passed = False
        
        if all_passed:
            logger.info("=== ALL CHECKS PASSED ===")
            logger.info("System is ready for production deployment!")
            
            # Generate config files
            self.generate_systemd_service()
            self.generate_nginx_config()
            
            logger.info("=== DEPLOYMENT COMMANDS ===")
            logger.info("1. Start with Gunicorn:")
            logger.info("   gunicorn --config gunicorn_config.py app:app")
            logger.info("2. Or install as systemd service (see above)")
            
        else:
            logger.error("=== CHECKS FAILED ===")
            logger.error("Please fix the issues before production deployment")
            return False
        
        return True

if __name__ == "__main__":
    deployer = ProductionDeployer()
    success = deployer.run_production_check()
    sys.exit(0 if success else 1)
