// widget.js (Ana widget mantığı - Form ve Aksiyonlar Kaldırıldı)

import * as THREE from 'three';
// Yollardan 'loaders/' kısmını kaldırıyoruz.
import { GLTFLoader } from 'three/addons/GLTFLoader.js';
import { FBXLoader } from 'three/addons/FBXLoader.js';

class MyChatWidget {
      constructor(settings) {
        console.log("--- MyChatWidget: constructor BAŞLADI ---");
        const defaultSettings = {
            hotelIdentifier: null,
            backendUrl: 'http://127.0.0.1:5000',
            theme: {
                primaryColor: '#007AFF', headerTextColor: '#FFFFFF', widgetBackgroundColor: '#F7F7F7',
                textColor: '#1c1c1e', borderRadius: '18px',
                bubbleAiBgGradientStart: '#EFEFF4', bubbleAiBgGradientEnd: '#E9E9EB',
                bubbleAiText: '#1c1c1e', bubbleUserBg: '#007AFF', bubbleUserText: '#FFFFFF',
                inputBg: '#FFFFFF', inputBorderColor: '#E0E0E0',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Cantarell, "Helvetica Neue", sans-serif',
                avatarSize: '32px', headerHeight: '50px'
            },
            welcomeScreen: {
                enabled: true,
                logoUrl: 'https://resepsiyonapi.rehberim360.com/static/models/avatar-icon.jpg',
                title: 'Canlı Desteğe Hoş Geldiniz!',
                message: 'Size nasıl yardımcı olabiliriz? Lütfen bir devam etme şekli seçin.',
                videoButtonText: 'Görüntülü Konuşma (Avatar)',
                textButtonText: 'Yazışarak Devam Et'
            },
            headerText: 'ResepsiyonAI', headerLogoUrl: 'https://cdn-icons-png.flaticon.com/512/732/732085.png',
            aiAvatarUrl: 'https://resepsiyonapi.rehberim360.com/static/models/avatar-icon.jpg', userAvatarUrl: '',
            initialLauncherIcon: '💬',
            launcherOpenIcon: '💬',
            launcherCloseIcon: '✕',
            footerText: 'Powered by LiveChatAI', showTimestamps: true,
            micButtonEnabled: true, attachButtonEnabled: true,
            aiAgentName: "AI Agent",
            speechLang: 'tr-TR'
        };

        // Ayarları birleştir
        this.settings = this._mergeDeep(defaultSettings, settings || {})

        // YENİ: Ses efekti için Audio nesnesi (Dinamik URL ile)
        const notificationSoundUrl = `${this.settings.backendUrl}/static/sounds/blob.mp3`;
        this.audio = {
            notification: new Audio(notificationSoundUrl)
        };
        console.log(`INFO: Bildirim sesi şu adresten yüklendi: ${notificationSoundUrl}`);


        // Sınıf değişkenleri
        this.socket = null;
        this.sessionId = null;
        this.elements = {};
        this.isWindowOpen = false;
        this.aiTyping = false;
        this.originalTitle = document.title;
        this.notificationInterval = null;
        this.speechRecognition = null;
        this.isRecognizingSpeech = false;
        this.isAudioEnabled = true; // Ses varsayılan olarak açık
        this.isMicListeningMode = false; // Mikrofon dinleme modu kapalı
        this.recognitionActive = false; // SpeechRecognition API'sinin o an aktif olup olmadığını tutar
        this.isRecording = false; // YENİ: Bas-Konuş butonunun durumunu tutar
        this.mediaRecorder = null; // YENİ: Ses kaydı için
        this.audioChunks = [];     // YENİ: Kaydedilen ses parçaları
        // Sayfa yüklendiğinde localStorage'dan session ID'yi oku.
        this.sessionId = localStorage.getItem('myChatWidgetSessionId'); 

        // Three.js değişkenleri
        this.three = {
            scene: null, camera: null, renderer: null, avatar: null,
            mixer: null, clock: new THREE.Clock(),
            morphTargetDictionary: null, morphTargetInfluences: null,
            audioContext: null, audioSource: null, isPlaying: false,
            visemeQueue: [], audioStartTime: 0, currentViseme: 'viseme_sil',
            lastBlinkTime: 0,
            audioQueue: [], // Gelen ses paketleri için yeni kuyruk
            isAudioQueuePlaying: false, // Kuyruğun oynatılıp oynatılmadığını belirten bayrak
            analyser: null,          // AI'ın ses çıkışını analiz etmek için
            micStream: null,         // Kullanıcının mikrofon akışı
            micAnalyser: null,       // Mikrofonu analiz etmek için
            micGainNode: null,       // Mikrofon sesini yönetmek için
            isBargeInDetected: false, // Söze girme algılandı mı?
            animationActions: {}, // Yüklenen tüm animasyon "eylemlerini" burada saklayacağız
            activeAction: null,   // O an aktif olan animasyon eylemi
            lastAction: null,      // Bir önceki animasyon eylemi (yumuşak geçiş için)
            activeExpressions: {}, // O an aktif olan ifade animasyonları
            expressionClock: new THREE.Clock(), // İfadelerin zamanlaması için AYRI BİR SAAT
            headBone: null, // Kafa kemiğinin referansı
            neckBone: null, // Boyun kemiğinin referansı
            spineBone: null, // Omurga kemiğinin referansı
            isAvatarModeActive: false, // Başlangıçta kapalı
           
    _lookAtTarget: new THREE.Vector3(),
    _avatarPosition: new THREE.Vector3(),
    _bodyTargetQuaternion: new THREE.Quaternion(),
    _tempMatrix: new THREE.Matrix4(),
    _headTargetQuaternion: new THREE.Quaternion(),
    _headMatrix: new THREE.Matrix4()
        };

        

        // === 1. GELİŞMİŞ ANİMASYON ve VURGU AYARLARI ===
        
        this.animationParams = {
            // Animasyonun genel yumuşaklığı. Düşük değer = daha yumuşak, yüksek değer = daha keskin.
            lerpFactor: 0.13,
            
            // Backend'den gelen sesin ağ gecikmesini telafi etmek için.
            latencyOffset: 0.05,
            
            // Fonemlerin temel süreleri (saniye).
            vowelDuration: 0.08,      // Sesli harfleri biraz daha uzun tutalım.
            consonantDuration: 0.09,  // Sessiz harfler daha kısa ve keskin.
            lipCloseDuration: 0.07    // p, b, m için dudakların kapalı kalma süresi.
        };

          // === 2. YENİ: VISEME VURGU (AĞIRLIK) HARİTASI ===
        // Her bir viseme'in ne kadar "güçlü" olacağını belirler (0.0 - 1.0 arası).
        // 1.0'dan büyük değerler abartılı hareketler için kullanılabilir.
        this.visemeWeights = {
            'viseme_sil': 0.0, // Sessizlik
            'viseme_PP': 3.0,  // m, b, p - Dudaklar tam kapansın
            'viseme_FF': 2.5,  // f, v - Dudaklar hafifçe kapanır
            'viseme_DD': 0.6,  // t, d - Dil hareketi, dudak az etkilenir
            'viseme_kk': 0.5,  // k, g - Gırtlak hareketi, dudak az etkilenir
            'viseme_CH': 0.7,  // ç, ş - Dudaklar biraz büzülür
            'viseme_SS': 0.6,  // s, z - Dişler birleşir, dudaklar az etkilenir
            'viseme_nn': 0.5,  // n - Dil hareketi
            'viseme_RR': 0.8,  // r, l - Dil yuvarlanır, dudaklar etkilenir
            
            // VURGUYU ARTIRACAĞIMIZ SESLİLER
            'viseme_I': 1.5,   // ı, i - Dudaklar yana çekilir
            'viseme_aa': 1.0,  // a, e - Ağız en açık pozisyonda
            'viseme_O': 1.5,   // o, u - VURGULU! Ağız yuvarlak ve açık (1.0'dan büyük)
            'viseme_U': 2.5    // ö, ü - VURGULU! Ağız yuvarlak ve büzülmüş (1.0'dan büyük)
        };

        // === 2. TEK ve DOĞRU VISEME HARİTASI ===
      this.phonemeToVisemeMap = {
            // Sessizler (Dudak Kapanması Gerekenler)
            'p': 'viseme_PP', 'b': 'viseme_PP', 'm': 'viseme_PP',
            
            // Sessizler (Dişler ve Dudaklar)
            'f': 'viseme_FF', 'v': 'viseme_FF',
            
            // Sessizler (Dilin Üst Dişlere Değdiği)
            't': 'viseme_DD', 'd': 'viseme_DD',
            
            // Sessizler (Dilin Damağa Değdiği)
            'k': 'viseme_kk', 'g': 'viseme_kk',
            
            // Sessizler (Dilin Arkası)
            'ç': 'viseme_CH', 'c': 'viseme_CH', 'ş': 'viseme_CH', 'j': 'viseme_CH',
            's': 'viseme_SS', 'z': 'viseme_SS',
            
            // Akıcılar
            'n': 'viseme_nn',
            'r': 'viseme_RR',
            'l': 'viseme_RR', // 'l' sesi de 'r' gibi dil hareketi içerir
            'y': 'viseme_I',  // 'y' sesi 'i' sesine benzer bir ağız şekliyle başlar
            
            // Sesliler (Vokaller)
            'a': 'viseme_aa', 'e': 'viseme_aa', // 'a' ve 'e' en açık sesler
            'ı': 'viseme_I', 'i': 'viseme_I',   // Dar ve düz sesliler
            'o': 'viseme_O', 'u': 'viseme_O',   // Yuvarlak sesliler (geniş)
            'ö': 'viseme_U', 'ü': 'viseme_U'    // Yuvarlak sesliler (dar)
        };

        // --- YENİ: YÜZ İFADESİ TARİFLERİ ---
        // Hangi ifadenin, hangi blend shape'i ne kadar etkileyeceğini burada tanımlıyoruz.
        // ÖNEMLİ: 'mouthSmile', 'browsUp' gibi anahtarlar, sizin GLB modelinizdeki
        // morph target isimleriyle EŞLEŞMELİDİR.
        this.expressionRecipes = {
            // Samimi bir gülümseme
            'smile': [
                // Simetrik gülümseme için hem sol hem de sağ blend shape'i kullanıyoruz.
                { name: 'mouthSmileLeft',  start: 0.1, peak: 0.4, end: 2.5, targetValue: 0.8 },
                { name: 'mouthSmileRight', start: 0.1, peak: 0.4, end: 2.5, targetValue: 0.8 },
                // Gülümserken gözler hafifçe kısılır, bu daha doğal bir görünüm verir.
                { name: 'eyeSquintLeft',   start: 0.2, peak: 0.6, end: 2.5, targetValue: 0.5 },
                { name: 'eyeSquintRight',  start: 0.2, peak: 0.6, end: 2.5, targetValue: 0.5 }
            ],
            // Hafif bir üzüntü veya olumsuz yanıt ifadesi
            'sad': [
                // Ağız kenarlarını aşağı indir
                { name: 'mouthFrownLeft',  start: 0.2, peak: 0.5, end: 3.0, targetValue: 0.6 },
                { name: 'mouthFrownRight', start: 0.2, peak: 0.5, end: 3.0, targetValue: 0.6 },
                // Kaşların iç kısımlarını yukarı kaldır (endişeli/üzgün ifade)
                { name: 'browInnerUp',     start: 0.1, peak: 0.4, end: 3.0, targetValue: 0.9 }
            ],
            // Meraklı veya soru soran bir ifade
            'confused': [
                // Tek kaşı kaldırma efekti için birini daha fazla kaldırıyoruz.
                { name: 'browInnerUp',     start: 0.1, peak: 0.4, end: 2.5, targetValue: 0.5 },
                { name: 'browDownLeft',    start: 0.15, peak: 0.5, end: 2.5, targetValue: 0.4 } // Bir kaşı hafifçe indir
            ],
            // Şaşırma ifadesi
            'surprised': [
                // Gözleri aniden aç
                { name: 'eyeWideLeft',     start: 0.0, peak: 0.2, end: 2.0, targetValue: 0.7 },
                { name: 'eyeWideRight',    start: 0.0, peak: 0.2, end: 2.0, targetValue: 0.7 },
                // Kaşları yukarı kaldır
                { name: 'browInnerUp',     start: 0.0, peak: 0.2, end: 2.0, targetValue: 1.0 },
                // Ağzı hafifçe arala
                { name: 'mouthOpen',       start: 0.05, peak: 0.25, end: 2.0, targetValue: 0.3 }
            ],
            // "Merhaba!" gibi pozitif bir karşılama
            'greeting': [
                // Kısa ve hafif bir gülümseme
                { name: 'mouthSmile',      start: 0.1, peak: 0.4, end: 4.0, targetValue: 0.6 },
                // Kaşları kaldırarak pozitif bir ifade ver
                { name: 'browInnerUp',     start: 0.0, peak: 0.3, end: 1.5, targetValue: 0.5 }
            ]
        };
        
    
        this._createDOM();
        this._addEventListeners();
        console.log("--- MyChatWidget: constructor BİTTİ ---");
    }

    _mergeDeep(target, source) {
        const output = { ...target };
        if (this._isObject(target) && this._isObject(source)) {
            Object.keys(source).forEach(key => {
                if (this._isObject(source[key])) {
                    if (!(key in target)) Object.assign(output, { [key]: source[key] });
                    else output[key] = this._mergeDeep(target[key], source[key]);
                } else {
                    Object.assign(output, { [key]: source[key] });
                }
            });
        }
        return output;
    }
    _isObject(item) {
        return (item && typeof item === 'object' && !Array.isArray(item));
    }


    _applyTheme() {
        const root = document.documentElement; const theme = this.settings.theme;
        Object.keys(theme).forEach(key => {
            const cssVarName = `--widget-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
            root.style.setProperty(cssVarName, theme[key]);
        });
    }

   _createDOM() {
    console.log("--- MyChatWidget: _createDOM BAŞLADI ---");
    this.elements.widgetContainer = document.createElement('div');
    this.elements.widgetContainer.className = 'my-chat-widget-container';

    // --- LAUNCHER GÜNCELLEMESİ BAŞLANGICI ---
    this.elements.launcher = document.createElement('button');
    this.elements.launcher.className = 'my-chat-launcher';
    // Geçici bir avatar URL'i, bunu ayarlardan alacak şekilde güncelleyebilirsiniz.
    const launcherAvatarUrl = 'https://resepsiyonapi.rehberim360.com/static/models/avatar-icon.jpg'; 
    this.elements.launcher.innerHTML = `
        <div class="launcher-avatar">
            <img src="${launcherAvatarUrl}" alt="Avatar">
        </div>
        <span class="launcher-text">Yapay Zeka Resepsiyon</span>
    `;
    // --- LAUNCHER GÜNCELLEMESİ SONU ---

    this.elements.widgetContainer.appendChild(this.elements.launcher);
    this.elements.chatWindow = document.createElement('div');
    this.elements.chatWindow.className = 'my-chat-window my-chat-hidden';
    
    // ... fonksiyonun geri kalanı aynı ...
    this._createWelcomeScreen();
    this._createMainChatContainer();
    this._createAvatarSceneContainer();
    this._createLightboxDOM();

    this.elements.widgetContainer.appendChild(this.elements.chatWindow);
    document.body.appendChild(this.elements.widgetContainer);

    this.elements.mainChatContainer.style.display = 'none';
    this.elements.avatarSceneContainer.style.display = 'none';

    if (this.settings.welcomeScreen.enabled) {
        this._switchView('welcome');
    } else {
        this._startTextChat();
    }
    }

    _createWelcomeScreen() {
        this.elements.welcomeScreen = document.createElement('div');
        this.elements.welcomeScreen.className = 'my-chat-welcome-screen';

        const { logoUrl, title, message, videoButtonText, textButtonText } = this.settings.welcomeScreen;

        this.elements.welcomeScreen.innerHTML = `
            <img src="${logoUrl}" alt="Welcome Logo" class="my-chat-welcome-logo">
            <h2 class="my-chat-welcome-title">${title}</h2>
            <p class="my-chat-welcome-message">${message}</p>
            <div class="my-chat-welcome-options">
                <button class="my-chat-welcome-button primary" id="my-chat-start-video-btn">${videoButtonText}</button>
                <button class="my-chat-welcome-button secondary" id="my-chat-start-text-btn">${textButtonText}</button>
            </div>
        `;

        this.elements.chatWindow.appendChild(this.elements.welcomeScreen);
    }

    /**
 * Standart yazışma ekranının tüm görsel bileşenlerini oluşturur ve birleştirir.
 */
_createMainChatContainer() {
    this.elements.mainChatContainer = document.createElement('div');
    this.elements.mainChatContainer.className = 'my-chat-main-container';

    this._createHeader();
    this._createMessageArea();
    this._createInputArea();
    const footerElement = this._createFooter(); // Footer'ı oluştur

    this.elements.mainChatContainer.appendChild(this.elements.header);
    this.elements.mainChatContainer.appendChild(this.elements.messageArea);
    this.elements.mainChatContainer.appendChild(this.elements.inputArea);
    
    if (footerElement) { // Eğer footer varsa ekle
        this.elements.mainChatContainer.appendChild(footerElement);
    }
    
    this.elements.chatWindow.appendChild(this.elements.mainChatContainer);
}


// =============================================================
// Bu versiyon, avatarın canvas'ını ve PTT butonunu doğru şekilde oluşturur.
_createAvatarSceneContainer() {
    this.elements.avatarSceneContainer = document.createElement('div');
    this.elements.avatarSceneContainer.className = 'my-chat-avatar-scene-container';
    
    // Header'ı klonla (bu hala gerekli)
    const headerClone = this.elements.header.cloneNode(true);
    this.elements.cloneHeader = headerClone;

    // Canvas için sarmalayıcı, canvas VE yükleme animasyonu
    const canvasWrapper = document.createElement('div');
    canvasWrapper.className = 'my-chat-avatar-canvas-wrapper';
    canvasWrapper.innerHTML = `<canvas id="my-chat-avatar-canvas"></canvas>`; // Canvas'ı ekle

    const loaderOverlay = document.createElement('div');
    loaderOverlay.className = 'my-chat-loader-overlay';
    loaderOverlay.innerHTML = '<div class="my-chat-spinner"></div>';
    this.elements.avatarLoader = loaderOverlay; // Referansı sakla
    canvasWrapper.appendChild(loaderOverlay); // Yükleyiciyi canvas sarmalayıcısına ekle

    // "Aç/Kapat" Butonu ve Durum Metni
    const pttWrapper = document.createElement('div');
    pttWrapper.className = 'my-chat-ptt-wrapper';

    this.elements.pushToTalkButton = document.createElement('button');
    this.elements.pushToTalkButton.className = 'my-chat-toggle-button';
    this.elements.pushToTalkButton.innerHTML = `
        <svg class="mic-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">
            <path d="M192 0C139 0 96 43 96 96V256c0 53 43 96 96 96s96-43 96-96V96c0-53-43-96-96-96zM64 216c0-13.3-10.7-24-24-24s-24 10.7-24 24v40c0 89.1 66.2 162.7 152 174.4V464H120c-13.3 0-24 10.7-24 24s10.7 24 24 24h144c13.3 0 24-10.7 24-24s-10.7-24-24-24H192V430.4c85.8-11.7 152-85.3 152-174.4V216c0-13.3-10.7-24-24-24s-24 10.7-24 24v40c0 70.7-57.3 128-128 128s-128-57.3-128-128V216z"/>
        </svg>
        <svg class="stop-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">
            <path d="M0 128C0 92.7 28.7 64 64 64H320c35.3 0 64 28.7 64 64V384c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V128z"/>
        </svg>
    `;
    pttWrapper.appendChild(this.elements.pushToTalkButton);

    this.elements.pttStatusText = document.createElement('span');
    this.elements.pttStatusText.className = 'my-chat-ptt-status';
    this.elements.pttStatusText.textContent = 'Konuşmak için dokunun';
    pttWrapper.appendChild(this.elements.pttStatusText);

    // --- YENİ: Footer'ı Avatar Ekranına da Ekle ---
    const footerElementClone = this._createFooter();

    // --- Elementleri Doğru Sırada Ekle ---
    this.elements.avatarSceneContainer.appendChild(headerClone);
    this.elements.avatarSceneContainer.appendChild(canvasWrapper); // Önce canvas
    this.elements.avatarSceneContainer.appendChild(pttWrapper);    // Sonra PTT butonu
    if(footerElementClone) { // Footer varsa klonunu ekle
        this.elements.avatarSceneContainer.appendChild(footerElementClone);
    }
    
    this.elements.chatWindow.appendChild(this.elements.avatarSceneContainer);

    // --- Olay Dinleyicileri ---
    const clonedHeaderButtons = headerClone.querySelectorAll('.my-chat-header-button');
    clonedHeaderButtons.forEach(button => {
        const label = button.getAttribute('aria-label');
        if (label === 'Sohbeti Kapat') button.addEventListener('click', () => this.toggleWindow(false));
        else if (label === 'Geri') button.addEventListener('click', () => this._showWelcomeScreen());
        else if (label === 'Sesi Aç/Kapat') button.addEventListener('click', () => this._toggleAudio());
    });

    if (this.elements.pushToTalkButton) {
        this.elements.pushToTalkButton.addEventListener('click', () => this._handleToggleButtonClick());
    }
}
// ================================================================

// Bu fonksiyon, butona her tıklandığında çalışır.
_handleToggleButtonClick() {
    if (this.isRecognizingSpeech) {
        if (this.speechRecognition) {
            this.speechRecognition.stop();
        }
    } else {
        this._startToggleRecording();
    }
}

// Bu fonksiyon, kaydı başlatır ve arayüzü günceller.
_startToggleRecording() {
    if (this.three.audioContext && this.three.audioContext.state === 'suspended') {
        this.three.audioContext.resume();
    }
    
    const SpeechRecognitionAPI = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognitionAPI) {
        this._addMessageToUI("Tarayıcınız ses tanımayı desteklemiyor.", "system");
        return;
    }

    try {
        this.speechRecognition = new SpeechRecognitionAPI();
        this.speechRecognition.lang = this.settings.speechLang || 'tr-TR';
        this.speechRecognition.continuous = false;
        this.speechRecognition.interimResults = false;
        
        this.isRecognizingSpeech = true;
        this.elements.pushToTalkButton.classList.add('is-recording');
        this.elements.pttStatusText.textContent = 'Kaydediliyor... Durdurmak için dokunun';
        this._stopAllAudio();

        this.speechRecognition.onresult = (e) => {
            const transcript = e.results[e.results.length - 1][0].transcript.trim();
            if (transcript) {
                 // Konuşulan metni, kullanıcı mesajı olarak sohbet arayüzüne ekle.
                 this._addMessageToUI(transcript, 'user', new Date().toISOString());
                // Konuşmayı sunucuya gönder
                this.socket.emit('user_message_to_server', {
                    session_id: this.sessionId,
                    message: transcript,
                    mode: 'avatar'
                });
            }
        };
        
        this.speechRecognition.onerror = (event) => {
            console.error(`[ERROR] SpeechRecognition Hatası: ${event.error}`);
            this._resetToggleUI();
        };

        this.speechRecognition.onend = () => {
            console.log("[INFO] SpeechRecognition.onend çağrıldı.");
            this._resetToggleUI();
        };
        
        this.speechRecognition.start();

    } catch (e) {
        console.error("[FATAL] SpeechRecognition başlatılamadı:", e);
        this._resetToggleUI();
    }
}

// Bu fonksiyon, kayıt bittiğinde veya hata olduğunda arayüzü sıfırlar.
_resetToggleUI() {
    console.log("[CLEANUP] Toggle arayüzü temizleniyor...");
    
    this.isRecognizingSpeech = false;
    if (this.elements.pushToTalkButton) {
        this.elements.pushToTalkButton.classList.remove('is-recording');
    }
    if (this.elements.pttStatusText) {
        this.elements.pttStatusText.textContent = 'Kaydı başlatmak için dokunun';
    }
}

     /**
     * Klonlanmış input alanından metin mesajı gönderir.
     */
    _sendMessageFromClone() {
        const messageText = this.elements.cloneMessageInput.value.trim();
        if (messageText === '') return;
        
        this._addMessageToUI(messageText, 'user', new Date().toISOString());

        this.socket.emit('user_message_to_server', { 
            session_id: this.sessionId, 
            message: messageText,
            mode: 'avatar' // Modu 'avatar' olarak gönder
        });
        
        this.elements.cloneMessageInput.value = ''; 
        this.elements.cloneMessageInput.style.height = 'auto';
        this.elements.cloneInputArea.classList.remove('has-text');
    }



      _createHeader() {
        this.elements.header = document.createElement('div');
        this.elements.header.className = 'my-chat-header';

        const headerLeftContent = document.createElement('div');
        headerLeftContent.className = 'my-chat-header-left-content';

        if (this.settings.headerLogoUrl) {
            this.elements.headerLogo = document.createElement('img');
            this.elements.headerLogo.className = 'my-chat-header-logo';
            this.elements.headerLogo.src = this.settings.headerLogoUrl;
            this.elements.headerLogo.alt = "Logo";
            headerLeftContent.appendChild(this.elements.headerLogo);
        }

        this.elements.headerTitleText = document.createElement('span');
        this.elements.headerTitleText.className = 'my-chat-header-title-text';
        this.elements.headerTitleText.textContent = this.settings.headerText;
        headerLeftContent.appendChild(this.elements.headerTitleText);

        const headerRight = document.createElement('div');
        headerRight.className = 'my-chat-header-right';

        this.elements.speakerButton = this._createHeaderButton('🔊', 'Sesi Aç/Kapat');
        this.elements.backButton = this._createHeaderButton('‹', 'Geri');
        this.elements.closeButton = this._createHeaderButton('✕', 'Sohbeti Kapat');

        headerRight.appendChild(this.elements.speakerButton);
        headerRight.appendChild(this.elements.backButton);
        headerRight.appendChild(this.elements.closeButton);

        this.elements.header.appendChild(headerLeftContent);
        this.elements.header.appendChild(headerRight);
    }

   /**
     * GÜNCELLENMİŞ Hoparlör Butonu Fonksiyonu
     * Artık sadece sesi açıp kapatır ve bunu anında uygular.
     */
    _toggleAudio() {
        this.isAudioEnabled = !this.isAudioEnabled;
        const icon = this.isAudioEnabled ? '🔊' : '🔇';
        
        // Tüm hoparlör butonlarının ikonunu güncelle.
        const speakerButtons = this.elements.widgetContainer.querySelectorAll('.my-chat-header-button[aria-label="Sesi Aç/Kapat"]');
        speakerButtons.forEach(button => button.innerHTML = icon);

        console.log(`Ses modu: ${this.isAudioEnabled ? 'AÇIK' : 'KAPALI'}`);

        // Eğer o an bir ses çalıyorsa ve ses kapatıldıysa, sesi anında kes.
        if (this.three.audioSource && this.three.audioSource.gain) {
            this.three.audioSource.gain.gain.value = this.isAudioEnabled ? 1 : 0;
        }
    }

 /**
     * YENİ: Tüm ses ve animasyonları anında durduran merkezi fonksiyon
     */
    _stopAllAudio() {
        if (this.three.audioSource) {
            this.three.audioSource.onended = null; // onended olayının tekrar tetiklenmesini engelle
            this.three.audioSource.stop(0);
        }
        this.three.audioSource = null;
        this.three.isPlaying = false;
        this.three.isAudioQueuePlaying = false;
        this.three.audioQueue = []; // Kuyruğu temizle
        this.three.visemeQueue = []; // Animasyon kuyruğunu temizle
        this.three.currentViseme = 'viseme_sil'; // Hedefi sıfırla
    }

    _showWelcomeScreen() {
     //this._switchView('welcome');
     this.elements.mainChatContainer.style.display = 'none';
    this.elements.avatarSceneContainer.style.display = 'none';
    this.elements.welcomeScreen.style.display = 'flex';

    // Karşılama ekranında sadece kapat butonu görünür
    this.elements.backButton.style.display = 'none';
    this.elements.speakerButton.style.display = 'none';
    this.elements.closeButton.style.display = 'flex';

    // --- ÖNERİ: Eğer kayıt yapılıyorsa onu da durdur ---
    if (this.isRecognizingSpeech && this.speechRecognition) {
        console.log("Geri gidildiği için ses kaydı durduruluyor.");
        this.speechRecognition.stop(); // Bu, onend olayını tetikleyip UI'ı temizleyecektir.
    }

     this._stopAllAudio();
    // Bayrağı KAPAT
    this.three.isAvatarModeActive = false;
}

   _createHeaderButton(innerHtml, ariaLabel) {
    const button = document.createElement('button');
    button.className = 'my-chat-header-button';
    button.setAttribute('aria-label', ariaLabel);

    // Modern SVG ikonları
    const icons = {
        '‹': '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><polyline points="15 18 9 12 15 6"></polyline></svg>',
        '✕': '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>',
        '🔊': '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path></svg>',
        '🔇': '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><line x1="23" y1="9" x2="17" y2="15"></line><line x1="17" y1="9" x2="23" y2="15"></line></svg>'
    };

    button.innerHTML = icons[innerHtml] || innerHtml;
    return button;
}


    _createMessageArea() {
        this.elements.messageArea = document.createElement('div');
        this.elements.messageArea.className = 'my-chat-message-area';
        this.elements.typingIndicator = document.createElement('div');
        this.elements.typingIndicator.className = 'my-chat-typing-indicator my-chat-hidden';
         // "Yazıyor..." göstergesini yeni animasyonlu yapıyla oluştur
        this.elements.typingIndicator = document.createElement('div');
        this.elements.typingIndicator.className = 'my-chat-typing-indicator my-chat-hidden';
        this.elements.typingIndicator.innerHTML = `
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
        `;
        
        this.elements.messageArea.appendChild(this.elements.typingIndicator);
      
    }

   _createInputArea() {
    // 1. Ana sarmalayıcı. Bu, konumlandırma için referans noktamız (anchor) olacak.
    this.elements.inputArea = document.createElement('div');
    this.elements.inputArea.className = 'my-chat-input-area';

    // 2. Textarea'yı DOĞRUDAN ana sarmalayıcıya ekle.
    this.elements.messageInput = document.createElement('textarea');
    this.elements.messageInput.placeholder = 'Mesajınızı yazın...';
    this.elements.messageInput.setAttribute('aria-label', 'Mesajınızı yazın');
    this.elements.messageInput.rows = 1;
    this.elements.inputArea.appendChild(this.elements.messageInput);

    // 3. Gönder butonunu da DOĞRUDAN ana sarmalayıcıya ekle.
    this.elements.sendButton = this._createInputIconButton('➤', 'Mesajı gönder', true);
    this.elements.inputArea.appendChild(this.elements.sendButton);
}


    _createInputIconButton(innerHtml, ariaLabel, isSendButton = false) {
        const button = document.createElement('button');
        button.className = `input-icon`;
        button.setAttribute('aria-label', ariaLabel);
        

        if (isSendButton) {
        button.classList.add('send-button-icon');
        // YENİ SVG İKONU
        button.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
                <path d="M3.478 2.405a.75.75 0 00-.926.94l2.432 7.905H13.5a.75.75 0 010 1.5H4.984l-2.432 7.905a.75.75 0 00.926.94 60.519 60.519 0 0018.445-8.986.75.75 0 000-1.218A60.517 60.517 0 003.478 2.405z" />
            </svg>
        `;
    } else {
        // Diğer ikonlar (mikrofon vb.)
        if (innerHtml === '🎤') button.classList.add('mic-button');
        button.innerHTML = innerHtml;
    }

    return button;
    }

    _createFooter() {
    if (this.settings.footerText) { // footerText ayarı hala bir kontrol olarak kalabilir
        this.elements.footer = document.createElement('div');
        this.elements.footer.className = 'my-chat-footer';
        this.elements.footer.innerHTML = `
            Powered by <a href="https://www.rehberim360.com" target="_blank" rel="noopener noreferrer">Rehberim360</a>
        `;
        // Bu fonksiyon artık sadece elementi oluşturup döndürecek
        // Nereye ekleneceğine çağıran fonksiyon karar verecek
        return this.elements.footer;
    }
    return null;
}

    _createLightboxDOM() {
        this.elements.lightboxOverlay = document.createElement('div');
        this.elements.lightboxOverlay.className = 'my-chat-lightbox-overlay my-chat-hidden';
        this.elements.lightboxImage = document.createElement('img');
        this.elements.lightboxImage.className = 'my-chat-lightbox-image';
        this.elements.lightboxClose = document.createElement('button');
        this.elements.lightboxClose.className = 'my-chat-lightbox-close';
        this.elements.lightboxClose.innerHTML = '✕';
        this.elements.lightboxOverlay.appendChild(this.elements.lightboxImage);
        this.elements.lightboxOverlay.appendChild(this.elements.lightboxClose);
        document.body.appendChild(this.elements.lightboxOverlay);
    }

    _initSocketConnection() {
        // Eğer zaten bağlıysak, tekrar deneme.
        if (this.socket && this.socket.connected) return;
        
        if (typeof io === 'undefined') {
            console.error("Socket.IO client library not found.");
            this._addMessageToUI('Sistem Hatası: Gerekli kütüphane yüklenemedi.', 'system');
            return;
        }
        if (!this.settings.hotelIdentifier) {
            console.error("hotelIdentifier ayarı eksik.");
            this._addMessageToUI('Yapılandırma hatası: Otel kimliği belirtilmemiş.', 'system');
            return;
        }

        this.socket = io(this.settings.backendUrl, {
            transports: ['websocket'],
            path: '/socket.io/',
            pingInterval: 10000,
            pingTimeout: 5000,
            reconnectionAttempts: 5
        });


        // --- TEMEL BAĞLANTI OLAYLARI ---
         this.socket.on('connect', () => {
        console.log('✅ WebSocket sunucusuna bağlandı. SID:', this.socket.id);
        
        const storedSessionId = localStorage.getItem('myChatWidgetSessionId');
        
        if (storedSessionId) {
            console.log(`Hafızada session ID bulundu: ${storedSessionId.substring(0,8)}... Devam ettirme deneniyor.`);
            // DOĞRUDAN this.sessionId'yi burada ayarlama. Sunucudan onayı bekle.
            this.socket.emit('resume_chat_from_widget', { session_id: storedSessionId });
        } else {
            console.log('Hafızada session ID yok. Yeni bir sohbet başlatılıyor...');
            this.socket.emit('start_chat_from_widget', { hotel_identifier: this.settings.hotelIdentifier });
        }
        });

        this.socket.on('disconnect', (reason) => {
            console.log('WebSocket bağlantısı kesildi. Neden:', reason);
            this._addMessageToUI(reason === 'io server disconnect' ? 'Sohbet sunucu tarafından sonlandırıldı.' : 'Bağlantı sorunu yaşandı.', 'system');
        });

        this.socket.on('error_message', (data) => {
            console.error('Sunucudan hata mesajı alındı:', data.message);
            this._addMessageToUI('Hata: ' + data.message, 'system');
        });

        // --- SOHBET AKIŞI OLAYLARI ---
        this.socket.on('initial_chat_data', (data) => {
        console.log('Yeni sohbet verileri alındı:', data);
        if (data.error) {
            this._addMessageToUI(data.error, 'system');
            return;
        }
        // Artık this.sessionId'yi güvenle ayarlayabiliriz.
        this.sessionId = data.session_id;
        localStorage.setItem('myChatWidgetSessionId', this.sessionId);

        this.elements.headerTitleText.textContent = data.hotel_name || this.settings.headerText;
        this._clearMessages();
        if (data.initial_message) {
            this._addMessageToUI(data.initial_message, 'ai', data.timestamp);
        }
        });


    this.socket.on('chat_resumed_successfully', (data) => {
        console.log("✅ Sohbet başarıyla devam ettirildi. Geçmiş yükleniyor...", data);
        
        // Sunucu devam etmeyi onayladı, artık bu session ID'yi resmi olarak kullanabiliriz.
        this.sessionId = data.session_id;
        localStorage.setItem('myChatWidgetSessionId', this.sessionId);

        this.elements.headerTitleText.textContent = data.hotel_name || this.settings.headerText;
        this._clearMessages();

        if (data.chat_history && Array.isArray(data.chat_history)) {
            data.chat_history.forEach(msg => {
                this._addMessageToUI(msg.message, msg.sender.toLowerCase(), msg.timestamp);
            });
        }
        });


     // Sunucu, hafızadaki session ID'nin geçersiz olduğunu söylediğinde
     this.socket.on('start_new_chat_required', (data) => {
         console.error(`KRİTİK HATA: Oturum beklenmedik bir anda geçersiz oldu. Neden: ${data.reason}`);
         // Bu durumda da temiz bir başlangıç yapmak en iyisidir.
         localStorage.removeItem('myChatWidgetSessionId');
         this.sessionId = null;
         this._clearMessages();
         this.socket.emit('start_chat_from_widget', { hotel_identifier: this.settings.hotelIdentifier });
        });
        
        this.socket.on('ai_typing_started', (data) => {
            if (data.session_id === this.sessionId) {
                this._showTypingIndicator(true);
            }
        });

    this.socket.on('resume_failed_start_new', (data) => {
        console.warn(`Devam etme başarısız oldu, sunucu yeni sohbet istedi. Neden: ${data.reason}`);
        
        // Geçersiz session ID'yi hafızadan sil.
        localStorage.removeItem('myChatWidgetSessionId');
        this.sessionId = null;
        this._clearMessages();
        
        // Sıfırdan yeni bir sohbet başlat.
        this.socket.emit('start_chat_from_widget', { hotel_identifier: this.settings.hotelIdentifier });
    });    

        // --- DÜZELTİLMİŞ OLAY DİNLEYİCİ ---
        this.socket.on('ai_response_to_widget', (data) => {
            // Sadece bizim oturumumuzla ilgiliyse ve ana metin paketiyse devam et.
            if (data.session_id === this.sessionId && data.is_full_text) {
                
                console.log("%c--- AI Yanıtı Alındı ---", "color: #4CAF50; font-weight: bold;");
                console.log("Gelen Ham Veri:", data);

                this._showTypingIndicator(false);
                
                const specialData = data.message_data || {};
                console.log("İşlenecek Özel Veri (message_data):", specialData);

                 // SADECE yazılı sohbet ekranı aktifse bildirim sesini çal.
                const isTextChatActive = this.elements.mainChatContainer.style.display === 'flex';
                if (isTextChatActive) {
                    this.audio.notification.play().catch(e => console.warn("Bildirim sesi oynatılamadı:", e));
                }
                
                 // Mesajı, animasyonu tetikleyecek şekilde ekrana bas.
                // Animasyon, yazılı sohbette her zaman olmalı.
                this._addMessageToUI(data.message, 'ai', data.timestamp, specialData, isTextChatActive)
                
                // Önce metni ekrana bas.
                //this._addMessageToUI(data.message, 'ai', data.timestamp, specialData);
                
                // --- KONTROLLER ARTIK DOĞRU YERDE (if bloğunun içinde) ---

                // Zafer animasyonunu kontrol et
                if (specialData.trigger_animation === 'victory') {
                    console.log("🏆 'victory' animasyon tetikleyicisi algılandı!");
                    if (this.elements.avatarSceneContainer.style.display === 'flex') {
                        this.playVictoryAnimation();
                    }
                }
                
                // Yüz ifadesini kontrol et
                if (specialData.set_expression) {
                    console.log(`😊 İfade komutu alındı: ${specialData.set_expression}`);
                    this.playExpression(specialData.set_expression);
                }
                // --------------------------------------------------------
            }
        });

        this.socket.on('audio_chunk_to_widget', (data) => {
            if (data.session_id === this.sessionId) {
                this.three.audioQueue.push(data);
                if (!this.three.isAudioQueuePlaying) {
                    this._playNextInAudioQueue();
                }
            }
        });

        this.socket.on('ai_stream_finished', (data) => {
            if (data.session_id === this.sessionId) {
                console.log("Sunucudan akışın bittiği bilgisi geldi.");
                this._showTypingIndicator(false);
            }
        });
    }

    _clearMessages() {
    if (!this.elements.messageArea) return;
    
    // Typing indicator hariç tüm mesajları sil
    const messages = this.elements.messageArea.querySelectorAll('.my-chat-message-entry');
    messages.forEach(msg => msg.remove());
}

 /**
     * YENİ FONKSİYON
     * Belirtilen yüz ifadesini oynatır.
     * @param {string} expressionName - Oynatılacak ifadenin adı (örn: 'smile').
     */
     playExpression(expressionName) {
        const recipe = this.expressionRecipes[expressionName];
        if (!recipe || !this.three.morphTargetDictionary) return;

        console.log(`😊 İfade aktive ediliyor: '${expressionName}'`);
        const now = this.three.expressionClock.getElapsedTime();

        recipe.forEach(part => {
            const index = this.three.morphTargetDictionary[part.name];
            if (index !== undefined) {
                this.three.activeExpressions[index] = {
                    startTime: now + part.start,
                    peakTime: now + part.peak,
                    endTime: now + part.end,
                    targetValue: part.targetValue
                };
            }
        });
    }


    /**
     * NİHAİ VERSİYON: Yüz ifadelerini günceller ve bunları dudak hareketleriyle
     * birleştirir. Arka plan gülümsemesini de yönetir.
     */
    _updateExpressions() {
        if (!this.three.morphTargetInfluences) return;

        const now = this.three.expressionClock.getElapsedTime();
        
        // Hedef ağırlıkları tutacak geçici bir obje
        const expressionTargets = {};

        // --- Arka Plan Gülümsemesi ---
        // Konuşmuyorken veya idle animasyonundayken hafif bir gülümseme ekle
        if (!this.three.isPlaying) {
            const smileLeftIndex = this.three.morphTargetDictionary['mouthSmileLeft'];
            const smileRightIndex = this.three.morphTargetDictionary['mouthSmileRight'];
            if (smileLeftIndex !== undefined && smileRightIndex !== undefined) {
                // Sinüs dalgası ile yavaş ve doğal bir gülümseme animasyonu
                const smileValue = (Math.sin(now * 0.5) + 1) / 2 * 0.35; // 0 ile 0.15 arasında gidip gelir
                expressionTargets[smileLeftIndex] = smileValue;
                expressionTargets[smileRightIndex] = smileValue;
            }
        }
        
        // --- Aktif İfadeler ---
        for (const index in this.three.activeExpressions) {
            const expr = this.three.activeExpressions[index];
            
            if (now > expr.endTime) {
                delete this.three.activeExpressions[index];
                continue;
            }

            let targetWeight = 0;
            if (now >= expr.startTime && now <= expr.peakTime) {
                const progress = (now - expr.startTime) / (expr.peakTime - expr.startTime);
                targetWeight = expr.targetValue * Math.sin(progress * Math.PI / 2);
            } else if (now > expr.peakTime && now < expr.endTime) {
                const progress = (now - expr.peakTime) / (expr.endTime - expr.peakTime);
                targetWeight = expr.targetValue * Math.cos(progress * Math.PI / 2);
            }

            // O anki blend shape için hedefi ayarla (daha yüksek olanı alarak birleştir)
            expressionTargets[index] = Math.max(expressionTargets[index] || 0, targetWeight);
        }

        // --- Değerleri Uygulama ---
        // Konuşma dışındaki tüm blend shape'leri güncelle
        for (const [name, index] of Object.entries(this.three.morphTargetDictionary)) {
            if (name.startsWith('viseme_')) {
                continue; // viseme'leri atla, onlar _updateLipSync'in işi
            }
            
            const targetWeight = expressionTargets[index] || 0;

            this.three.morphTargetInfluences[index] = THREE.MathUtils.lerp(
                this.three.morphTargetInfluences[index],
                targetWeight,
                0.1 // İfadeler için yumuşak geçiş
            );
        }
    }


     _addEventListeners() {
                
        // Ana Olaylar
        this.elements.launcher.addEventListener('click', () => this.toggleWindow());

        // Karşılama Ekranı Butonları
        const startTextBtn = document.getElementById('my-chat-start-text-btn');
        const startVideoBtn = document.getElementById('my-chat-start-video-btn');
        if (startTextBtn) startTextBtn.addEventListener('click', () => this._startTextChat());
        if (startVideoBtn) startVideoBtn.addEventListener('click', () => this._startVideoChat());

        // Header Butonları (Ana Header için)
        // Bu butonlar klonlanmadan önce de var olduğu için olaylarını burada atayabiliriz.
        this.elements.closeButton.addEventListener('click', () => this.toggleWindow(false));
        this.elements.backButton.addEventListener('click', () => this._showWelcomeScreen());
        this.elements.speakerButton.addEventListener('click', () => this._toggleAudio());

        // Normal Yazışma Ekranı Input Alanı Olayları
        if (this.elements.sendButton) {
            this.elements.sendButton.addEventListener('click', () => this._sendMessage());
        }
        
        if (this.elements.messageInput) {
        
        this.elements.messageInput.addEventListener('input', () => {
            const textarea = this.elements.messageInput;
            const inputArea = this.elements.inputArea;
          
            // 2. Yüksekliği dinamik olarak ayarla (WhatsApp gibi)
            // Önce yüksekliği sıfırla ki, 'scrollHeight' doğru hesaplanabilsin
            textarea.style.height = 'auto'; 
            // Şimdi yeni 'scrollHeight' değerini 'height' olarak ata
            textarea.style.height = `${textarea.scrollHeight}px`;
        });

        // Enter tuşu ile gönderme (Değişiklik yok, bu zaten doğruydu)
        this.elements.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault(); 
                this._sendMessage();
            }
        });
    }

    if (this.elements.sendButton) {
        this.elements.sendButton.addEventListener('click', () => this._sendMessage());
    }
            
        // Lightbox Kapatma
        if (this.elements.lightboxOverlay) {
            this.elements.lightboxOverlay.addEventListener('click', (e) => {
                if (e.target === this.elements.lightboxOverlay || e.target === this.elements.lightboxClose) {
                    this._hideLightbox();
                }
            });
        }
        console.log("--- MyChatWidget: _addEventListeners BİTTİ ---");
    }

    _switchView(viewName) {
    // Tüm ana görünümleri gizle
    this.elements.welcomeScreen.style.display = 'none';
    this.elements.mainChatContainer.style.display = 'none';
    this.elements.avatarSceneContainer.style.display = 'none';
    
    // İstenen görünümü göster
    if (viewName === 'welcome' && this.elements.welcomeScreen) {
        this.elements.welcomeScreen.style.display = 'flex';
    } else if (viewName === 'chat' && this.elements.mainChatContainer) {
        this.elements.mainChatContainer.style.display = 'flex';
    } else if (viewName === 'avatar' && this.elements.avatarSceneContainer) {
        this.elements.avatarSceneContainer.style.display = 'flex';
    }
}

    // /// YENİ METOD: Yazışma modunu başlatır ///
    _startTextChat() {
    this._switchView('chat');

    // Header butonlarının görünürlüğünü ayarla
    this.elements.backButton.style.display = 'flex';
    this.elements.speakerButton.style.display = 'none';
    this.elements.closeButton.style.display = 'flex';

    // === DÜZELTME: isAvatarModeActive bayrağını kapatmak, animasyon döngüsünü durdurmak için yeterlidir. ===
    this.three.isAvatarModeActive = false;
    
    // --- BU SATIRI SİLİYORUZ! WebGL context'ini yok etmeye gerek yok. ---
    // if (this.three.renderer) {
    //     this.three.renderer.forceContextLoss();
    // }

    console.log("Avatar modu duraklatıldı. Yazılı sohbet başlatıldı.");
    if (!this.socket || !this.socket.connected) {
        this._initSocketConnection();
    }
}

   _startVideoChat() {
    this._switchView('avatar');

    // Header butonlarının görünürlüğünü ayarla
    this.elements.backButton.style.display = 'flex';
    this.elements.speakerButton.style.display = 'flex';
    this.elements.closeButton.style.display = 'flex';
    
    // Ses ayarını uygula
    if (this.three.audioSource && this.three.audioSource.gain) {
        this.three.audioSource.gain.gain.value = this.isAudioEnabled ? 1 : 0;
    }
    
    // === DÜZELTME: Sahne daha önce oluşturulduysa, sadece animasyonu yeniden başlat. ===
    if (!this.three.scene) {
        // Sahne hiç oluşturulmadıysa, şimdi oluştur.
        if (!this.three.audioContext) {
            this.three.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        }
        this._initThreeScene();
    }
    
    if (!this.socket || !this.socket.connected) {
        this._initSocketConnection();
    }

    // Animasyon döngüsünü GÜVENLE yeniden başlat
    this.three.isAvatarModeActive = true;
    this._animate(); 
}

/**
     * Sadece "barge-in" (söze girme) tespiti için kullanıcının mikrofonunu
     * sessizce dinlemeye başlar.
     */
    async _startMicrophoneMonitoringForBargeIn() {
         return;
        // Zaten bir akış varsa, tekrar başlatma.
        if (this.three.micStream) return;
        
        try {
            // Kullanıcıdan mikrofon izni iste.
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            this.three.micStream = stream;
            
            // Eğer AudioContext kapalıysa (askıdaysa), onu uyandır.
            if (this.three.audioContext.state === 'suspended') {
                await this.three.audioContext.resume();
            }

            // Web Audio API düğümlerini oluştur.
            const source = this.three.audioContext.createMediaStreamSource(stream);
            const analyser = this.three.audioContext.createAnalyser();
            analyser.fftSize = 512; // Analiz hassasiyeti
            analyser.smoothingTimeConstant = 0.5; // Değerlerin yumuşatılması

            // ÖNEMLİ: Mikrofon sesini hoparlöre BAĞLAMIYORUZ.
            // Sadece analiz için kullanıyoruz, böylece kullanıcı kendi sesini duymaz.
            source.connect(analyser);
            
            this.three.micAnalyser = analyser;
            console.log("✅ Barge-in için mikrofon dinlemesi aktif.");

        } catch (err) {
            console.error("Mikrofon erişimi hatası (barge-in için):", err);
            // Hata durumunda kullanıcıya bir mesaj gösterilebilir.
        }
    }


      _initThreeScene() {
        const canvas = document.getElementById('my-chat-avatar-canvas');
        if (!canvas) return;

        this.three.scene = new THREE.Scene();
        this.three.camera = new THREE.PerspectiveCamera(25, canvas.clientWidth / canvas.clientHeight, 0.1, 1000);
        this.three.camera.position.set(0, 1.3, 1.8);

        this.three.renderer = new THREE.WebGLRenderer({ canvas, antialias: false, alpha: true });
        this.three.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        // _initThreeScene içindeki ilgili satırı güncelleyin
const maxPixelRatio = Math.min(window.devicePixelRatio, 2); // <-- Maksimum 2 ile sınırla
this.three.renderer.setPixelRatio(maxPixelRatio);
        this.three.renderer.outputColorSpace = THREE.SRGBColorSpace;
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 2.5);
        directionalLight.position.set(1, 2, 3);
        this.three.scene.add(directionalLight);

        const ambientLight = new THREE.AmbientLight(0xffffff, 1.0);
        this.three.scene.add(ambientLight);

        // YENİ: Arka plan resminin tam URL'ini oluştur
        const backgroundUrl = `${this.settings.backendUrl}/static/models/reception.jpg`;
        console.log(`INFO: Arka plan resmi yükleniyor: ${backgroundUrl}`);
        const textureLoader = new THREE.TextureLoader();
        textureLoader.load(
            backgroundUrl, // DÜZELTME: Dinamik URL kullanılıyor
            (texture) => {
                texture.colorSpace = THREE.SRGBColorSpace;
                this.three.scene.background = texture;
                console.log("✅ Arka plan resmi yüklendi.");
            },
            undefined,
            (err) => {
                console.error("Arka plan resmi yüklenemedi:", err);
                this.three.scene.background = new THREE.Color(0x333333);
            }
        );

        // Yükleyiciyi göster
        this.elements.avatarLoader.classList.add('visible');

        // YENİ: GLB modelin tam URL'ini oluştur
        const modelUrl = `${this.settings.backendUrl}/static/models/avatarkad9.glb`;
        console.log(`INFO: 3D model yükleniyor: ${modelUrl}`);
        const gltfLoader = new GLTFLoader();
        
        gltfLoader.load(
            modelUrl, // DÜZELTME: Dinamik URL kullanılıyor
            (gltf) => {
                // ... (onLoad callback fonksiyonunun geri kalanı aynı) ...
                this.three.avatar = gltf.scene;
                this.three.scene.add(this.three.avatar);
                this.three.avatar.position.y = -0.1;
                this.three.avatar.position.x = 0;
                this.three.avatar.position.z = -0.8;
                this.three.avatar.traverse(node => {
                    if (node.isSkinnedMesh && node.morphTargetDictionary) {
                        this.three.morphTargetDictionary = node.morphTargetDictionary;
                        this.three.morphTargetInfluences = node.morphTargetInfluences;
                    }
                    if (node.isBone) {
                        if (node.name === 'mixamorigHead') this.three.headBone = node;
                        if (node.name === 'mixamorigNeck') this.three.neckBone = node;
                        if (node.name === 'mixamorigSpine2') this.three.spineBone = node;
                    }
                });
                if (this.three.headBone) console.log("✅ Kafa kemiği bulundu.");
                if (this.three.morphTargetDictionary) console.log("✅ Morph target'lar bulundu.");
                if (this.three.morphTargetInfluences) { this.three.morphTargetInfluences.fill(0); }
                this.three.animations = gltf.animations;
                this.loadAnimations(); 
                console.log("✅ Avatar ve tüm bileşenleri başarıyla yüklendi ve ayarlandı.");
                this.elements.avatarLoader.classList.remove('visible');
            }, 
            undefined,
            (error) => {
                console.error("Avatar yüklenirken kritik bir hata oluştu:", error);
                this.elements.avatarLoader.classList.remove('visible');
            }
        );

        // Ana animasyon döngüsünü başlat
        this._animate();
    }

    // === YENİ YARDIMCI FONKSİYON: Animasyonları Yükle ve Uygula ===
    /**
     * YENİ FONKSİYON
     * Avatar için gerekli animasyonları yükler ve yönetir.
     */
    /**
     * NİHAİ GÜNCELLEME:
     * "No target node" hatasını çözmek için, animasyonun kökünü (root)
     * ve mikserin hedefini dinamik olarak bulur ve eşleştirir.
     */
     loadAnimations() {
        if (!this.three.avatar || !this.three.animations) return;

        const animations = this.three.animations;
        this.three.mixer = new THREE.AnimationMixer(this.three.avatar);
        this.three.animationActions = {};

        for (let i = 0; i < animations.length; i++) {
            const clip = animations[i];
            const action = this.three.mixer.clipAction(clip);
            
            if (clip.name.includes('Layer0')) continue;

            this.three.animationActions[clip.name] = action;
            
            // "victory" gibi tek seferlik animasyonları ayarla
            if (clip.name.toLowerCase() === 'victory') {
                action.setLoop(THREE.LoopOnce);
                action.clampWhenFinished = true;
            } 
            // DİĞER TÜM ANİMASYONLARI (idle'lar dahil) sonsuz döngüye ayarla.
            else {
                action.setLoop(THREE.LoopRepeat);
            }
        }
        
        if (Object.keys(this.three.animationActions).length === 0) return;

        console.log("✅ Yüklenen Animasyon Eylemleri:", Object.keys(this.three.animationActions));
        
        // Artık 'finished' olayını dinlemiyoruz.
        // Onun yerine, başlangıçta ilk idle animasyonunu doğrudan başlatıyoruz.
        const firstIdle = Object.keys(this.three.animationActions).find(name => name.toLowerCase().startsWith('idle'));
        if(firstIdle) {
            this.three.activeAction = this.three.animationActions[firstIdle];
            this.three.activeAction.play();
        }

        // Belirli aralıklarla idle animasyonunu değiştirecek zamanlayıcıyı kur.
        //this.startIdleAnimationSwitcher();
    }

    /**
     * YENİ FONKSİYON
     * 5 ila 10 saniye arasında rastgele bir süre belirleyip,
     * o süre sonunda idle animasyonunu değiştirir.
     */
    startIdleAnimationSwitcher() {
        const switchIdle = () => {
            this.playIdleAnimation(); // Rastgele bir sonraki idle'a geç
            
            // Bir sonraki geçiş için yeni bir rastgele süre belirle
            const nextSwitchTime = (5000 + Math.random() * 5000); // 5-10 saniye
            setTimeout(switchIdle, nextSwitchTime);
        };
        
        // İlk geçişi 5-10 saniye sonra yap
        const firstSwitchTime = 5000 + Math.random() * 5000;
        setTimeout(switchIdle, firstSwitchTime);
    }

      /**
     * YENİ YARDIMCI FONKSİYON
     * Mevcut idle animasyonları arasından rastgele birini seçip oynatır.
     * Bir önceki animasyondan yumuşak bir geçiş (cross-fade) yapar.
     */
    playIdleAnimation() {
        const idleActions = Object.keys(this.three.animationActions)
                                 .filter(name => name.toLowerCase().startsWith('idle'));
        
        if (idleActions.length <= 1) return; // Değişecek başka animasyon yoksa çık
        
        // Şu anki animasyondan farklı, rastgele bir animasyon seç
        let nextActionName;
        do {
            nextActionName = idleActions[Math.floor(Math.random() * idleActions.length)];
        } while (this.three.activeAction && this.three.activeAction.getClip().name === nextActionName);
        
        console.log(`Idle animasyonu değiştiriliyor -> ${nextActionName}`);
        this.fadeToAction(nextActionName, 1.0); // Geçiş süresini 1 saniye yapalım
    }

    playVictoryAnimation() {
        // ... (Bu fonksiyon aynı kalabilir, ama 'finished' olayını biz yöneteceğiz)
        if (!this.three.animationActions['victory']) return;
        
        this.fadeToAction('victory', 0.2);

        // Zafer animasyonu bittiğinde idle'a dönmek için bir zamanlayıcı kur.
        const victoryClip = this.three.animationActions['victory'].getClip();
        const durationInMs = victoryClip.duration * 1000;
        
        setTimeout(() => {
            console.log("Victory bitti, idle döngüsüne dönülüyor.");
            this.playIdleAnimation();
        }, durationInMs - 200); // Bitişten 0.2 saniye önce geçişi başlat
    }

     /**
     * YENİ YARDIMCI FONKSİYON
     * Bir animasyondan diğerine yumuşak bir geçiş (cross-fade) sağlar.
     * @param {string} name - Oynatılacak yeni animasyonun adı.
     * @param {number} duration - Geçişin ne kadar süreceği (saniye).
     */
       fadeToAction(name, duration) {
    this.three.lastAction = this.three.activeAction;
    this.three.activeAction = this.three.animationActions[name];
    
    if (this.three.lastAction === this.three.activeAction || !this.three.activeAction) {
        return;
    }

    // .reset() fonksiyonunu kaldırdık!
    this.three.activeAction
        .setEffectiveWeight(1)
        .play();

    // crossFadeTo geçişi yönetecek.
    if (this.three.lastAction) {
        this.three.lastAction.crossFadeTo(this.three.activeAction, duration, true);
    } else {
        this.three.activeAction.fadeIn(duration);
    }
}



    /// Ana animasyon döngüsü ///
   _animate() {
    // 1. ÖNCE KONTROL ET: Avatar modu aktif değilse, döngüyü hemen durdur.
    if (!this.three.isAvatarModeActive) {
        console.log("Animasyon döngüsü duraklatıldı.");
        return; 
    }

    // 2. Döngüyü bir sonraki kare için tekrar kur.
    requestAnimationFrame(() => this._animate());
    
    // 3. Eğer buraya geldiysek, avatar modu aktiftir. Normal işlemlere devam et.
    if (!this.three.renderer || !this.three.scene) return;
    
    const delta = this.three.clock.getDelta();
    
    if (this.three.mixer) {
        this.three.mixer.update(delta);
    }
    
    this._updateLipSync();
    this._updateExpressions();
    this._updateBlinking(delta);
    this._updateNaturalLook();
    
    this.three.renderer.render(this.three.scene, this.three.camera);
}
     
    /**
     * YENİ FONKSİYON
     * Avatarın kafa ve boyun hareketlerini yöneterek daha doğal bir
     * duruş ve bakış sağlar.
     */
     _updateNaturalLook() {
        // Gerekli nesneler bulunamadıysa çık
        if (!this.three.avatar || !this.three.camera || !this.three.headBone) {
            return;
        }

        // --- 1. HEDEFİ BELİRLE ---
        // Hedefimiz her zaman kameranın dünya pozisyonudur.
        const targetPosition = this.three._lookAtTarget;
        this.three.camera.getWorldPosition(targetPosition);

        // --- 2. DURUMA GÖRE DAVRANIŞI AYARLA ---

        if (this.three.isPlaying) {
            // --- SENARYO: AVATAR KONUŞUYOR ---
            // Amaç: Sabit ve direkt bakış. Animasyonun kafa hareketini eziyoruz.

            // Kafa kemiğinin rotasyonunu, kameraya bakacak şekilde AYNI KAREDE AYARLA.
            // Bu, en direkt ve en sert yöntemdir ama konuşma sırasında en stabil sonucu verir.
            this.three.headBone.lookAt(targetPosition);

            // İsteğe bağlı: Boyun ve omurgayı da hafifçe dahil etmek için.
            // Bu, bakışı daha az "robotik" yapabilir.
            if (this.three.neckBone) {
                this.three.neckBone.lookAt(targetPosition);
            }

        } else {
        // --- SENARYO: AVATAR BOŞTA (Yeniden kullanılan nesneler ile) ---
        const bodyTargetQuaternion = this.three._bodyTargetQuaternion;
        const avatarPosition = this.three._avatarPosition;
        const tempMatrix = this.three._tempMatrix;

        this.three.avatar.getWorldPosition(avatarPosition);
        tempMatrix.lookAt(new THREE.Vector3(targetPosition.x, avatarPosition.y, targetPosition.z), avatarPosition, this.three.avatar.up);
        bodyTargetQuaternion.setFromRotationMatrix(tempMatrix);
        this.three.avatar.quaternion.slerp(bodyTargetQuaternion, 0.05);

        const time = this.three.clock.getElapsedTime();
        targetPosition.x += Math.sin(time * 0.7) * 0.03;
        targetPosition.y += Math.cos(time * 0.4) * 0.04;

        const headTargetQuaternion = this.three._headTargetQuaternion;
        const headMatrix = this.three._headMatrix;

        headMatrix.lookAt(targetPosition, this.three.headBone.position, this.three.headBone.up);
        headTargetQuaternion.setFromRotationMatrix(headMatrix);

        this.three.headBone.quaternion.slerp(headTargetQuaternion, 0.05);
    }
    }

// Barge-in'i kontrol eden yeni metod
/**
     * Her animasyon karesinde çalışarak kullanıcının konuşup konuşmadığını kontrol eder.
     */
    _checkForBargeIn() {
        // Sadece AI konuşurken ve mikrofon analizörü hazırsa kontrol et.
        if (this.three.isPlaying && this.three.micAnalyser && !this.three.isBargeInDetected) {
            const dataArray = new Uint8Array(this.three.micAnalyser.frequencyBinCount);
            this.three.micAnalyser.getByteTimeDomainData(dataArray);

            let peak = 0;
            for (const amplitude of dataArray) {
                const value = Math.abs(amplitude - 128); // Merkezden sapma
                if (value > peak) {
                    peak = value;
                }
            }
            
            // Eşik değeri. Ortam gürültüsüne göre ayarlanması gerekebilir.
            // Değerler 0-128 arasındadır.
            const bargeInThreshold = 20; 

            if (peak > bargeInThreshold) {
                console.log(`Barge-in algılandı! Mikrofon Zirve Değeri: ${peak}`);
                this.three.isBargeInDetected = true; // Bayrağı ayarla
                
                // AI'ın sesini yavaşça kısıp durdur.
                this._fadeOutAudio(); 
            }
        }
    }


    // Base64'ten ArrayBuffer'a çeviren fonksiyon (bu zaten doğru)
    async _base64ToArrayBuffer(base64) {
        const res = await fetch(`data:audio/mpeg;base64,${base64}`);
        return res.arrayBuffer();
    }

   _addMessageToUI(text, sender, timestamp, messageData = {}, withAnimation = false) {
    if (!this.elements.messageArea) return;

    // 1. DocumentFragment ile sanal ortamda çalış, gerçek DOM'u rahat bırak.
    const fragment = document.createDocumentFragment();

    // 2. Elementleri oluştur (Bu kısım zaten iyiydi, aynen kalıyor).
    const messageEntry = this._createMessageEntryDOM(text, sender, timestamp, messageData);

    // 3. Oluşturulan tüm yapıyı tek parça halinde fragment'a ekle.
    fragment.appendChild(messageEntry);
    
    // 4. Fragment'ı tek bir işlemle gerçek DOM'a ekle. Bu, reflow/repaint sayısını en aza indirir.
    this.elements.messageArea.insertBefore(fragment, this.elements.typingIndicator);

    // 5. Animasyonu tetikle. Element artık DOM'da olduğu için sınıf ekleyebiliriz.
    if (withAnimation) {
        // Tarayıcının elementi fark etmesi için bir sonraki tick'i bekle
        requestAnimationFrame(() => {
            messageEntry.classList.add('new-message-animation');
        });
    }

    // 6. Kaydırma işlemini akıllı hale getir.
    this._smartScroll();
}

/**
 * YENİ AKILLI KAYDIRMA FONKSİYONU
 * Sadece kullanıcı zaten en alttaysa otomatik kaydırır.
 * Bu, kullanıcının eski mesajları okurken rahatsız edilmesini engeller.
 */
_smartScroll() {
    const el = this.elements.messageArea;
    if (!el) return;

    // Tolerans payı (birkaç piksel fark olabilir)
    const tolerance = 50; 
    
    // Kullanıcı en altta mı kontrolü:
    // (Kaydırılan miktar + Görünen yükseklik) >= (Toplam yükseklik - Tolerans)
    const isAtBottom = el.scrollTop + el.clientHeight >= el.scrollHeight - tolerance;

    if (isAtBottom) {
        // Sadece en alttaysa, animasyonlu ve yumuşak bir şekilde en dibe kaydır.
        el.scrollTo({
            top: el.scrollHeight,
            behavior: 'smooth' // Bu, modern tarayıcılarda yumuşak bir kaydırma sağlar.
        });
    }
}

/**
 * YENİ YARDIMCI FONKSİYON: Bir mesajın tüm DOM yapısını oluşturur ve döndürür.
 * Bu, _addMessageToUI'yi temiz tutar.
 */
_createMessageEntryDOM(text, sender, timestamp, messageData) {
    const messageEntry = document.createElement('div');
    messageEntry.className = `my-chat-message-entry ${sender}`;
    
    // Kod tekrarını önlemek için bu mantığı aynen koruyoruz, sadece ayrı bir fonksiyona taşıdık.
    const wrapper = document.createElement('div');
    wrapper.className = `my-chat-message-wrapper ${sender}`;

    if (sender === 'ai' && this.settings.aiAvatarUrl) {
        wrapper.appendChild(this._createAvatarElement(this.settings.aiAvatarUrl, 'AI Avatar'));
    }

    const messageElement = document.createElement('div');
    messageElement.classList.add('my-chat-message', `my-chat-message-${sender}`);

    const contentContainer = document.createElement('div');
    contentContainer.className = 'content-container';

    if (text && typeof text === 'string' && text.trim() !== '') {
        const textNode = document.createElement('div');
        textNode.innerHTML = this._formatTextToHtml(text, sender);
        contentContainer.appendChild(textNode);
    }

    if (sender === 'ai' && messageData?.type === 'gallery' && messageData.items?.length > 0) {
        this._renderImageGallery(contentContainer, messageData.items);
    }

    messageElement.appendChild(contentContainer);
    wrapper.appendChild(messageElement);
    messageEntry.appendChild(wrapper);

    if (this.settings.showTimestamps && timestamp) {
        const timeContainer = document.createElement('div');
        timeContainer.className = 'my-chat-timestamp-container';
        try {
            const date = new Date(timestamp);
            if (!isNaN(date)) {
                timeContainer.textContent = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            }
        } catch (e) {}
        messageEntry.appendChild(timeContainer);
    }
    
    return messageEntry;
}

     /**
     * Backend'den gelen kelime ve zamanlama listesini, avatarın anlayacağı
     * blend shape animasyon komutları listesine dönüştürür.
     * @param {Array} wordTimings - Örnek: [{time: 0.1, value: "Merhaba"}, {time: 0.5, value: "dünya"}]
     * @returns {Array} - Örnek: [{time: 0.05, value: "viseme_kk"}, {time: 0.12, value: "viseme_E"}, ...]
     */
      _convertWordTimingsToVisemes(wordTimings) {
        // Eğer veri yoksa boş bir dizi döndür.
        if (!wordTimings || wordTimings.length === 0) return [];

        const visemes = [];
        const { latencyOffset, vowelDuration, consonantDuration, lipCloseDuration } = this.animationParams;
        
        let lastWordEndTime = 0;

        // Gelen her bir kelime için döngüye gir
        for (const wordData of wordTimings) {
            let { time: wordStartTime, value: word } = wordData;

            // Kelimenin başlangıç zamanını, ağ gecikmesini telafi etmek için ayarla.
            wordStartTime -= latencyOffset;
            if (wordStartTime < 0) wordStartTime = 0;
            
            // Eğer kelimeler arasında büyük bir boşluk varsa, araya bir "sessizlik" ekle.
            if (wordStartTime > lastWordEndTime + 0.1) { // 100ms'den fazla boşluk varsa
                 visemes.push({ time: lastWordEndTime, value: 'viseme_sil' });
            }

            let currentTimeInWord = wordStartTime;
            
            // Kelimeyi küçük harfe çevir ve Türkçe karakterler için normalleştir.
            word = word.toLowerCase().trim();
            if (!word) continue; // Boş kelimeleri atla

            // Regex ile kelimeyi sesli ve sessiz harf gruplarına ayır.
            // Örnek: "merhaba" -> ["m", "e", "rh", "a", "b", "a"] gibi (yaklaşık)
            const phonemeChunks = word.match(/([aeıioöuü]+)|([bcçdfgğhjklmnprsştvyz]+)/g) || [];

            // Her bir ses grubu için döngüye gir
            for (const chunk of phonemeChunks) {
                const firstChar = chunk[0];
                let visemeShape = 'viseme_sil'; // Varsayılan şekil
                let duration = 0;

                // Eğer ses grubu sesli harflerden oluşuyorsa...
                if ('aeıioöuü'.includes(firstChar)) {
                    // Sesli harf grubunun ortasındaki harfi temsilci olarak al (örn: "aa" için 'a').
                    const representativeVowel = chunk[Math.floor(chunk.length / 2)];
                    visemeShape = this.phonemeToVisemeMap[representativeVowel] || 'viseme_sil';
                    duration = vowelDuration * chunk.length; // Süreyi harf sayısıyla çarp
                } 
                // Eğer ses grubu sessiz harflerden oluşuyorsa...
                else {
                    const representativeConsonant = firstChar; // İlk sessiz harfi baz al
                    visemeShape = this.phonemeToVisemeMap[representativeConsonant] || 'viseme_sil';
                    
                    // 'p, b, m' gibi dudakların birleştiği sesler için ayrı bir süre kullan.
                    duration = ('pbm'.includes(representativeConsonant)) ? lipCloseDuration : consonantDuration;
                }

                // Eğer geçerli bir viseme şekli bulunduysa, animasyon kuyruğuna ekle.
                if (visemeShape !== 'viseme_sil') {
                    visemes.push({ time: currentTimeInWord, value: visemeShape });
                }
                
                // Bir sonraki ses grubunun başlangıç zamanını ilerlet.
                currentTimeInWord += duration;
            }
            
            lastWordEndTime = currentTimeInWord; // Bu kelimenin bittiği zamanı kaydet.
        }

        // Animasyonun sonunda ağzın kapandığından emin olmak için son bir "sessizlik" komutu ekle.
        if (visemes.length > 0) {
            visemes.push({ time: lastWordEndTime, value: 'viseme_sil' });
        }

        // Son olarak, tüm viseme listesini zamanına göre sırala ve ardışık aynı olanları temizle.
        // Bu, animasyonda gereksiz "titreşimleri" önler.
        visemes.sort((a, b) => a.time - b.time);
        
        const finalVisemes = visemes.filter((viseme, index, self) =>
            index === 0 || viseme.value !== self[index - 1].value
        );

        console.log("Üretilen Viseme Kuyruğu:", finalVisemes); // Hata ayıklama için konsola yazdır.
        return finalVisemes;
    }

 /**
 * Gelen ses ve viseme verisini alarak dudak senkronizasyonunu ve sesi oynatır.
 * Bu fonksiyon, akışlı ses kuyruğunun bir parçasını işler.
 * @param {Array} wordTimings - Backend'den gelen kelime ve zamanlama verisi.
 * @param {string} audioBase64 - Oynatılacak sesin base64 formatındaki verisi.
 */
 async playLipSync(wordTimings, audioBase64) {
        // Gerekli bileşenler hazır değilse, işlemi güvenli bir şekilde iptal et.
        if (!this.three.avatar || !this.three.audioContext) {
            console.error("LipSync başlatılamıyor: Avatar veya AudioContext hazır değil.");
            // Bir sonraki parçayı denemek için kuyruğu tetikle.
            this._playNextInAudioQueue();
            return;
        }

        // Önceki bir ses hala çalıyorsa (nadiren olur), onu durdur.
        if (this.three.audioSource) {
            this.three.audioSource.stop(0);
        }

        this.three.isBargeInDetected = false;

        // 1. Viseme Animasyon Kuyruğunu Hazırla
        // Gelen kelime zamanlamalarını, 3D modelin anlayacağı animasyon komutlarına çevir.
        this.three.visemeQueue = this._convertWordTimingsToVisemes(wordTimings);
        
        // 2. Animasyon Durumunu Ayarla
        this.three.isPlaying = true; // Hem ses hem animasyon oynatılıyor.
        // Oynatma başlangıç zamanını, tüm animasyonun referans alacağı şekilde kaydet.
        this.three.audioStartTime = this.three.audioContext.currentTime;

        // 3. Sesi Hazırla ve Oynat
      try {
            const audioBuffer = await this._base64ToArrayBuffer(audioBase64);
            const decodedBuffer = await this.three.audioContext.decodeAudioData(audioBuffer);
            
            const source = this.three.audioContext.createBufferSource();
            source.buffer = decodedBuffer;
            
            // YENİ: Ses seviyesini kontrol etmek için GainNode oluştur.
            const gainNode = this.three.audioContext.createGain();
            gainNode.gain.value = this.isAudioEnabled ? 1 : 0; // Mevcut ses ayarına göre başlat.
            
            // Zincir: Kaynak -> Ses Seviyesi -> Hoparlör
            source.connect(gainNode).connect(this.three.audioContext.destination);
            
            this.three.audioSource = source;
            this.three.audioSource.gain = gainNode; // GainNode'u referans olarak sakla

            source.onended = () => {
                this.three.isPlaying = false;
                this._playNextInAudioQueue();
            };

            source.start(0);

        } catch (error) {
            // ... (hata yönetimi aynı)
        }
    }

 _playNextInAudioQueue() {
        // Eğer kuyrukta işlenecek başka ses yoksa, işlemi sonlandır.
        if (this.three.audioQueue.length === 0) {
            console.log("Ses kuyruğu tamamlandı.");
            this.three.isAudioQueuePlaying = false; // Ana kuyruk artık boşta.
            return;
        }

        // Ana kuyruğun meşgul olduğunu işaretle.
        this.three.isAudioQueuePlaying = true;
        
        // Kuyruktan sıradaki ses paketini al.
        const audioData = this.three.audioQueue.shift();

        // Bu ses paketi için dudak senkronizasyonunu ve sesi başlat.
        this.playLipSync(audioData.word_timings, audioData.audio_content_b64);
    }

// YENİ: Sadece sesi çalmaktan sorumlu fonksiyon
async _playAudioForLipSync(audioBase64) {
    // Eğer ses kapalıysa, hiçbir şey yapma
    if (!this.isAudioEnabled) {
        if(this.three.audioSource) this.three.audioSource.stop(0);
        return;
    }

    try {
        const audioBuffer = await this._base64ToArrayBuffer(audioBase64);
        const decodedBuffer = await this.three.audioContext.decodeAudioData(audioBuffer);
        const source = this.three.audioContext.createBufferSource();
        source.buffer = decodedBuffer;
        
        const gainNode = this.three.audioContext.createGain();
        const analyser = this.three.audioContext.createAnalyser();
        analyser.fftSize = 512;

        source.connect(analyser).connect(gainNode).connect(this.three.audioContext.destination);

        this.three.audioSource = source;
        this.three.audioSource.gain = gainNode;
        this.three.analyser = analyser;
        
        source.start(0);
    } catch (error) {
        console.error("Ses çalma hatası:", error);
    }
}

// YENİ: Sadece dudak animasyonunu yöneten fonksiyon
_playVisemeAnimation(wordTimings) {
    if (this.three.isPlaying) return; // Zaten bir animasyon oynuyorsa, üzerine yazma

    this.three.visemeQueue = this._convertWordTimingsToVisemes(wordTimings);
    this.three.isPlaying = true; // Sadece dudak animasyonunun oynadığını belirtir
    this.three.audioStartTime = this.three.audioContext.currentTime;

    // Animasyonun tahmini süresini hesapla
    const lastViseme = this.three.visemeQueue[this.three.visemeQueue.length - 1];
    const estimatedDuration = lastViseme ? lastViseme.time : 0.5;

    // Bu süre sonunda, bir sonraki kuyruk elemanını tetikle
    setTimeout(() => {
        this.three.isPlaying = false; // Bu animasyon bitti
        if (!this.three.isBargeInDetected) {
            this._playNextInAudioQueue();
        } else {
            this.three.isBargeInDetected = false;
            this.three.isAudioQueuePlaying = false;
        }
    }, (estimatedDuration + 0.2) * 1000); // +0.2sn tampon
}
    /**
     * NİHAİ VERSİYON: Dudak senkronizasyonunu GÜVENİLİR bir şekilde günceller.
     * Sadece konuşmayla ilgili blend shape'leri etkiler.
     */
    _updateLipSync() {
        if (!this.three.morphTargetInfluences || !this.three.morphTargetDictionary) return;

        if (this.three.isPlaying) {
            const elapsedTime = this.three.audioContext.currentTime - this.three.audioStartTime;
            let lastVisemeShape = null;
            while (this.three.visemeQueue.length > 0 && elapsedTime >= this.three.visemeQueue[0].time) {
                lastVisemeShape = this.three.visemeQueue.shift().value;
            }
            if (lastVisemeShape) {
                this.three.currentViseme = lastVisemeShape;
            }
        } else {
            this.three.currentViseme = 'viseme_sil';
        }

        // SADECE viseme'leri (ağız şekilleri) güncelle
        for (const [name, index] of Object.entries(this.three.morphTargetDictionary)) {
            if (name.startsWith('viseme_')) {
                const targetWeight = (name === this.three.currentViseme) ? (this.visemeWeights[name] || 1.0) : 0.0;
                this.three.morphTargetInfluences[index] = THREE.MathUtils.lerp(
                    this.three.morphTargetInfluences[index],
                    targetWeight,
                    this.animationParams.lerpFactor
                );
            }
        }
    }


    _fadeOutAudio(duration = 0.3) {
        if (!this.three.audioSource || !this.three.audioSource.context) return;
        
        // GainNode'u bulmak için ses kaynağının bağlı olduğu yerleri kontrol etmeliyiz.
        // Modern bir yaklaşımla, gain'i doğrudan source'a ekleyelim.
        // Bu yüzden playLipSync'te source'a gainNode'u eklemek önemlidir.
        // Şimdilik, eğer varsa kullanalım. Genellikle bu yapı doğrudan desteklenmez,
        // bu yüzden ses zincirini doğru kurmak esastır.
        // Biz şimdilik doğrudan stop() kullanalım, bir sonraki adımda gain zincirini kurarız.
        
        console.log("Sesi anında durduruyorum (barge-in)...");
        // `onended` olayı otomatik olarak tetiklenecek ve kuyruğu temizleyecektir.
        this.three.audioSource.stop(0);  
    }

    // Tüm viseme'leri yavaşça sıfırlayan yardımcı fonksiyon
    _resetAllVisemes() {
        if (!this.three.morphTargetInfluences || !this.three.morphTargetDictionary) return;

        for (const [name, index] of Object.entries(this.three.morphTargetDictionary)) {
            if (name.startsWith('viseme_')) {
                this.three.morphTargetInfluences[index] = THREE.MathUtils.lerp(
                    this.three.morphTargetInfluences[index], 0, 0.2
                );
            }
        }
    }


    // /// GÜNCELLEME: Göz kırpma mantığı ///
    _updateBlinking(delta) {
        if (!this.three.morphTargetInfluences) return;

        // Modelinizdeki blend shape isimlerini kullanın
        const blinkIndex = this.three.morphTargetDictionary['eyeBlinkLeft'];
        const blinkRightIndex = this.three.morphTargetDictionary['eyeBlinkRight'];

        if (blinkIndex === undefined || blinkRightIndex === undefined) return;

        // Gözleri yavaşça aç (her frame'de çalışır)
        this.three.morphTargetInfluences[blinkIndex] *= 0.85;
        this.three.morphTargetInfluences[blinkRightIndex] *= 0.85;

        // Zamanlayıcıyı güncelle
        this.three.lastBlinkTime -= delta;

        // Sadece AI konuşmuyorken VE zamanı geldiyse göz kırp
        if (!this.three.isPlaying && this.three.lastBlinkTime < 0) {
            this.three.lastBlinkTime = 2.0 + Math.random() * 4.0; // 2-6 saniyede bir yeni zaman ata
            this.three.morphTargetInfluences[blinkIndex] = 1.0;
            this.three.morphTargetInfluences[blinkRightIndex] = 1.0;
        }
    }

    async _base64ToArrayBuffer(base64) {
        const res = await fetch(`data:audio/mpeg;base64,${base64}`);
        return res.arrayBuffer();
    }



_startContinuousRecognition() {
    if (this.recognitionActive || !this.isMicListeningMode) return; // Zaten çalışıyorsa veya mod kapalıysa çık

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognition) {
        this._addMessageToUI("Tarayıcınız ses tanımayı desteklemiyor.", "system");
        this.isMicListeningMode = false; // Modu geri kapat
        this._updateMicVisuals(false);
        return;
    }

    this.speechRecognition = new SpeechRecognition();
    // ... (lang, interimResults ayarları aynı) ...

    this.speechRecognition.onstart = () => {
        this.recognitionActive = true;
        console.log("Sürekli dinleme: Yeni döngü başladı.");
    };

    this.speechRecognition.onresult = (event) => {
        const speechResult = event.results[event.results.length - 1][0].transcript.trim();
        console.log("Konuşma algılandı (sürekli dinleme):", speechResult);
        
        // Barge-in: Eğer AI konuşuyorsa, onu sustur
        if (this.three.isPlaying) {
            this._stopAllAudio(true); // Kuyruğu temizleyerek sesi tamamen durdur
        }

        // Mesajı gönder
        this._sendMessageFromClone(null, speechResult);
    };

    this.speechRecognition.onend = () => {
        this.recognitionActive = false;
        console.log("Sürekli dinleme: Döngü bitti.");
        // Eğer kullanıcı modu manuel olarak kapatmadıysa, dinlemeye devam et.
        if (this.isMicListeningMode) {
            console.log("Yeniden başlatılıyor...");
            this._startContinuousRecognition();
        }
    };
    
    this.speechRecognition.onerror = (event) => {
        if (event.error !== 'no-speech') {
            console.error('Konuşma tanıma hatası:', event.error);
        }
        // 'no-speech' hatası normaldir, sadece dinlemeye devam ederiz.
    };

    this.speechRecognition.start();
}


    toggleWindow(forceOpen) {
        const shouldOpen = typeof forceOpen === 'boolean' ? forceOpen : !this.isWindowOpen;
        if (this.isWindowOpen === shouldOpen && !forceOpen && forceOpen !== undefined) return;

        this.isWindowOpen = shouldOpen;
        const isMobile = window.innerWidth <= 480;

        if (this.isWindowOpen) {
            // --- PENCEREYİ AÇMA ADIMLARI ---

            this.elements.chatWindow.classList.remove('my-chat-hidden');

            // Sadece mobilde launcher'ı GİZLE
            if (isMobile) {
                this.elements.launcher.style.display = 'none';
            }

            // Odaklanma ve diğer işlemler...
            this.elements.messageInput.focus();
            requestAnimationFrame(() => {
                if (this.elements.messageArea) this.elements.messageArea.scrollTop = this.elements.messageArea.scrollHeight;
            });

        } else {
            // --- PENCEREYİ KAPATMA ADIMLARI ---

            this.elements.chatWindow.classList.add('my-chat-hidden');

            // Her durumda launcher'ı GÖRÜNÜR yap
            // Not: CSS'te .my-chat-launcher'ın display: flex; olduğundan emin olun
            this.elements.launcher.style.display = 'flex';

            if (this.notificationInterval) {
                clearInterval(this.notificationInterval);
            }
        }
    }

    _sendMessage() {
        const messageText = this.elements.messageInput.value.trim();
        if (messageText === '' || !this.socket || !this.sessionId || !this.socket.connected) {
            if (this.socket && !this.socket.connected) this._addMessageToUI("Bağlantı yok, mesaj gönderilemedi.", "system");
            return;
        }
        this._addMessageToUI(messageText, 'user', new Date().toISOString());

        // SADECE TEK BİR ÇAĞRI OLMALI
        this.socket.emit('user_message_to_server', {
            session_id: this.sessionId,
            message: messageText,
            mode: 'text' // Yazı modu olduğunu belirt
        });

        this.elements.messageInput.value = '';
        this.elements.messageInput.style.height = 'auto';
        this.elements.messageInput.focus();
        this.elements.inputArea.classList.remove('has-text');
    }

    _formatTextToHtml(text, sender = 'ai') {
        if (!text && text !== "") return '';
        if (text === null || text === undefined) return '';
        let html = String(text);
        if (sender === 'user') {
            html = html.replace(/&/g, "&").replace(/</g, "<").replace(/>/g, ">");
            html = html.replace(/\n/g, '<br>');
            const urlRegex = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
            return html.replace(urlRegex, `<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>`);
        }
        const urlRegex = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
        html = html.replace(urlRegex, `<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>`);
        html = html.replace(/\*\*(.*?)\*\*/g, '<b>$1</b>').replace(/__(.*?)__/g, '<b>$1</b>');
        html = html.replace(/\*(.*?)\*/g, '<i>$1</i>').replace(/_(.*?)_/g, '<i>$1</i>');
        let listProcessedHtmlLines = []; let inList = false; const originalLines = html.split('\n');
        for (let i = 0; i < originalLines.length; i++) {
            let lineContent = originalLines[i]; const trimmedLine = lineContent.trim();
            const isLineJustALink = urlRegex.test(trimmedLine) && trimmedLine.match(urlRegex)[0].length === trimmedLine.length;
            if (isLineJustALink && !(trimmedLine.startsWith('- ') || trimmedLine.startsWith('* '))) {
                if (inList) { listProcessedHtmlLines.push('</ul>'); inList = false; }
                listProcessedHtmlLines.push(lineContent); continue;
            }
            if (trimmedLine.startsWith('- ') || trimmedLine.startsWith('* ')) {
                const listItemText = trimmedLine.substring(2).trim();
                if (!inList) { listProcessedHtmlLines.push('<ul>'); inList = true; }
                listProcessedHtmlLines.push(`<li>${listItemText}</li>`);
            } else {
                if (inList) { listProcessedHtmlLines.push('</ul>'); inList = false; }
                listProcessedHtmlLines.push(lineContent);
            }
        }
        if (inList) { listProcessedHtmlLines.push('</ul>'); } html = listProcessedHtmlLines.join('\n');
        html = html.replace(/\n/g, '<br>'); html = html.replace(/(<br\s*\/?>\s*){2,}/g, '<br>');
        html = html.replace(/^\s*<br\s*\/?>|<br\s*\/?>\s*$/g, ''); html = html.replace(/<li><br\s*\/?>\s*<\/li>/g, '<li> </li>');
        html = html.replace(/\[image:\s*(https?:\/\/[^\s\]]+)\s*\]/gi, '<img src="$1" alt="Sohbet görseli" class="my-chat-image">');
        return html;
    }

    /**
     * NİHAİ, TAM ve ANİMASYONLU FONKSİYON
     * Bir mesajı, tüm görsel bileşenleriyle birlikte sohbet arayüzüne ekler.
     * Yeni AI mesajları için, tarayıcının render döngüsüyle uyumlu,
     * güvenilir bir fade-in ve scale animasyonunu tetikler.
     *
     * @param {string} text - Gösterilecek ana metin içeriği.
     * @param {string} sender - Mesajı gönderen taraf ('ai' veya 'user').
     * @param {string} timestamp - Mesajın ISO formatındaki zaman damgası.
     * @param {object} messageData - Galeri gibi özel render verilerini içeren nesne.
     * @param {boolean} withAnimation - Eğer true ise, mesaja animasyon için CSS sınıfları eklenir.
     */
    _addMessageToUI(text, sender, timestamp, messageData = {}, withAnimation = false) {
        // Eğer mesajların ekleneceği ana alan (messageArea) bulunamazsa, hatayı önlemek için fonksiyondan çık.
        if (!this.elements.messageArea) return;

        // --- A. ELEMENTLERİ OLUŞTURMA ---
        
        // 1. En Dış Sarmalayıcı (Tüm satırı kapsar)
        const messageEntry = document.createElement('div');
        messageEntry.className = `my-chat-message-entry ${sender}`;

        // 2. Avatar ve Mesaj Baloncuğu için İç Sarmalayıcı
        const wrapper = document.createElement('div');
        wrapper.className = `my-chat-message-wrapper ${sender}`;

        // Gönderene göre avatarı oluştur ve ekle.
        if (sender === 'ai' && this.settings.aiAvatarUrl) {
            wrapper.appendChild(this._createAvatarElement(this.settings.aiAvatarUrl, 'AI Avatar'));
        } else if (sender === 'user' && this.settings.userAvatarUrl) {
            wrapper.appendChild(this._createAvatarElement(this.settings.userAvatarUrl, 'User Avatar'));
        }

        // 3. Mesaj Baloncuğunu Oluştur
        const messageElement = document.createElement('div');
        messageElement.classList.add('my-chat-message', `my-chat-message-${sender}`);

        // 4. Baloncuk İçeriğini Hazırla (Metin, Galeri vb.)
        const contentContainer = document.createElement('div');
        // İsmi genel tutmak, hem AI hem de kullanıcı mesajları için yeniden kullanılabilirliği artırır.
        contentContainer.className = 'content-container';

        // Metin varsa, HTML'e çevir ve içeriğe ekle.
        if (text && typeof text === 'string' && text.trim() !== '') {
            const textNode = document.createElement('div');
            textNode.innerHTML = this._formatTextToHtml(text, sender);
            contentContainer.appendChild(textNode);
        }

        // Özel veri (örneğin galeri) varsa, onu render et ve içeriğe ekle.
        if (sender === 'ai' && messageData && messageData.type === 'gallery' && messageData.items && messageData.items.length > 0) {
            this._renderImageGallery(contentContainer, messageData.items);
        } 
        // Eğer içerik tamamen boş kaldıysa (ne metin ne galeri), AI için "..." ekle.
        else if (sender === 'ai' && contentContainer.childNodes.length === 0) {
            contentContainer.innerHTML = this._formatTextToHtml("...", sender);
        }

        // --- B. PARÇALARI BİRLEŞTİRME ---

        messageElement.appendChild(contentContainer); // İçeriği balona ekle.
        wrapper.appendChild(messageElement);         // Balonu avatarın yanına ekle.
        messageEntry.appendChild(wrapper);           // Tüm satırı ana konteynere ekle.

        // Zaman Damgasını Ekle (Eğer ayarlar açıksa).
        if (this.settings.showTimestamps && timestamp) {
            const timeContainer = document.createElement('div');
            timeContainer.className = 'my-chat-timestamp-container';
            try {
                const date = new Date(timestamp);
                if (!isNaN(date)) {
                    let prefix = ""; // Önek metni
                    if (sender === 'ai' && this.settings.aiAvatarUrl) prefix = (this.settings.aiAgentName || "AI Agent") + " · ";
                    else if (sender === 'user' && this.settings.userAvatarUrl) prefix = "Siz · ";
                    timeContainer.textContent = prefix + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                }
            } catch (e) { /* Hatalı zaman damgası varsa görmezden gel. */ }
            messageEntry.appendChild(timeContainer);
        }

        // --- C. ANİMASYON ve DOM'a EKLEME (En Güvenilir Yöntem) ---

        // Animasyon istenip istenmediğini kontrol et.
        if (withAnimation) {
            // 1. Elementi DOM'a eklemeden ÖNCE, animasyonun başlangıç durumu sınıfını ata.
            // Bu, elementin "görünmez ve küçük" olarak DOM'a girmesini sağlar.
            messageEntry.classList.add('animate-in-start');
            
            // 2. Elementi sohbet alanına ekle.
            this.elements.messageArea.insertBefore(messageEntry, this.elements.typingIndicator);
            
            // 3. Tarayıcının bu ilk durumu işlemesi için bir sonraki "boyama" karesini bekle.
            // Bu, animasyonun atlanmasını engelleyen en kritik adımdır.
            requestAnimationFrame(() => {
                // 4. Şimdi, animasyonun bitiş durumu sınıfını ekle.
                // CSS'teki 'transition' özelliği, bu iki durum arasındaki geçişi otomatik olarak canlandıracaktır.
                messageEntry.classList.add('animate-in-end');
            });

        } else {
            // Eğer animasyon istenmiyorsa (örn: kullanıcı mesajları), elementi doğrudan ekle.
            this.elements.messageArea.insertBefore(messageEntry, this.elements.typingIndicator);
        }
        
        // --- D. SON DOKUNUŞLAR ---
        
        // Sohbeti her zaman en alta kaydır.
        requestAnimationFrame(() => {
            if (this.elements.messageArea) {
                this.elements.messageArea.scrollTop = this.elements.messageArea.scrollHeight;
            }
        });
    }

    _createAvatarElement(url, altText) {
        const avatarDiv = document.createElement('div'); avatarDiv.className = 'my-chat-message-avatar';
        const avatarImg = document.createElement('img'); avatarImg.src = url; avatarImg.alt = altText;
        avatarDiv.appendChild(avatarImg); return avatarDiv;
    }

    _renderImageGallery(container, images) {
        const galleryWrapper = document.createElement('div');
        galleryWrapper.className = 'my-chat-image-gallery';
        images.forEach(imageData => {
            if (imageData.url) {
                const figure = document.createElement('figure'); figure.className = 'my-chat-gallery-item';
                const img = document.createElement('img');
                img.src = imageData.url; img.alt = imageData.caption || "Sohbet görseli"; img.className = 'my-chat-image';
                img.addEventListener('click', () => this._showLightbox(imageData.url));
                figure.appendChild(img);
                if (imageData.caption && imageData.caption.trim() !== "") {
                    const figcaption = document.createElement('figcaption'); figcaption.textContent = imageData.caption;
                    figure.appendChild(figcaption);
                }
                galleryWrapper.appendChild(figure);
            }
        });
        if (galleryWrapper.hasChildNodes()) { container.appendChild(galleryWrapper); }
    }

    _showLightbox(imageUrl) {
        if (!this.elements.lightboxOverlay) return; // Eğer _createLightboxDOM çağrılmadıysa
        this.elements.lightboxImage.src = imageUrl;
        this.elements.lightboxOverlay.classList.remove('my-chat-hidden');
    }

    _hideLightbox() {
        if (!this.elements.lightboxOverlay) return;
        this.elements.lightboxOverlay.classList.add('my-chat-hidden');
        this.elements.lightboxImage.src = '';
    }

    _showTypingIndicator(show) {
        this.aiTyping = show;
        if (this.elements.typingIndicator) {
            const isHidden = this.elements.typingIndicator.classList.contains('my-chat-hidden');
            if (show && isHidden) { this.elements.typingIndicator.classList.remove('my-chat-hidden'); }
            else if (!show && !isHidden) { this.elements.typingIndicator.classList.add('my-chat-hidden'); }
            requestAnimationFrame(() => { if (this.elements.messageArea) this.elements.messageArea.scrollTop = this.elements.messageArea.scrollHeight; });
        }
    }

    _notifyNewMessage() {
        if (document.hidden && this.isWindowOpen) {
            if (this.notificationInterval) clearInterval(this.notificationInterval); let count = 0;
            this.notificationInterval = setInterval(() => { document.title = (count % 2 === 0) ? "💬 Yeni Mesaj!" : this.originalTitle; count++; }, 1200);
            const onFocus = () => { if (this.notificationInterval) clearInterval(this.notificationInterval); document.title = this.originalTitle; this.notificationInterval = null; window.removeEventListener('focus', onFocus); };
            window.addEventListener('focus', onFocus);
        } else if (!document.hidden && this.notificationInterval) { clearInterval(this.notificationInterval); document.title = this.originalTitle; this.notificationInterval = null; }
    }
}



// loader.js'in async defer ile yüklenmesinden kaynaklanabilecek zamanlama sorunlarına 
// karşı bir sigortadır ve projenizin çalışmasını sağlayan anahtar parçadır.
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeWidget);
} else {
    initializeWidget();
}

function initializeWidget() {
    const userSettings = window.myChatWidgetSettings || {};
    new MyChatWidget(userSettings);
    console.log("--- MyChatWidget: Tek Sefer Başlatıldı! ---");
}

