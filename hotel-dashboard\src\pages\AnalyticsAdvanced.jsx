import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  CircularProgress,
  Alert,
  Chip,
  Avatar,
  LinearProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Paper,
  Divider
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Schedule as ScheduleIcon,
  MonetizationOn as MoneyIcon,
  Chat as ChatIcon,
  Hotel as HotelIcon,
  Star as StarIcon,
  Facebook as FacebookIcon,
  Google as GoogleIcon,
  SmartToy as AIIcon,
  People as PeopleIcon,
  EventNote as ReservationIcon,
  Speed as SpeedIcon,
  Psychology as PsychologyIcon
} from '@mui/icons-material';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Composed<PERSON>hart
} from 'recharts';
import { useApi } from '../contexts/ApiContext';

const AnalyticsAdvanced = () => {
  const { getAIChatAnalytics, getDashboardTotals, currentHotelId } = useApi();
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [timeRange, setTimeRange] = useState('7d');

  // Demo data - Server'dan gelecek gerçek veriler
  const [chartData, setChartData] = useState({
    // Rezervasyon trendi (son 30 gün)
    reservationTrend: [
      { date: '01/12', reservations: 12, revenue: 24000, ai_initiated: 8 },
      { date: '02/12', reservations: 15, revenue: 30000, ai_initiated: 11 },
      { date: '03/12', reservations: 8, revenue: 16000, ai_initiated: 5 },
      { date: '04/12', reservations: 18, revenue: 36000, ai_initiated: 13 },
      { date: '05/12', reservations: 22, revenue: 44000, ai_initiated: 16 },
      { date: '06/12', reservations: 14, revenue: 28000, ai_initiated: 9 },
      { date: '07/12', reservations: 19, revenue: 38000, ai_initiated: 14 }
    ],
    
    // Chat performansı (saatlik)
    chatPerformance: [
      { hour: '09:00', chats: 12, success_rate: 85, avg_response: 2.1 },
      { hour: '10:00', chats: 18, success_rate: 88, avg_response: 2.3 },
      { hour: '11:00', chats: 24, success_rate: 92, avg_response: 1.9 },
      { hour: '12:00', chats: 15, success_rate: 78, avg_response: 2.8 },
      { hour: '13:00', chats: 8, success_rate: 82, avg_response: 2.5 },
      { hour: '14:00', chats: 22, success_rate: 89, avg_response: 2.2 },
      { hour: '15:00', chats: 28, success_rate: 94, avg_response: 1.8 },
      { hour: '16:00', chats: 19, success_rate: 87, avg_response: 2.4 }
    ],
    
    // Oda tipi performansı
    roomTypePerformance: [
      { name: 'Standart Oda', bookings: 45, revenue: 90000, avg_price: 2000 },
      { name: 'Deluxe Oda', bookings: 32, revenue: 96000, avg_price: 3000 },
      { name: 'Suite', bookings: 18, revenue: 90000, avg_price: 5000 },
      { name: 'Family Room', bookings: 12, revenue: 48000, avg_price: 4000 }
    ],
    
    // Müşteri kaynakları
    customerSources: [
      { name: 'AI Chat', value: 45, color: '#4caf50' },
      { name: 'Telefon', value: 25, color: '#2196f3' },
      { name: 'Email', value: 15, color: '#ff9800' },
      { name: 'Walk-in', value: 10, color: '#9c27b0' },
      { name: 'Diğer', value: 5, color: '#607d8b' }
    ],
    
    // Sentiment analizi trendi
    sentimentTrend: [
      { date: '01/12', positive: 82, neutral: 15, negative: 3 },
      { date: '02/12', positive: 85, neutral: 12, negative: 3 },
      { date: '03/12', positive: 78, neutral: 18, negative: 4 },
      { date: '04/12', positive: 88, neutral: 10, negative: 2 },
      { date: '05/12', positive: 91, neutral: 8, negative: 1 },
      { date: '06/12', positive: 86, neutral: 12, negative: 2 },
      { date: '07/12', positive: 89, neutral: 9, negative: 2 }
    ]
  });

  const fetchAnalytics = useCallback(async () => {
    try {
      setRefreshing(true);
      const [aiData, dashboardData] = await Promise.all([
        getAIChatAnalytics(currentHotelId),
        getDashboardTotals(currentHotelId)
      ]);
      
      setAnalytics({ ...aiData, ...dashboardData });
      setError(null);
    } catch (err) {
      console.error('Analytics fetch error:', err);
      setError('Analitik verileri yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [getAIChatAnalytics, getDashboardTotals, currentHotelId]);

  useEffect(() => {
    fetchAnalytics();
    
    // Her 5 dakikada bir otomatik güncelle
    const interval = setInterval(fetchAnalytics, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [fetchAnalytics]);

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0
    }).format(value);
  };

  const formatPercentage = (value) => {
    return `%${value.toFixed(1)}`;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={4}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box display="flex" alignItems="center">
            <Avatar sx={{ bgcolor: 'primary.main', mr: 2, width: 56, height: 56 }}>
              <AnalyticsIcon />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight="600">
                Gelişmiş Analitikler
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Otel performansı ve AI resepsiyon detaylı analizi
              </Typography>
            </Box>
          </Box>
          
          <Box display="flex" gap={2}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Zaman Aralığı</InputLabel>
              <Select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
              >
                <MenuItem value="24h">Son 24 Saat</MenuItem>
                <MenuItem value="7d">Son 7 Gün</MenuItem>
                <MenuItem value="30d">Son 30 Gün</MenuItem>
                <MenuItem value="90d">Son 3 Ay</MenuItem>
              </Select>
            </FormControl>
            
            <Button
              variant="outlined"
              startIcon={refreshing ? <CircularProgress size={20} /> : <RefreshIcon />}
              onClick={fetchAnalytics}
              disabled={refreshing}
            >
              {refreshing ? 'Güncelleniyor...' : 'Yenile'}
            </Button>
          </Box>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* KPI Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', p: 3 }}>
              <ReservationIcon sx={{ fontSize: 40, mb: 2, color: 'primary.main' }} />
              <Typography variant="h4" fontWeight="700" color="primary.main">
                {analytics?.total_confirmed_reservations || 0}
              </Typography>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 1 }}>
                Toplam Rezervasyon
              </Typography>
              <Chip 
                label="+12% (bu ay)" 
                color="success" 
                size="small"
                icon={<TrendingUpIcon />}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', p: 3 }}>
              <MoneyIcon sx={{ fontSize: 40, mb: 2, color: 'success.main' }} />
              <Typography variant="h4" fontWeight="700" color="success.main">
                {formatCurrency(analytics?.today_revenue || 0)}
              </Typography>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 1 }}>
                Toplam Gelir
              </Typography>
              <Chip 
                label="+8% (bu ay)" 
                color="success" 
                size="small"
                icon={<TrendingUpIcon />}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', p: 3 }}>
              <AIIcon sx={{ fontSize: 40, mb: 2, color: 'warning.main' }} />
              <Typography variant="h4" fontWeight="700" color="warning.main">
                {formatPercentage(analytics?.ai_conversion_rate || 0)}
              </Typography>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 1 }}>
                AI Dönüşüm Oranı
              </Typography>
              <Chip 
                label="+3% (bu hafta)" 
                color="success" 
                size="small"
                icon={<TrendingUpIcon />}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', p: 3 }}>
              <SpeedIcon sx={{ fontSize: 40, mb: 2, color: 'info.main' }} />
              <Typography variant="h4" fontWeight="700" color="info.main">
                {analytics?.avg_response_time_seconds || 0}s
              </Typography>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 1 }}>
                Ortalama Yanıt Süresi
              </Typography>
              <Chip 
                label="-0.3s (bu hafta)" 
                color="success" 
                size="small"
                icon={<TrendingDownIcon />}
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tab Navigation */}
      <Paper sx={{ mb: 3 }}>
        <Tabs 
          value={tabValue} 
          onChange={(e, newValue) => setTabValue(newValue)}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label="📈 Rezervasyon Analizi" />
          <Tab label="💬 Chat Performansı" />
          <Tab label="🏨 Oda Performansı" />
          <Tab label="😊 Müşteri Analizi" />
          <Tab label="🌐 Sosyal Medya" />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      {tabValue === 0 && (
        <Grid container spacing={3}>
          {/* Rezervasyon Trendi */}
          <Grid item xs={12} lg={8}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  📈 Rezervasyon ve Gelir Trendi
                </Typography>
                <ResponsiveContainer width="100%" height={400}>
                  <ComposedChart data={chartData.reservationTrend}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip
                      formatter={(value, name) => [
                        name === 'revenue' ? formatCurrency(value) : value,
                        name === 'reservations' ? 'Rezervasyon' :
                        name === 'revenue' ? 'Gelir' : 'AI Rezervasyon'
                      ]}
                    />
                    <Legend />
                    <Bar yAxisId="left" dataKey="reservations" fill="#2196f3" name="Rezervasyon" />
                    <Bar yAxisId="left" dataKey="ai_initiated" fill="#4caf50" name="AI Rezervasyon" />
                    <Line yAxisId="right" type="monotone" dataKey="revenue" stroke="#ff9800" strokeWidth={3} name="Gelir" />
                  </ComposedChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Rezervasyon Kaynakları */}
          <Grid item xs={12} lg={4}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  🎯 Rezervasyon Kaynakları
                </Typography>
                <ResponsiveContainer width="100%" height={400}>
                  <PieChart>
                    <Pie
                      data={chartData.customerSources}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={120}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {chartData.customerSources.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Rezervasyon Özeti */}
          <Grid item xs={12}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  📊 Rezervasyon Performans Özeti
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={3}>
                    <Box textAlign="center" p={2} sx={{ backgroundColor: 'rgba(33, 150, 243, 0.1)', borderRadius: 2 }}>
                      <Typography variant="h4" fontWeight="700" color="primary">
                        {analytics?.total_ai_initiated_reservations || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        AI'dan Gelen Rezervasyon
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={75}
                        sx={{ mt: 1, height: 6, borderRadius: 3 }}
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Box textAlign="center" p={2} sx={{ backgroundColor: 'rgba(255, 152, 0, 0.1)', borderRadius: 2 }}>
                      <Typography variant="h4" fontWeight="700" color="warning.main">
                        {analytics?.pending_reservations || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Onay Bekleyen
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={25}
                        color="warning"
                        sx={{ mt: 1, height: 6, borderRadius: 3 }}
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Box textAlign="center" p={2} sx={{ backgroundColor: 'rgba(76, 175, 80, 0.1)', borderRadius: 2 }}>
                      <Typography variant="h4" fontWeight="700" color="success.main">
                        {formatPercentage(85.5)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Onay Oranı
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={85.5}
                        color="success"
                        sx={{ mt: 1, height: 6, borderRadius: 3 }}
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Box textAlign="center" p={2} sx={{ backgroundColor: 'rgba(156, 39, 176, 0.1)', borderRadius: 2 }}>
                      <Typography variant="h4" fontWeight="700" color="secondary.main">
                        {formatCurrency(2450)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Ortalama Rezervasyon Değeri
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={65}
                        color="secondary"
                        sx={{ mt: 1, height: 6, borderRadius: 3 }}
                      />
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {tabValue === 1 && (
        <Grid container spacing={3}>
          {/* Chat Performansı Saatlik */}
          <Grid item xs={12} lg={8}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  💬 Saatlik Chat Performansı
                </Typography>
                <ResponsiveContainer width="100%" height={400}>
                  <ComposedChart data={chartData.chatPerformance}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="hour" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip
                      formatter={(value, name) => [
                        name === 'success_rate' ? `%${value}` :
                        name === 'avg_response' ? `${value}s` : value,
                        name === 'chats' ? 'Chat Sayısı' :
                        name === 'success_rate' ? 'Başarı Oranı' : 'Ortalama Yanıt'
                      ]}
                    />
                    <Legend />
                    <Bar yAxisId="left" dataKey="chats" fill="#2196f3" name="Chat Sayısı" />
                    <Line yAxisId="right" type="monotone" dataKey="success_rate" stroke="#4caf50" strokeWidth={3} name="Başarı Oranı (%)" />
                    <Line yAxisId="right" type="monotone" dataKey="avg_response" stroke="#ff9800" strokeWidth={2} name="Yanıt Süresi (s)" />
                  </ComposedChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Chat Özet Metrikleri */}
          <Grid item xs={12} lg={4}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  📊 Chat Özet Metrikleri
                </Typography>

                <Box mb={3}>
                  <Typography variant="body2" color="text.secondary" mb={1}>
                    Toplam Chat Oturumu
                  </Typography>
                  <Typography variant="h4" fontWeight="700" color="primary">
                    {analytics?.total_chat_sessions_month || 0}
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={78}
                    sx={{ mt: 1, height: 6, borderRadius: 3 }}
                  />
                </Box>

                <Divider sx={{ my: 2 }} />

                <Box mb={3}>
                  <Typography variant="body2" color="text.secondary" mb={1}>
                    AI Başarı Oranı
                  </Typography>
                  <Typography variant="h4" fontWeight="700" color="success.main">
                    {formatPercentage(analytics?.ai_success_rate || 0)}
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={analytics?.ai_success_rate || 0}
                    color="success"
                    sx={{ mt: 1, height: 6, borderRadius: 3 }}
                  />
                </Box>

                <Divider sx={{ my: 2 }} />

                <Box>
                  <Typography variant="body2" color="text.secondary" mb={1}>
                    Konuşma Tamamlama Oranı
                  </Typography>
                  <Typography variant="h4" fontWeight="700" color="warning.main">
                    {formatPercentage(analytics?.conversation_completion_rate || 0)}
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={analytics?.conversation_completion_rate || 0}
                    color="warning"
                    sx={{ mt: 1, height: 6, borderRadius: 3 }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {tabValue === 2 && (
        <Grid container spacing={3}>
          {/* Oda Tipi Performansı */}
          <Grid item xs={12}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  🏨 Oda Tipi Performans Analizi
                </Typography>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={chartData.roomTypePerformance}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip
                      formatter={(value, name) => [
                        name === 'revenue' || name === 'avg_price' ? formatCurrency(value) : value,
                        name === 'bookings' ? 'Rezervasyon' :
                        name === 'revenue' ? 'Gelir' : 'Ortalama Fiyat'
                      ]}
                    />
                    <Legend />
                    <Bar yAxisId="left" dataKey="bookings" fill="#2196f3" name="Rezervasyon Sayısı" />
                    <Bar yAxisId="right" dataKey="revenue" fill="#4caf50" name="Toplam Gelir" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {tabValue === 3 && (
        <Grid container spacing={3}>
          {/* Müşteri Memnuniyet Trendi */}
          <Grid item xs={12}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  😊 Müşteri Memnuniyet Trendi
                </Typography>
                <ResponsiveContainer width="100%" height={400}>
                  <AreaChart data={chartData.sentimentTrend}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`%${value}`, '']} />
                    <Legend />
                    <Area type="monotone" dataKey="positive" stackId="1" stroke="#4caf50" fill="#4caf50" name="Pozitif" />
                    <Area type="monotone" dataKey="neutral" stackId="1" stroke="#ff9800" fill="#ff9800" name="Nötr" />
                    <Area type="monotone" dataKey="negative" stackId="1" stroke="#f44336" fill="#f44336" name="Negatif" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {tabValue === 4 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 2 }}>
                  🌐 Sosyal Medya Analizi
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Google Reviews ve Facebook entegrasyonu ile sosyal medya performansı
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Box p={3} sx={{ backgroundColor: 'rgba(66, 165, 245, 0.1)', borderRadius: 2 }}>
                      <GoogleIcon sx={{ fontSize: 48, color: '#4285f4', mb: 2 }} />
                      <Typography variant="h5" fontWeight="600">
                        Google Reviews
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Yakında aktif olacak
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box p={3} sx={{ backgroundColor: 'rgba(66, 103, 178, 0.1)', borderRadius: 2 }}>
                      <FacebookIcon sx={{ fontSize: 48, color: '#1877f2', mb: 2 }} />
                      <Typography variant="h5" fontWeight="600">
                        Facebook Analytics
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Yakında aktif olacak
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default AnalyticsAdvanced;
