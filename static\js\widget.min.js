import*as THREE from"three";import{GLTFLoader}from"three/addons/GLTFLoader.js";import{FBXLoader}from"three/addons/FBXLoader.js";class MyChatWidget{constructor(e){console.log("--- MyChatWidget: constructor BAŞLADI ---");this.settings=this._mergeDeep({hotelIdentifier:null,backendUrl:"http://127.0.0.1:5000",theme:{primaryColor:"#007AFF",headerTextColor:"#FFFFFF",widgetBackgroundColor:"#F7F7F7",textColor:"#1c1c1e",borderRadius:"18px",bubbleAiBgGradientStart:"#EFEFF4",bubbleAiBgGradientEnd:"#E9E9EB",bubbleAiText:"#1c1c1e",bubbleUserBg:"#007AFF",bubbleUserText:"#FFFFFF",inputBg:"#FFFFFF",inputBorderColor:"#E0E0E0",fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Cantarell, "Helvetica Neue", sans-serif',avatarSize:"32px",headerHeight:"50px"},welcomeScreen:{enabled:!0,logoUrl:"https://cdn-icons-png.flaticon.com/512/732/732085.png",title:"Canlı Desteğe Hoş Geldiniz!",message:"Size nasıl yardımcı olabiliriz? Lütfen bir devam etme şekli seçin.",videoButtonText:"Görüntülü Konuşma (Avatar)",textButtonText:"Yazışarak Devam Et"},headerText:"LiveChatAI",headerLogoUrl:"https://cdn-icons-png.flaticon.com/512/732/732085.png",aiAvatarUrl:"https://cdn-icons-png.flaticon.com/512/4712/4712027.png",userAvatarUrl:"",initialLauncherIcon:"💬",launcherOpenIcon:"💬",launcherCloseIcon:"✕",footerText:"Powered by LiveChatAI",showTimestamps:!0,micButtonEnabled:!0,attachButtonEnabled:!0,aiAgentName:"AI Agent",speechLang:"tr-TR"},e||{});const t=`${this.settings.backendUrl}/static/sounds/blob.mp3`;this.audio={notification:new Audio(t)},console.log(`INFO: Bildirim sesi şu adresten yüklendi: ${t}`),this.socket=null,this.sessionId=null,this.elements={},this.isWindowOpen=!1,this.aiTyping=!1,this.originalTitle=document.title,this.notificationInterval=null,this.speechRecognition=null,this.isRecognizingSpeech=!1,this.isAudioEnabled=!0,this.isMicListeningMode=!1,this.recognitionActive=!1,this.isRecording=!1,this.mediaRecorder=null,this.audioChunks=[],this.three={scene:null,camera:null,renderer:null,avatar:null,mixer:null,clock:new THREE.Clock,morphTargetDictionary:null,morphTargetInfluences:null,audioContext:null,audioSource:null,isPlaying:!1,visemeQueue:[],audioStartTime:0,currentViseme:"viseme_sil",lastBlinkTime:0,audioQueue:[],isAudioQueuePlaying:!1,analyser:null,micStream:null,micAnalyser:null,micGainNode:null,isBargeInDetected:!1,animationActions:{},activeAction:null,lastAction:null,activeExpressions:{},expressionClock:new THREE.Clock,headBone:null,neckBone:null,spineBone:null},this.animationParams={lerpFactor:.13,latencyOffset:.05,vowelDuration:.08,consonantDuration:.09,lipCloseDuration:.07},this.visemeWeights={viseme_sil:0,viseme_PP:3,viseme_FF:2.5,viseme_DD:.6,viseme_kk:.5,viseme_CH:.7,viseme_SS:.6,viseme_nn:.5,viseme_RR:.8,viseme_I:1.5,viseme_aa:1,viseme_O:1.5,viseme_U:2.5},this.phonemeToVisemeMap={p:"viseme_PP",b:"viseme_PP",m:"viseme_PP",f:"viseme_FF",v:"viseme_FF",t:"viseme_DD",d:"viseme_DD",k:"viseme_kk",g:"viseme_kk","ç":"viseme_CH",c:"viseme_CH","ş":"viseme_CH",j:"viseme_CH",s:"viseme_SS",z:"viseme_SS",n:"viseme_nn",r:"viseme_RR",l:"viseme_RR",y:"viseme_I",a:"viseme_aa",e:"viseme_aa","ı":"viseme_I",i:"viseme_I",o:"viseme_O",u:"viseme_O","ö":"viseme_U","ü":"viseme_U"},this.expressionRecipes={smile:[{name:"mouthSmileLeft",start:.1,peak:.4,end:2.5,targetValue:.8},{name:"mouthSmileRight",start:.1,peak:.4,end:2.5,targetValue:.8},{name:"eyeSquintLeft",start:.2,peak:.6,end:2.5,targetValue:.5},{name:"eyeSquintRight",start:.2,peak:.6,end:2.5,targetValue:.5}],sad:[{name:"mouthFrownLeft",start:.2,peak:.5,end:3,targetValue:.6},{name:"mouthFrownRight",start:.2,peak:.5,end:3,targetValue:.6},{name:"browInnerUp",start:.1,peak:.4,end:3,targetValue:.9}],confused:[{name:"browInnerUp",start:.1,peak:.4,end:2.5,targetValue:.5},{name:"browDownLeft",start:.15,peak:.5,end:2.5,targetValue:.4}],surprised:[{name:"eyeWideLeft",start:0,peak:.2,end:2,targetValue:.7},{name:"eyeWideRight",start:0,peak:.2,end:2,targetValue:.7},{name:"browInnerUp",start:0,peak:.2,end:2,targetValue:1},{name:"mouthOpen",start:.05,peak:.25,end:2,targetValue:.3}],greeting:[{name:"mouthSmile",start:.1,peak:.4,end:4,targetValue:.6},{name:"browInnerUp",start:0,peak:.3,end:1.5,targetValue:.5}]},this._createDOM(),this._addEventListeners(),console.log("--- MyChatWidget: constructor BİTTİ ---")}_mergeDeep(e,t){const i={...e};return this._isObject(e)&&this._isObject(t)&&Object.keys(t).forEach(s=>{this._isObject(t[s])?s in e?i[s]=this._mergeDeep(e[s],t[s]):Object.assign(i,{[s]:t[s]}):Object.assign(i,{[s]:t[s]})}),i}_isObject(e){return e&&"object"==typeof e&&!Array.isArray(e)}_applyTheme(){const e=document.documentElement,t=this.settings.theme;Object.keys(t).forEach(i=>{const s=`--widget-${i.replace(/([A-Z])/g,"-$1").toLowerCase()}`;e.style.setProperty(s,t[i])})}_createDOM(){console.log("--- MyChatWidget: _createDOM BAŞLADI ---"),this.elements.widgetContainer=document.createElement("div"),this.elements.widgetContainer.className="my-chat-widget-container",console.log("widgetContainer oluşturuldu:",this.elements.widgetContainer),this.elements.launcher=document.createElement("button"),this.elements.launcher.className="my-chat-launcher",this.elements.launcher.innerHTML=this.settings.initialLauncherIcon||"💬",this.elements.widgetContainer.appendChild(this.elements.launcher),this.elements.chatWindow=document.createElement("div"),this.elements.chatWindow.className="my-chat-window my-chat-hidden",this.elements.widgetContainer.appendChild(this.elements.chatWindow),this._createWelcomeScreen(),this._createMainChatContainer(),this._createAvatarSceneContainer(),this._createLightboxDOM(),this.elements.widgetContainer.appendChild(this.elements.launcher),this.elements.widgetContainer.appendChild(this.elements.chatWindow),document.body.appendChild(this.elements.widgetContainer),console.log("!!! widgetContainer, document.body'e eklendi. !!!"),this.elements.mainChatContainer.style.display="none",this.elements.avatarSceneContainer.style.display="none",this.settings.welcomeScreen.enabled||this._startTextChat(),console.log("--- MyChatWidget: _createDOM BİTTİ ---")}_createWelcomeScreen(){this.elements.welcomeScreen=document.createElement("div"),this.elements.welcomeScreen.className="my-chat-welcome-screen";const{logoUrl:e,title:t,message:i,videoButtonText:s,textButtonText:n}=this.settings.welcomeScreen;this.elements.welcomeScreen.innerHTML=`\n            <img src="${e}" alt="Welcome Logo" class="my-chat-welcome-logo">\n            <h2 class="my-chat-welcome-title">${t}</h2>\n            <p class="my-chat-welcome-message">${i}</p>\n            <div class="my-chat-welcome-options">\n                <button class="my-chat-welcome-button primary" id="my-chat-start-video-btn">${s}</button>\n                <button class="my-chat-welcome-button secondary" id="my-chat-start-text-btn">${n}</button>\n            </div>\n        `,this.elements.chatWindow.appendChild(this.elements.welcomeScreen)}_createMainChatContainer(){this.elements.mainChatContainer=document.createElement("div"),this.elements.mainChatContainer.className="my-chat-main-container",this._createHeader(),this._createMessageArea(),this._createInputArea(),this._createFooter(),this.elements.mainChatContainer.appendChild(this.elements.header),this.elements.mainChatContainer.appendChild(this.elements.messageArea),this.elements.mainChatContainer.appendChild(this.elements.inputArea),this.elements.footer&&this.elements.mainChatContainer.appendChild(this.elements.footer),this.elements.chatWindow.appendChild(this.elements.mainChatContainer)}_createAvatarSceneContainer(){console.log("--- _createAvatarSceneContainer: Avatar sahnesi oluşturuluyor ---"),this.elements.avatarSceneContainer=document.createElement("div"),this.elements.avatarSceneContainer.className="my-chat-avatar-scene-container";const e=document.createElement("div");e.className="my-chat-avatar-canvas-wrapper",e.innerHTML='<canvas id="my-chat-avatar-canvas"></canvas>';const t=this.elements.header.cloneNode(!0);console.log("Mevcut this.elements.inputArea klonlanıyor:",this.elements.inputArea);const i=this.elements.inputArea.cloneNode(!0);console.log("Klonlanmış inputAreaClone oluşturuldu:",i);const s=i.querySelector(".mic-button");console.log("Klonun içinden .mic-button aranıyor. Bulunan element:",s),s?(s.style.display="none",console.log("%c✅ Klonlanmış mikrofon butonu GİZLENDİ.","color: green; font-weight: bold;")):console.error("%c❌ HATA: Klonlanmış input alanı içinde .mic-button sınıfına sahip bir element BULUNAMADI!","color: red; font-weight: bold;"),this.elements.cloneHeader=t,this.elements.cloneInputArea=i,this.elements.cloneMessageInput=i.querySelector("textarea"),this.elements.cloneSendButton=i.querySelector(".send-button-icon"),this.elements.pushToTalkButton=null;const n=document.createElement("div");n.className="my-chat-ptt-wrapper",this.elements.pushToTalkButton=document.createElement("button"),this.elements.pushToTalkButton.className="my-chat-ptt-button",this.elements.pushToTalkButton.innerHTML='\n            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">\n                \x3c!--!Font Awesome Free 6.5.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2024 Fonticons, Inc.--\x3e\n                <path d="M192 0C139 0 96 43 96 96V256c0 53 43 96 96 96s96-43 96-96V96c0-53-43-96-96-96zM64 216c0-13.3-10.7-24-24-24s-24 10.7-24 24v40c0 89.1 66.2 162.7 152 174.4V464H120c-13.3 0-24 10.7-24 24s10.7 24 24 24h144c13.3 0 24-10.7 24-24s-10.7-24-24-24H192V430.4c85.8-11.7 152-85.3 152-174.4V216c0-13.3-10.7-24-24-24s-24 10.7-24 24v40c0 70.7-57.3 128-128 128s-128-57.3-128-128V216z"/>\n            </svg>\n        ',n.appendChild(this.elements.pushToTalkButton);const a=document.createElement("div");a.className="my-chat-loader-overlay",a.innerHTML='<div class="my-chat-spinner"></div>',e.appendChild(a),this.elements.avatarLoader=a,this.elements.avatarSceneContainer.appendChild(t),this.elements.avatarSceneContainer.appendChild(e),this.elements.avatarSceneContainer.appendChild(n),this.elements.avatarSceneContainer.appendChild(i),this.elements.chatWindow.appendChild(this.elements.avatarSceneContainer);t.querySelectorAll(".my-chat-header-button").forEach(e=>{const t=e.getAttribute("aria-label");"Sohbeti Kapat"===t?e.addEventListener("click",()=>this.toggleWindow(!1)):"Geri"===t?e.addEventListener("click",()=>this._showWelcomeScreen()):"Sesi Aç/Kapat"===t&&e.addEventListener("click",()=>this._toggleAudio())}),this.elements.cloneSendButton&&this.elements.cloneSendButton.addEventListener("click",()=>this._sendMessageFromClone()),this.elements.cloneMessageInput&&this.elements.cloneMessageInput.addEventListener("keypress",e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),this._sendMessageFromClone())}),this.elements.pushToTalkButton&&(this.elements.pushToTalkButton.addEventListener("mousedown",e=>this._handleMicPress(e)),this.elements.pushToTalkButton.addEventListener("touchstart",e=>this._handleMicPress(e,!0)),window.addEventListener("mouseup",()=>this._handleMicRelease()),window.addEventListener("touchend",()=>this._handleMicRelease()))}_handleMicPress(e,t=!1){if(t&&e.preventDefault(),this.isRecognizingSpeech)return;const i=window.SpeechRecognition||window.webkitSpeechRecognition;i?(this.three.isPlaying&&this._stopAllAudio(),this.speechRecognition=new i,this.speechRecognition.lang=this.settings.speechLang||"tr-TR",this.speechRecognition.interimResults=!1,this.speechRecognition.maxAlternatives=1,this.speechRecognition.onstart=()=>{console.log("Konuşma tanıma başladı..."),this.isRecognizingSpeech=!0,this._startRecordingAnimation()},this.speechRecognition.onresult=e=>{const t=e.results[e.results.length-1][0].transcript.trim();console.log("Konuşma tanındı:",t),t&&(this._addMessageToUI(t,"user",(new Date).toISOString()),this.socket.emit("user_message_to_server",{session_id:this.sessionId,message:t,mode:"avatar"}))},this.speechRecognition.onend=()=>{console.log("Konuşma tanıma sona erdi."),this.isRecognizingSpeech=!1,this._stopRecordingAnimation()},this.speechRecognition.onerror=e=>{console.error("Speech Recognition Hatası:",e.error),this._addMessageToUI(`[Mikrofon Hatası: ${e.error}]`,"system")},this.speechRecognition.start()):this._addMessageToUI("Tarayıcınız ses tanımayı desteklemiyor.","system")}_handleMicRelease(){this.isRecognizingSpeech&&this.speechRecognition&&(console.log("Mikrofon bırakıldı, tanıma durduruluyor..."),this.speechRecognition.stop())}_sendAudioToServer(e){console.log(`Kaydedilen ses gönderiliyor... Boyut: ${e.size} bytes`),this._addMessageToUI("[Sesli mesajınız gönderiliyor...]","user",(new Date).toISOString());const t=new FormData;t.append("audio_data",e,"user_voice_message.webm"),t.append("hotel_id_key",this.settings.hotelIdentifier),t.append("session_id",this.sessionId),t.append("sample_rate",this.three.audioContext?.sampleRate||48e3),fetch(`${this.settings.backendUrl}/voice_command`,{method:"POST",body:t}).then(e=>{if(!e.ok)throw new Error(`Sunucu Hatası: ${e.statusText}`);return e.json()}).then(e=>{if(e.success&&e.audio_content_b64){const t={audio_content_b64:e.audio_content_b64,word_timings:e.visemes||[]};this.three.audioQueue.push(t),this.three.isAudioQueuePlaying||this._playNextInAudioQueue()}else console.error("Sesli komut yanıtında hata:",e.error),this._addMessageToUI(`[Hata: ${e.error||"Yanıt alınamadı"}]`,"system")}).catch(e=>{console.error("Sesli komut gönderiminde network hatası:",e),this._addMessageToUI("[Gönderim hatası: Sunucuya ulaşılamadı.]","system")})}_startRecordingAnimation(){this.three.isPlaying&&this._stopAllAudio();const e=this.elements.pushToTalkButton,t=this.elements.chatWindow;if(!e||!t)return;const i=document.createElement("div");i.className="mic-ripple",e.appendChild(i),e.classList.add("is-recording"),t.classList.add("is-listening")}_stopRecordingAnimation(){const e=this.elements.pushToTalkButton,t=this.elements.chatWindow;if(!e||!t)return;e.classList.remove("is-recording"),t.classList.remove("is-listening");const i=e.querySelector(".mic-ripple");i&&i.remove()}_handlePttStart(e){e.preventDefault(),this.isRecording||(this.isRecording=!0,this.elements.pushToTalkButton.classList.add("is-recording"),this.elements.pushToTalkButton.innerHTML="Dinleniyor...",this.three.isPlaying&&this._stopAllAudio(),this._startRecording())}_handlePttEnd(){this.isRecording&&(this.isRecording=!1,this.elements.pushToTalkButton.classList.remove("is-recording"),this.elements.pushToTalkButton.innerHTML="🎤",this._stopRecording())}async _startRecording(){try{const e=await navigator.mediaDevices.getUserMedia({audio:!0});this.three.isPlaying&&this._stopAllAudio(),this.mediaRecorder=new MediaRecorder(e,{mimeType:"audio/webm"}),this.audioChunks=[],this.mediaRecorder.ondataavailable=e=>{this.audioChunks.push(e.data)},this.mediaRecorder.start()}catch(e){console.error("Mikrofon kaydı başlatılamadı:",e),this._addMessageToUI("[Mikrofon hatası: Lütfen tarayıcı izinlerini kontrol edin.]","system"),this.isRecording=!1,this._stopRecordingAnimation()}}_stopRecording(){this.mediaRecorder&&"inactive"!==this.mediaRecorder.state&&(this.mediaRecorder.onstop=async()=>{const e=new Blob(this.audioChunks,{type:"audio/webm"});if(e.size<1e3)console.log("Kayıt çok kısa, gönderilmedi.");else{console.log(`WebM ses dosyası oluşturuldu. Boyut: ${e.size} bytes`),this._addMessageToUI("[Sesli mesajınız işleniyor...]","user",(new Date).toISOString());try{const t=await this._convertWebMToPCM(e);console.log(`Ham PCM verisi oluşturuldu. Boyut: ${t.byteLength} bytes`);const i=new Blob([t],{type:"audio/l16"}),s=new FormData;s.append("audio_data",i,"user_voice_message.raw"),s.append("hotel_id_key",this.settings.hotelIdentifier),s.append("session_id",this.sessionId),s.append("sample_rate",this.three.audioContext.sampleRate),fetch(`${this.settings.backendUrl}/voice_command`,{method:"POST",body:s}).then(e=>e.json()).then(e=>{if(e.success&&e.audio_content_b64){const t={audio_content_b64:e.audio_content_b64,word_timings:e.visemes};this.three.audioQueue.push(t),this.three.isAudioQueuePlaying||this._playNextInAudioQueue()}else console.error("Sesli komut yanıtında hata:",e.error),this._addMessageToUI(`[Hata: ${e.error||"Yanıt alınamadı"}]`,"system")}).catch(e=>{console.error("Sesli komut gönderiminde network hatası:",e),this._addMessageToUI("[Gönderim hatası: Sunucuya ulaşılamadı.]","system")})}catch(e){console.error("Sesi PCM'e çevirirken hata oluştu:",e),this._addMessageToUI("[Ses işleme hatası.]","system")}this.mediaRecorder&&this.mediaRecorder.stream&&this.mediaRecorder.stream.getTracks().forEach(e=>e.stop()),this.mediaRecorder=null}},this.mediaRecorder.stop())}async _convertWebMToPCM(e){this.three.audioContext||(this.three.audioContext=new(window.AudioContext||window.webkitAudioContext));const t=await e.arrayBuffer(),i=await this.three.audioContext.decodeAudioData(t),s=new OfflineAudioContext(i.numberOfChannels,16e3*i.duration,16e3),n=s.createBufferSource();n.buffer=i,n.connect(s.destination),n.start();const a=(await s.startRendering()).getChannelData(0),o=new Int16Array(a.length);for(let e=0;e<a.length;e++)o[e]=32767*Math.max(-1,Math.min(1,a[e]));return o.buffer}_sendMessageFromClone(){const e=this.elements.cloneMessageInput.value.trim();""!==e&&(this._addMessageToUI(e,"user",(new Date).toISOString()),this.socket.emit("user_message_to_server",{session_id:this.sessionId,message:e,mode:"avatar"}),this.elements.cloneMessageInput.value="",this.elements.cloneMessageInput.style.height="auto")}_createHeader(){this.elements.header=document.createElement("div"),this.elements.header.className="my-chat-header";const e=document.createElement("div");e.className="my-chat-header-left-content",this.settings.headerLogoUrl&&(this.elements.headerLogo=document.createElement("img"),this.elements.headerLogo.className="my-chat-header-logo",this.elements.headerLogo.src=this.settings.headerLogoUrl,this.elements.headerLogo.alt="Logo",e.appendChild(this.elements.headerLogo)),this.elements.headerTitleText=document.createElement("span"),this.elements.headerTitleText.className="my-chat-header-title-text",this.elements.headerTitleText.textContent=this.settings.headerText,e.appendChild(this.elements.headerTitleText);const t=document.createElement("div");t.className="my-chat-header-right",this.elements.speakerButton=this._createHeaderButton("🔊","Sesi Aç/Kapat"),this.elements.backButton=this._createHeaderButton("‹","Geri"),this.elements.closeButton=this._createHeaderButton("✕","Sohbeti Kapat"),t.appendChild(this.elements.speakerButton),t.appendChild(this.elements.backButton),t.appendChild(this.elements.closeButton),this.elements.header.appendChild(e),this.elements.header.appendChild(t)}_toggleAudio(){this.isAudioEnabled=!this.isAudioEnabled;const e=this.isAudioEnabled?"🔊":"🔇";this.elements.widgetContainer.querySelectorAll('.my-chat-header-button[aria-label="Sesi Aç/Kapat"]').forEach(t=>t.innerHTML=e),console.log("Ses modu: "+(this.isAudioEnabled?"AÇIK":"KAPALI")),this.three.audioSource&&this.three.audioSource.gain&&(this.three.audioSource.gain.gain.value=this.isAudioEnabled?1:0)}_stopAllAudio(){this.three.audioSource&&(this.three.audioSource.onended=null,this.three.audioSource.stop(0)),this.three.audioSource=null,this.three.isPlaying=!1,this.three.isAudioQueuePlaying=!1,this.three.audioQueue=[],this.three.visemeQueue=[],this.three.currentViseme="viseme_sil"}_showWelcomeScreen(){this.elements.mainChatContainer.style.display="none",this.elements.avatarSceneContainer.style.display="none",this.elements.welcomeScreen.style.display="flex",this.elements.welcomeScreen.classList.remove("my-chat-hidden"),this.elements.backButton.style.display="none",this.elements.speakerButton.style.display="none",this.elements.closeButton.style.display="flex",this._stopAllAudio()}_createHeaderButton(e,t){const i=document.createElement("button");return i.className="my-chat-header-button",i.innerHTML=e,i.setAttribute("aria-label",t),i}_createMessageArea(){this.elements.messageArea=document.createElement("div"),this.elements.messageArea.className="my-chat-message-area",this.elements.typingIndicator=document.createElement("div"),this.elements.typingIndicator.className="my-chat-typing-indicator my-chat-hidden",this.elements.typingIndicator=document.createElement("div"),this.elements.typingIndicator.className="my-chat-typing-indicator my-chat-hidden",this.elements.typingIndicator.innerHTML='\n            <div class="typing-dot"></div>\n            <div class="typing-dot"></div>\n            <div class="typing-dot"></div>\n        ',this.elements.messageArea.appendChild(this.elements.typingIndicator)}_createInputArea(){this.elements.inputArea=document.createElement("div"),this.elements.inputArea.className="my-chat-input-area",this.elements.inputArea.style.backgroundColor="var(--widget-input-bg)",this.elements.inputArea.style.borderTopColor="var(--widget-input-border-color)",this.elements.messageInput=document.createElement("textarea"),this.elements.messageInput.placeholder="Mesajınızı yazın...",this.elements.messageInput.setAttribute("aria-label","Mesajınızı yazın"),this.elements.inputArea.appendChild(this.elements.messageInput);const e=document.createElement("div");e.className="my-chat-input-icons-wrapper",this.settings.micButtonEnabled&&(this.elements.micButton=this._createInputIconButton("🎤","Sesli mesaj"),console.log("Orijinal micButton elementi oluşturuldu:",this.elements.micButton),e.appendChild(this.elements.micButton)),this.elements.sendButton=this._createInputIconButton("➤","Mesajı gönder",!0),this.elements.sendButton.style.color="var(--widget-primary-color)",e.appendChild(this.elements.sendButton),this.elements.inputArea.appendChild(e)}_createInputIconButton(e,t,i=!1){const s=document.createElement("button");return s.className="input-icon",i?s.classList.add("send-button-icon"):"🎤"===e?s.classList.add("mic-button"):"📎"===e&&s.classList.add("attach-button"),s.innerHTML=e,s.setAttribute("aria-label",t),s}_createFooter(){this.settings.footerText&&(this.elements.footer=document.createElement("div"),this.elements.footer.className="my-chat-footer",this.elements.footer.innerHTML=this.settings.footerText,this.elements.footer.style.backgroundColor="var(--widget-input-bg)",this.elements.footer.style.borderTopColor="var(--widget-input-border-color)",this.elements.chatWindow.appendChild(this.elements.footer))}_createLightboxDOM(){this.elements.lightboxOverlay=document.createElement("div"),this.elements.lightboxOverlay.className="my-chat-lightbox-overlay my-chat-hidden",this.elements.lightboxImage=document.createElement("img"),this.elements.lightboxImage.className="my-chat-lightbox-image",this.elements.lightboxClose=document.createElement("button"),this.elements.lightboxClose.className="my-chat-lightbox-close",this.elements.lightboxClose.innerHTML="✕",this.elements.lightboxOverlay.appendChild(this.elements.lightboxImage),this.elements.lightboxOverlay.appendChild(this.elements.lightboxClose),document.body.appendChild(this.elements.lightboxOverlay)}_initSocketConnection(){if(!this.socket||!this.socket.connected){if("undefined"==typeof io)return console.error("Socket.IO client library not found."),void this._addMessageToUI("Sistem Hatası: Gerekli kütüphane yüklenemedi.","system");if(!this.settings.hotelIdentifier)return console.error("hotelIdentifier ayarı eksik."),void this._addMessageToUI("Yapılandırma hatası: Otel kimliği belirtilmemiş.","system");this.socket=io(this.settings.backendUrl,{transports:["websocket"]}),this.socket.on("connect",()=>{console.log("WebSocket sunucusuna başarıyla bağlandı. SID:",this.socket.id),this.socket.emit("start_chat_from_widget",{hotel_identifier:this.settings.hotelIdentifier})}),this.socket.on("disconnect",e=>{console.log("WebSocket bağlantısı kesildi. Neden:",e),this._addMessageToUI("io server disconnect"===e?"Sohbet sunucu tarafından sonlandırıldı.":"Bağlantı sorunu yaşandı.","system")}),this.socket.on("error_message",e=>{console.error("Sunucudan hata mesajı alındı:",e.message),this._addMessageToUI("Hata: "+e.message,"system")}),this.socket.on("initial_chat_data",e=>{if(console.log("İlk sohbet verileri alındı:",e),e.error)this._addMessageToUI(e.error,"system");else if(this.sessionId=e.session_id,this.elements.headerTitleText&&(this.elements.headerTitleText.textContent=e.hotel_name||this.settings.headerText),e.initial_message){this._addMessageToUI(e.initial_message,"ai",e.timestamp,e.message_data||{});const t=e.message_data||{};t.set_expression&&this.playExpression(t.set_expression)}}),this.socket.on("ai_typing_started",e=>{e.session_id===this.sessionId&&this._showTypingIndicator(!0)}),this.socket.on("ai_response_to_widget",e=>{if(e.session_id===this.sessionId&&e.is_full_text){console.log("%c--- AI Yanıtı Alındı ---","color: #4CAF50; font-weight: bold;"),console.log("Gelen Ham Veri:",e),this._showTypingIndicator(!1);const t=e.message_data||{};console.log("İşlenecek Özel Veri (message_data):",t);const i="flex"===this.elements.mainChatContainer.style.display;i&&this.audio.notification.play().catch(e=>console.warn("Bildirim sesi oynatılamadı:",e)),this._addMessageToUI(e.message,"ai",e.timestamp,t,i),"victory"===t.trigger_animation&&(console.log("🏆 'victory' animasyon tetikleyicisi algılandı!"),"flex"===this.elements.avatarSceneContainer.style.display&&this.playVictoryAnimation()),t.set_expression&&(console.log(`😊 İfade komutu alındı: ${t.set_expression}`),this.playExpression(t.set_expression))}}),this.socket.on("audio_chunk_to_widget",e=>{e.session_id===this.sessionId&&(this.three.audioQueue.push(e),this.three.isAudioQueuePlaying||this._playNextInAudioQueue())}),this.socket.on("ai_stream_finished",e=>{e.session_id===this.sessionId&&(console.log("Sunucudan akışın bittiği bilgisi geldi."),this._showTypingIndicator(!1))})}}playExpression(e){const t=this.expressionRecipes[e];if(!t||!this.three.morphTargetDictionary)return;console.log(`😊 İfade aktive ediliyor: '${e}'`);const i=this.three.expressionClock.getElapsedTime();t.forEach(e=>{const t=this.three.morphTargetDictionary[e.name];void 0!==t&&(this.three.activeExpressions[t]={startTime:i+e.start,peakTime:i+e.peak,endTime:i+e.end,targetValue:e.targetValue})})}_updateExpressions(){if(!this.three.morphTargetInfluences)return;const e=this.three.expressionClock.getElapsedTime(),t={};if(!this.three.isPlaying){const i=this.three.morphTargetDictionary.mouthSmileLeft,s=this.three.morphTargetDictionary.mouthSmileRight;if(void 0!==i&&void 0!==s){const n=(Math.sin(.5*e)+1)/2*.35;t[i]=n,t[s]=n}}for(const i in this.three.activeExpressions){const s=this.three.activeExpressions[i];if(e>s.endTime){delete this.three.activeExpressions[i];continue}let n=0;if(e>=s.startTime&&e<=s.peakTime){const t=(e-s.startTime)/(s.peakTime-s.startTime);n=s.targetValue*Math.sin(t*Math.PI/2)}else if(e>s.peakTime&&e<s.endTime){const t=(e-s.peakTime)/(s.endTime-s.peakTime);n=s.targetValue*Math.cos(t*Math.PI/2)}t[i]=Math.max(t[i]||0,n)}for(const[e,i]of Object.entries(this.three.morphTargetDictionary)){if(e.startsWith("viseme_"))continue;const s=t[i]||0;this.three.morphTargetInfluences[i]=THREE.MathUtils.lerp(this.three.morphTargetInfluences[i],s,.1)}}_addEventListeners(){console.log("--- MyChatWidget: _addEventListeners BAŞLADI ---"),this.elements.launcher.addEventListener("click",()=>this.toggleWindow());const e=document.getElementById("my-chat-start-text-btn"),t=document.getElementById("my-chat-start-video-btn");e&&e.addEventListener("click",()=>this._startTextChat()),t&&t.addEventListener("click",()=>this._startVideoChat()),this.elements.closeButton.addEventListener("click",()=>this.toggleWindow(!1)),this.elements.backButton.addEventListener("click",()=>this._showWelcomeScreen()),this.elements.speakerButton.addEventListener("click",()=>this._toggleAudio()),this.elements.sendButton&&this.elements.sendButton.addEventListener("click",()=>this._sendMessage()),this.elements.messageInput&&(this.elements.messageInput.addEventListener("keypress",e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),this._sendMessage())}),this.elements.messageInput.addEventListener("input",e=>{e.target.style.height="auto",e.target.style.height=e.target.scrollHeight+"px"})),this.elements.lightboxOverlay&&this.elements.lightboxOverlay.addEventListener("click",e=>{e.target!==this.elements.lightboxOverlay&&e.target!==this.elements.lightboxClose||this._hideLightbox()}),console.log("--- MyChatWidget: _addEventListeners BİTTİ ---")}_startTextChat(){this.elements.welcomeScreen.style.display="none",this.elements.mainChatContainer.style.display="flex",this.elements.avatarSceneContainer.style.display="none",this.elements.backButton.style.display="flex",this.elements.speakerButton.style.display="flex",this.elements.closeButton.style.display="flex",this.socket&&this.socket.connected||this._initSocketConnection()}_startVideoChat(){this.elements.welcomeScreen.style.display="none",this.elements.mainChatContainer.style.display="none",this.elements.avatarSceneContainer.style.display="flex",this.elements.backButton.style.display="flex",this.elements.speakerButton.style.display="flex",this.elements.closeButton.style.display="flex",this.three.audioSource&&this.three.audioSource.gain&&(this.three.audioSource.gain.gain.value=this.isAudioEnabled?1:0),this.three.audioContext||(this.three.audioContext=new(window.AudioContext||window.webkitAudioContext)),this._startMicrophoneMonitoringForBargeIn(),this.three.scene||this._initThreeScene(),this.socket&&this.socket.connected||this._initSocketConnection()}async _startMicrophoneMonitoringForBargeIn(){if(!this.three.micStream)try{const e=await navigator.mediaDevices.getUserMedia({audio:!0});this.three.micStream=e,"suspended"===this.three.audioContext.state&&await this.three.audioContext.resume();const t=this.three.audioContext.createMediaStreamSource(e),i=this.three.audioContext.createAnalyser();i.fftSize=512,i.smoothingTimeConstant=.5,t.connect(i),this.three.micAnalyser=i,console.log("✅ Barge-in için mikrofon dinlemesi aktif.")}catch(e){console.error("Mikrofon erişimi hatası (barge-in için):",e)}}_initThreeScene(){const e=document.getElementById("my-chat-avatar-canvas");if(!e)return;this.three.scene=new THREE.Scene,this.three.camera=new THREE.PerspectiveCamera(25,e.clientWidth/e.clientHeight,.1,1e3),this.three.camera.position.set(0,1.3,1.8),this.three.renderer=new THREE.WebGLRenderer({canvas:e,antialias:!0,alpha:!0}),this.three.renderer.setSize(e.clientWidth,e.clientHeight),this.three.renderer.setPixelRatio(window.devicePixelRatio),this.three.renderer.outputColorSpace=THREE.SRGBColorSpace;const t=new THREE.DirectionalLight(16777215,2.5);t.position.set(1,2,3),this.three.scene.add(t);const i=new THREE.AmbientLight(16777215,1);this.three.scene.add(i);const s=`${this.settings.backendUrl}/static/models/reception.jpg`;console.log(`INFO: Arka plan resmi yükleniyor: ${s}`);(new THREE.TextureLoader).load(s,e=>{e.colorSpace=THREE.SRGBColorSpace,this.three.scene.background=e,console.log("✅ Arka plan resmi yüklendi.")},void 0,e=>{console.error("Arka plan resmi yüklenemedi:",e),this.three.scene.background=new THREE.Color(3355443)}),this.elements.avatarLoader.classList.add("visible");const n=`${this.settings.backendUrl}/static/models/avatarkad9.glb`;console.log(`INFO: 3D model yükleniyor: ${n}`);(new GLTFLoader).load(n,e=>{this.three.avatar=e.scene,this.three.scene.add(this.three.avatar),this.three.avatar.position.y=-.1,this.three.avatar.position.x=0,this.three.avatar.position.z=-.8,this.three.avatar.traverse(e=>{e.isSkinnedMesh&&e.morphTargetDictionary&&(this.three.morphTargetDictionary=e.morphTargetDictionary,this.three.morphTargetInfluences=e.morphTargetInfluences),e.isBone&&("mixamorigHead"===e.name&&(this.three.headBone=e),"mixamorigNeck"===e.name&&(this.three.neckBone=e),"mixamorigSpine2"===e.name&&(this.three.spineBone=e))}),this.three.headBone&&console.log("✅ Kafa kemiği bulundu."),this.three.morphTargetDictionary&&console.log("✅ Morph target'lar bulundu."),this.three.morphTargetInfluences&&this.three.morphTargetInfluences.fill(0),this.three.animations=e.animations,this.loadAnimations(),console.log("✅ Avatar ve tüm bileşenleri başarıyla yüklendi ve ayarlandı."),this.elements.avatarLoader.classList.remove("visible")},void 0,e=>{console.error("Avatar yüklenirken kritik bir hata oluştu:",e),this.elements.avatarLoader.classList.remove("visible")}),this._animate()}loadAnimations(){if(!this.three.avatar||!this.three.animations)return;const e=this.three.animations;this.three.mixer=new THREE.AnimationMixer(this.three.avatar),this.three.animationActions={};for(let t=0;t<e.length;t++){const i=e[t],s=this.three.mixer.clipAction(i);i.name.includes("Layer0")||(this.three.animationActions[i.name]=s,"victory"===i.name.toLowerCase()?(s.setLoop(THREE.LoopOnce),s.clampWhenFinished=!0):s.setLoop(THREE.LoopRepeat))}if(0===Object.keys(this.three.animationActions).length)return;console.log("✅ Yüklenen Animasyon Eylemleri:",Object.keys(this.three.animationActions));const t=Object.keys(this.three.animationActions).find(e=>e.toLowerCase().startsWith("idle"));t&&(this.three.activeAction=this.three.animationActions[t],this.three.activeAction.play()),this.startIdleAnimationSwitcher()}startIdleAnimationSwitcher(){const e=()=>{this.playIdleAnimation();const t=5e3+5e3*Math.random();setTimeout(e,t)},t=5e3+5e3*Math.random();setTimeout(e,t)}playIdleAnimation(){const e=Object.keys(this.three.animationActions).filter(e=>e.toLowerCase().startsWith("idle"));if(e.length<=1)return;let t;do{t=e[Math.floor(Math.random()*e.length)]}while(this.three.activeAction&&this.three.activeAction.getClip().name===t);console.log(`Idle animasyonu değiştiriliyor -> ${t}`),this.fadeToAction(t,1)}playVictoryAnimation(){if(!this.three.animationActions.victory)return;this.fadeToAction("victory",.2);const e=1e3*this.three.animationActions.victory.getClip().duration;setTimeout(()=>{console.log("Victory bitti, idle döngüsüne dönülüyor."),this.playIdleAnimation()},e-200)}fadeToAction(e,t){this.three.lastAction=this.three.activeAction,this.three.activeAction=this.three.animationActions[e],this.three.lastAction!==this.three.activeAction&&this.three.activeAction&&(this.three.activeAction.reset().setEffectiveWeight(1).play(),this.three.lastAction?this.three.lastAction.crossFadeTo(this.three.activeAction,t,!0):this.three.activeAction.fadeIn(t))}_animate(){if(requestAnimationFrame(()=>this._animate()),!this.three.renderer||!this.three.scene)return;const e=this.three.clock.getDelta();this.three.mixer&&this.three.mixer.update(e),this._updateLipSync(),this._updateExpressions(),this._updateBlinking(e),this._updateNaturalLook(e),this.three.renderer.render(this.three.scene,this.three.camera)}_updateNaturalLook(){if(!this.three.avatar||!this.three.camera||!this.three.headBone)return;const e=new THREE.Vector3;if(this.three.camera.getWorldPosition(e),this.three.isPlaying)this.three.headBone.lookAt(e),this.three.neckBone&&this.three.neckBone.lookAt(e);else{const t=new THREE.Quaternion,i=new THREE.Vector3;this.three.avatar.getWorldPosition(i);const s=new THREE.Matrix4;s.lookAt(new THREE.Vector3(e.x,i.y,e.z),i,this.three.avatar.up),t.setFromRotationMatrix(s),this.three.avatar.quaternion.slerp(t,.05);const n=this.three.clock.getElapsedTime();e.x+=.03*Math.sin(.7*n),e.y+=.04*Math.cos(.4*n);const a=new THREE.Quaternion,o=new THREE.Matrix4;o.lookAt(e,this.three.headBone.position,this.three.headBone.up),a.setFromRotationMatrix(o),this.three.headBone.quaternion.slerp(a,.05)}}_checkForBargeIn(){if(this.three.isPlaying&&this.three.micAnalyser&&!this.three.isBargeInDetected){const e=new Uint8Array(this.three.micAnalyser.frequencyBinCount);this.three.micAnalyser.getByteTimeDomainData(e);let t=0;for(const i of e){const e=Math.abs(i-128);e>t&&(t=e)}t>20&&(console.log(`Barge-in algılandı! Mikrofon Zirve Değeri: ${t}`),this.three.isBargeInDetected=!0,this._fadeOutAudio())}}async _base64ToArrayBuffer(e){return(await fetch(`data:audio/mpeg;base64,${e}`)).arrayBuffer()}_addMessageToUI(e,t,i,s={},n=!1){if(!this.elements.messageArea)return;const a=document.createElement("div");a.className=`my-chat-message-entry ${t}`,"ai"===t&&this.settings.aiAvatarUrl?o.appendChild(this._createAvatarElement(this.settings.aiAvatarUrl,"AI Avatar")):"user"===t&&this.settings.userAvatarUrl&&o.appendChild(this._createAvatarElement(this.settings.userAvatarUrl,"User Avatar")),n&&requestAnimationFrame(()=>{a.classList.add("new-message-animation")});const o=document.createElement("div");o.className=`my-chat-message-wrapper ${t}`,"ai"===t&&this.settings.aiAvatarUrl&&o.appendChild(this._createAvatarElement(this.settings.aiAvatarUrl,"AI Avatar"));const r=document.createElement("div");r.classList.add("my-chat-message",`my-chat-message-${t}`);const h=document.createElement("div");if(h.className="content-container",e&&""!==e.trim()){const i=document.createElement("div");i.innerHTML=this._formatTextToHtml(e,t),h.appendChild(i)}if("ai"===t&&"gallery"===s.type&&s.items&&this._renderImageGallery(h,s.items),r.appendChild(h),o.appendChild(r),a.appendChild(o),this.settings.showTimestamps&&i){const e=document.createElement("div");e.className="my-chat-timestamp-container";try{const t=new Date(i);isNaN(t)||(e.textContent=t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}))}catch(e){}a.appendChild(e)}this.elements.messageArea.insertBefore(a,this.elements.typingIndicator),requestAnimationFrame(()=>{this.elements.messageArea&&(this.elements.messageArea.scrollTop=this.elements.messageArea.scrollHeight)}),a.addEventListener("animationend",()=>{a.classList.remove("new-message-animation")})}_convertWordTimingsToVisemes(e){if(!e||0===e.length)return[];const t=[],{latencyOffset:i,vowelDuration:s,consonantDuration:n,lipCloseDuration:a}=this.animationParams;let o=0;for(const r of e){let{time:e,value:h}=r;e-=i,e<0&&(e=0),e>o+.1&&t.push({time:o,value:"viseme_sil"});let l=e;if(h=h.toLowerCase().trim(),!h)continue;const c=h.match(/([aeıioöuü]+)|([bcçdfgğhjklmnprsştvyz]+)/g)||[];for(const e of c){const i=e[0];let o="viseme_sil",r=0;if("aeıioöuü".includes(i)){const t=e[Math.floor(e.length/2)];o=this.phonemeToVisemeMap[t]||"viseme_sil",r=s*e.length}else{const e=i;o=this.phonemeToVisemeMap[e]||"viseme_sil",r="pbm".includes(e)?a:n}"viseme_sil"!==o&&t.push({time:l,value:o}),l+=r}o=l}t.length>0&&t.push({time:o,value:"viseme_sil"}),t.sort((e,t)=>e.time-t.time);const r=t.filter((e,t,i)=>0===t||e.value!==i[t-1].value);return console.log("Üretilen Viseme Kuyruğu:",r),r}async playLipSync(e,t){if(!this.three.avatar||!this.three.audioContext)return console.error("LipSync başlatılamıyor: Avatar veya AudioContext hazır değil."),void this._playNextInAudioQueue();this.three.audioSource&&this.three.audioSource.stop(0),this.three.isBargeInDetected=!1,this.three.visemeQueue=this._convertWordTimingsToVisemes(e),this.three.isPlaying=!0,this.three.audioStartTime=this.three.audioContext.currentTime;try{const e=await this._base64ToArrayBuffer(t),i=await this.three.audioContext.decodeAudioData(e),s=this.three.audioContext.createBufferSource();s.buffer=i;const n=this.three.audioContext.createGain();n.gain.value=this.isAudioEnabled?1:0,s.connect(n).connect(this.three.audioContext.destination),this.three.audioSource=s,this.three.audioSource.gain=n,s.onended=()=>{this.three.isPlaying=!1,this._playNextInAudioQueue()},s.start(0)}catch(e){}}_playNextInAudioQueue(){if(0===this.three.audioQueue.length)return console.log("Ses kuyruğu tamamlandı."),void(this.three.isAudioQueuePlaying=!1);this.three.isAudioQueuePlaying=!0;const e=this.three.audioQueue.shift();this.playLipSync(e.word_timings,e.audio_content_b64)}async _playAudioForLipSync(e){if(this.isAudioEnabled)try{const t=await this._base64ToArrayBuffer(e),i=await this.three.audioContext.decodeAudioData(t),s=this.three.audioContext.createBufferSource();s.buffer=i;const n=this.three.audioContext.createGain(),a=this.three.audioContext.createAnalyser();a.fftSize=512,s.connect(a).connect(n).connect(this.three.audioContext.destination),this.three.audioSource=s,this.three.audioSource.gain=n,this.three.analyser=a,s.start(0)}catch(e){console.error("Ses çalma hatası:",e)}else this.three.audioSource&&this.three.audioSource.stop(0)}_playVisemeAnimation(e){if(this.three.isPlaying)return;this.three.visemeQueue=this._convertWordTimingsToVisemes(e),this.three.isPlaying=!0,this.three.audioStartTime=this.three.audioContext.currentTime;const t=this.three.visemeQueue[this.three.visemeQueue.length-1],i=t?t.time:.5;setTimeout(()=>{this.three.isPlaying=!1,this.three.isBargeInDetected?(this.three.isBargeInDetected=!1,this.three.isAudioQueuePlaying=!1):this._playNextInAudioQueue()},1e3*(i+.2))}_updateLipSync(){if(this.three.morphTargetInfluences&&this.three.morphTargetDictionary){if(this.three.isPlaying){const e=this.three.audioContext.currentTime-this.three.audioStartTime;let t=null;for(;this.three.visemeQueue.length>0&&e>=this.three.visemeQueue[0].time;)t=this.three.visemeQueue.shift().value;t&&(this.three.currentViseme=t)}else this.three.currentViseme="viseme_sil";for(const[e,t]of Object.entries(this.three.morphTargetDictionary))if(e.startsWith("viseme_")){const i=e===this.three.currentViseme?this.visemeWeights[e]||1:0;this.three.morphTargetInfluences[t]=THREE.MathUtils.lerp(this.three.morphTargetInfluences[t],i,this.animationParams.lerpFactor)}}}_fadeOutAudio(e=.3){this.three.audioSource&&this.three.audioSource.context&&(console.log("Sesi anında durduruyorum (barge-in)..."),this.three.audioSource.stop(0))}_resetAllVisemes(){if(this.three.morphTargetInfluences&&this.three.morphTargetDictionary)for(const[e,t]of Object.entries(this.three.morphTargetDictionary))e.startsWith("viseme_")&&(this.three.morphTargetInfluences[t]=THREE.MathUtils.lerp(this.three.morphTargetInfluences[t],0,.2))}_updateBlinking(e){if(!this.three.morphTargetInfluences)return;const t=this.three.morphTargetDictionary.eyeBlinkLeft,i=this.three.morphTargetDictionary.eyeBlinkRight;void 0!==t&&void 0!==i&&(this.three.morphTargetInfluences[t]*=.85,this.three.morphTargetInfluences[i]*=.85,this.three.lastBlinkTime-=e,!this.three.isPlaying&&this.three.lastBlinkTime<0&&(this.three.lastBlinkTime=2+4*Math.random(),this.three.morphTargetInfluences[t]=1,this.three.morphTargetInfluences[i]=1))}async _base64ToArrayBuffer(e){return(await fetch(`data:audio/mpeg;base64,${e}`)).arrayBuffer()}_handleMicButtonClick(){this.isMicListeningMode=!this.isMicListeningMode,this._updateMicVisuals(this.isMicListeningMode),this.isMicListeningMode?this._startContinuousRecognition():this.speechRecognition&&this.speechRecognition.stop()}_startContinuousRecognition(){if(this.recognitionActive||!this.isMicListeningMode)return;const e=window.SpeechRecognition||window.webkitSpeechRecognition;if(!e)return this._addMessageToUI("Tarayıcınız ses tanımayı desteklemiyor.","system"),this.isMicListeningMode=!1,void this._updateMicVisuals(!1);this.speechRecognition=new e,this.speechRecognition.onstart=()=>{this.recognitionActive=!0,console.log("Sürekli dinleme: Yeni döngü başladı.")},this.speechRecognition.onresult=e=>{const t=e.results[e.results.length-1][0].transcript.trim();console.log("Konuşma algılandı (sürekli dinleme):",t),this.three.isPlaying&&this._stopAllAudio(!0),this._sendMessageFromClone(null,t)},this.speechRecognition.onend=()=>{this.recognitionActive=!1,console.log("Sürekli dinleme: Döngü bitti."),this.isMicListeningMode&&(console.log("Yeniden başlatılıyor..."),this._startContinuousRecognition())},this.speechRecognition.onerror=e=>{"no-speech"!==e.error&&console.error("Konuşma tanıma hatası:",e.error)},this.speechRecognition.start()}_updateMicVisuals(e){[this.elements.micButton,this.elements.cloneMicButton].forEach(t=>{t&&(t.classList.toggle("is-listening",e),this.elements.cloneInputArea&&t===this.elements.cloneMicButton&&this.elements.cloneInputArea.classList.toggle("mic-active-bg",e))})}_updateMicIcons(e){[this.elements.micButton,this.elements.cloneMicButton].forEach(t=>{t&&t.classList.toggle("is-listening",e)})}_stopMicrophoneMonitoring(){this.three.micStream&&(this.three.micStream.getTracks().forEach(e=>e.stop()),this.three.micStream=null,this.three.micAnalyser=null,this.three.micGainNode=null)}toggleWindow(e){const t="boolean"==typeof e?e:!this.isWindowOpen;if(this.isWindowOpen===t&&!e&&void 0!==e)return;this.isWindowOpen=t;const i=window.innerWidth<=480;this.isWindowOpen?(this.elements.chatWindow.classList.remove("my-chat-hidden"),i&&(this.elements.launcher.style.display="none"),this.elements.messageInput.focus(),requestAnimationFrame(()=>{this.elements.messageArea&&(this.elements.messageArea.scrollTop=this.elements.messageArea.scrollHeight)})):(this.elements.chatWindow.classList.add("my-chat-hidden"),this.elements.launcher.style.display="flex",this.notificationInterval&&clearInterval(this.notificationInterval))}_sendMessage(){const e=this.elements.messageInput.value.trim();""!==e&&this.socket&&this.sessionId&&this.socket.connected?(this._addMessageToUI(e,"user",(new Date).toISOString()),this.socket.emit("user_message_to_server",{session_id:this.sessionId,message:e,mode:"text"}),this.elements.messageInput.value="",this.elements.messageInput.style.height="auto",this.elements.messageInput.focus()):this.socket&&!this.socket.connected&&this._addMessageToUI("Bağlantı yok, mesaj gönderilemedi.","system")}_formatTextToHtml(e,t="ai"){if(!e&&""!==e)return"";if(null==e)return"";let i=String(e);if("user"===t){i=i.replace(/&/g,"&").replace(/</g,"<").replace(/>/g,">"),i=i.replace(/\n/g,"<br>");const e=/(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi;return i.replace(e,'<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>')}const s=/(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi;i=i.replace(s,'<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>'),i=i.replace(/\*\*(.*?)\*\*/g,"<b>$1</b>").replace(/__(.*?)__/g,"<b>$1</b>"),i=i.replace(/\*(.*?)\*/g,"<i>$1</i>").replace(/_(.*?)_/g,"<i>$1</i>");let n=[],a=!1;const o=i.split("\n");for(let e=0;e<o.length;e++){let t=o[e];const i=t.trim();if(!(s.test(i)&&i.match(s)[0].length===i.length)||i.startsWith("- ")||i.startsWith("* "))if(i.startsWith("- ")||i.startsWith("* ")){const e=i.substring(2).trim();a||(n.push("<ul>"),a=!0),n.push(`<li>${e}</li>`)}else a&&(n.push("</ul>"),a=!1),n.push(t);else a&&(n.push("</ul>"),a=!1),n.push(t)}return a&&n.push("</ul>"),i=n.join("\n"),i=i.replace(/\n/g,"<br>"),i=i.replace(/(<br\s*\/?>\s*){2,}/g,"<br>"),i=i.replace(/^\s*<br\s*\/?>|<br\s*\/?>\s*$/g,""),i=i.replace(/<li><br\s*\/?>\s*<\/li>/g,"<li> </li>"),i=i.replace(/\[image:\s*(https?:\/\/[^\s\]]+)\s*\]/gi,'<img src="$1" alt="Sohbet görseli" class="my-chat-image">'),i}_addMessageToUI(e,t,i,s={},n=!1){if(!this.elements.messageArea)return;const a=document.createElement("div");a.className=`my-chat-message-entry ${t}`;const o=document.createElement("div");o.className=`my-chat-message-wrapper ${t}`,"ai"===t&&this.settings.aiAvatarUrl?o.appendChild(this._createAvatarElement(this.settings.aiAvatarUrl,"AI Avatar")):"user"===t&&this.settings.userAvatarUrl&&o.appendChild(this._createAvatarElement(this.settings.userAvatarUrl,"User Avatar"));const r=document.createElement("div");r.classList.add("my-chat-message",`my-chat-message-${t}`);const h=document.createElement("div");if(h.className="content-container",e&&"string"==typeof e&&""!==e.trim()){const i=document.createElement("div");i.innerHTML=this._formatTextToHtml(e,t),h.appendChild(i)}if("ai"===t&&s&&"gallery"===s.type&&s.items&&s.items.length>0?this._renderImageGallery(h,s.items):"ai"===t&&0===h.childNodes.length&&(h.innerHTML=this._formatTextToHtml("...",t)),r.appendChild(h),o.appendChild(r),a.appendChild(o),this.settings.showTimestamps&&i){const e=document.createElement("div");e.className="my-chat-timestamp-container";try{const s=new Date(i);if(!isNaN(s)){let i="";"ai"===t&&this.settings.aiAvatarUrl?i=(this.settings.aiAgentName||"AI Agent")+" · ":"user"===t&&this.settings.userAvatarUrl&&(i="Siz · "),e.textContent=i+s.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}}catch(e){}a.appendChild(e)}n?(a.classList.add("animate-in-start"),this.elements.messageArea.insertBefore(a,this.elements.typingIndicator),requestAnimationFrame(()=>{a.classList.add("animate-in-end")})):this.elements.messageArea.insertBefore(a,this.elements.typingIndicator),requestAnimationFrame(()=>{this.elements.messageArea&&(this.elements.messageArea.scrollTop=this.elements.messageArea.scrollHeight)})}_createAvatarElement(e,t){const i=document.createElement("div");i.className="my-chat-message-avatar";const s=document.createElement("img");return s.src=e,s.alt=t,i.appendChild(s),i}_renderImageGallery(e,t){const i=document.createElement("div");i.className="my-chat-image-gallery",t.forEach(e=>{if(e.url){const t=document.createElement("figure");t.className="my-chat-gallery-item";const s=document.createElement("img");if(s.src=e.url,s.alt=e.caption||"Sohbet görseli",s.className="my-chat-image",s.addEventListener("click",()=>this._showLightbox(e.url)),t.appendChild(s),e.caption&&""!==e.caption.trim()){const i=document.createElement("figcaption");i.textContent=e.caption,t.appendChild(i)}i.appendChild(t)}}),i.hasChildNodes()&&e.appendChild(i)}_showLightbox(e){this.elements.lightboxOverlay&&(this.elements.lightboxImage.src=e,this.elements.lightboxOverlay.classList.remove("my-chat-hidden"))}_hideLightbox(){this.elements.lightboxOverlay&&(this.elements.lightboxOverlay.classList.add("my-chat-hidden"),this.elements.lightboxImage.src="")}_showTypingIndicator(e){if(this.aiTyping=e,this.elements.typingIndicator){const t=this.elements.typingIndicator.classList.contains("my-chat-hidden");e&&t?this.elements.typingIndicator.classList.remove("my-chat-hidden"):e||t||this.elements.typingIndicator.classList.add("my-chat-hidden"),requestAnimationFrame(()=>{this.elements.messageArea&&(this.elements.messageArea.scrollTop=this.elements.messageArea.scrollHeight)})}}_notifyNewMessage(){if(document.hidden&&this.isWindowOpen){this.notificationInterval&&clearInterval(this.notificationInterval);let e=0;this.notificationInterval=setInterval(()=>{document.title=e%2==0?"💬 Yeni Mesaj!":this.originalTitle,e++},1200);const t=()=>{this.notificationInterval&&clearInterval(this.notificationInterval),document.title=this.originalTitle,this.notificationInterval=null,window.removeEventListener("focus",t)};window.addEventListener("focus",t)}else!document.hidden&&this.notificationInterval&&(clearInterval(this.notificationInterval),document.title=this.originalTitle,this.notificationInterval=null)}}function initializeWidget(){const e=window.myChatWidgetSettings||{};new MyChatWidget(e),console.log("--- MyChatWidget: Tek Sefer Başlatıldı! ---")}"loading"===document.readyState?document.addEventListener("DOMContentLoaded",initializeWidget):initializeWidget();