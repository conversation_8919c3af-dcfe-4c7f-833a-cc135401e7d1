import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Chip,
  CircularProgress,
  Alert
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { useApi } from '../contexts/ApiContext';

const Reservations = () => {
  const { getReservations, updateReservationStatus, currentHotelId } = useApi();
  const [reservations, setReservations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [updating, setUpdating] = useState(null);

  const fetchReservations = async () => {
    try {
      setLoading(true);
      console.log('Fetching reservations for hotel:', currentHotelId);
      const data = await getReservations(currentHotelId);
      console.log('Reservations data:', data);
      setReservations(data.reservations || []);
      setError(null);
    } catch (err) {
      console.error('Reservations fetch error:', err);
      setError('Rezervasyonlar yüklenirken bir hata oluştu: ' + err.message);
      setReservations([]);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (reservationId, newStatus) => {
    try {
      setUpdating(reservationId);
      await updateReservationStatus(reservationId, newStatus);
      // Refresh reservations after update
      await fetchReservations();
    } catch (err) {
      console.error('Status update error:', err);
      alert('Durum güncellenirken hata oluştu: ' + err.message);
    } finally {
      setUpdating(null);
    }
  };

  useEffect(() => {
    fetchReservations();

    // Auto refresh every 30 seconds
    const interval = setInterval(fetchReservations, 30000);
    return () => clearInterval(interval);
  }, [currentHotelId]);

  const getStatusChip = (status) => {
    const statusMap = {
      'HUMAN_CONFIRMED': { label: 'Onaylandı', color: 'success' },
      'AI_PENDING_HUMAN': { label: 'Onay Bekliyor', color: 'warning' },
      'CANCELLED_GUEST': { label: 'Misafir İptal', color: 'error' },
      'CANCELLED_HOTEL': { label: 'Otel İptal', color: 'error' },
      'COMPLETED': { label: 'Tamamlandı', color: 'info' },
      'NO_SHOW': { label: 'Gelmedi', color: 'default' }
    };
    
    const statusInfo = statusMap[status] || { label: status, color: 'default' };
    return <Chip label={statusInfo.label} color={statusInfo.color} size="small" />;
  };

  const columns = [
    { field: 'reservation_id', headerName: 'Rezervasyon ID', width: 130 },
    { field: 'guest_name', headerName: 'Ad', width: 100 },
    { field: 'guest_surname', headerName: 'Soyad', width: 100 },
    { field: 'room_type', headerName: 'Oda Tipi', width: 150 },
    { field: 'check_in_date', headerName: 'Giriş', width: 120 },
    { field: 'check_out_date', headerName: 'Çıkış', width: 120 },
    {
      field: 'status',
      headerName: 'Durum',
      width: 150,
      renderCell: (params) => getStatusChip(params.value)
    },
    { field: 'total_price_at_booking', headerName: 'Tutar (₺)', width: 100 },
    {
      field: 'actions',
      headerName: 'İşlemler',
      width: 200,
      renderCell: (params) => (
        <Box display="flex" gap={1}>
          {params.row.status === 'AI_PENDING_HUMAN' && (
            <>
              <Button
                variant="contained"
                size="small"
                color="success"
                disabled={updating === params.row.id}
                onClick={() => handleStatusUpdate(params.row.id, 'HUMAN_CONFIRMED')}
              >
                Onayla
              </Button>
              <Button
                variant="outlined"
                size="small"
                color="error"
                disabled={updating === params.row.id}
                onClick={() => handleStatusUpdate(params.row.id, 'CANCELLED_HOTEL')}
              >
                Reddet
              </Button>
            </>
          )}
          {params.row.status === 'HUMAN_CONFIRMED' && (
            <Button
              variant="outlined"
              size="small"
              color="info"
              disabled={updating === params.row.id}
              onClick={() => handleStatusUpdate(params.row.id, 'COMPLETED')}
            >
              Tamamla
            </Button>
          )}
        </Box>
      )
    }
  ];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom fontWeight="bold">
        Rezervasyon Yönetimi
      </Typography>
      <Typography variant="body1" color="text.secondary" mb={3}>
        Tüm rezervasyonları görüntüleyin ve yönetin.
      </Typography>

      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error} (Demo verileri gösteriliyor)
        </Alert>
      )}

      <Card>
        <CardContent>
          <Box sx={{ height: 600, width: '100%' }}>
            <DataGrid
              rows={reservations}
              columns={columns}
              pageSize={10}
              rowsPerPageOptions={[10, 25, 50]}
              disableSelectionOnClick
              sx={{
                '& .MuiDataGrid-cell': {
                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                },
                '& .MuiDataGrid-columnHeaders': {
                  backgroundColor: 'rgba(255, 255, 255, 0.05)',
                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                },
              }}
            />
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Reservations;
