import { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Alert,
  Chip,
  Avatar,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Hotel as HotelIcon,
  Save as SaveIcon,
  Edit as EditIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Room as RoomIcon,
  Star as StarIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  Language as WebsiteIcon,
  Business as BusinessIcon
} from '@mui/icons-material';
import { useApi } from '../contexts/ApiContext';
import { useAuth } from '../contexts/AuthContext';

const HotelSettings = () => {
  const { getHotelInfo, updateHotelInfo, currentHotelId } = useApi();
  const { user } = useAuth();
  const [hotelInfo, setHotelInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [roomDialogOpen, setRoomDialogOpen] = useState(false);
  const [newRoom, setNewRoom] = useState({ name: '', quantity: 0, price: 0 });

  // Form state
  const [formData, setFormData] = useState({
    hotel_name: '',
    address: '',
    phone: '',
    email: '',
    website: '',
    room_count: 0,
    star_rating: 4,
    check_in_time: '14:00',
    check_out_time: '12:00',
    description: '',
    amenities: [],
    room_types: []
  });

  const amenitiesList = [
    'WiFi', 'Spa', 'Fitness', 'Restaurant', 'Bar', 'Parking', 
    'Pool', 'Concierge', 'Room Service', 'Laundry', 'Airport Shuttle', 'Pet Friendly'
  ];

  useEffect(() => {
    fetchHotelInfo();
  }, [currentHotelId]);

  const fetchHotelInfo = async () => {
    try {
      setLoading(true);
      const info = await getHotelInfo(currentHotelId);
      setHotelInfo(info);
      
      // Form data'yı doldur
      setFormData({
        hotel_name: info.hotel_name || '',
        address: info.address || '',
        phone: info.phone || '',
        email: info.email || '',
        website: info.website || '',
        room_count: info.room_count || 0,
        star_rating: info.star_rating || 4,
        check_in_time: info.check_in_time || '14:00',
        check_out_time: info.check_out_time || '12:00',
        description: info.description || '',
        amenities: info.amenities || [],
        room_types: info.room_types || []
      });
    } catch (err) {
      console.error('Hotel info fetch error:', err);
      setError('Otel bilgileri yüklenirken hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAmenityToggle = (amenity) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...prev.amenities, amenity]
    }));
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);
      
      await updateHotelInfo(currentHotelId, formData);
      setSuccess('Otel bilgileri başarıyla güncellendi!');
      
      // 3 saniye sonra success mesajını temizle
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Hotel info update error:', err);
      setError('Otel bilgileri güncellenirken hata oluştu.');
    } finally {
      setSaving(false);
    }
  };

  const handleAddRoom = () => {
    if (newRoom.name && newRoom.quantity > 0) {
      setFormData(prev => ({
        ...prev,
        room_types: [...prev.room_types, { ...newRoom, id: Date.now() }]
      }));
      setNewRoom({ name: '', quantity: 0, price: 0 });
      setRoomDialogOpen(false);
    }
  };

  const handleDeleteRoom = (roomId) => {
    setFormData(prev => ({
      ...prev,
      room_types: prev.room_types.filter(room => room.id !== roomId)
    }));
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Otel bilgileri yükleniyor...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={4}>
        <Box display="flex" alignItems="center" mb={2}>
          <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
            <HotelIcon />
          </Avatar>
          <Box>
            <Typography variant="h4" fontWeight="600">
              Otel Ayarları
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Otel bilgilerinizi düzenleyin ve servera kaydedin
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Alerts */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Sol Taraf - Temel Bilgiler */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                🏨 Temel Bilgiler
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Otel Adı"
                    value={formData.hotel_name}
                    onChange={(e) => handleInputChange('hotel_name', e.target.value)}
                    InputProps={{
                      startAdornment: <BusinessIcon sx={{ mr: 1, color: 'text.secondary' }} />
                    }}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Adres"
                    multiline
                    rows={2}
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    InputProps={{
                      startAdornment: <LocationIcon sx={{ mr: 1, color: 'text.secondary', alignSelf: 'flex-start', mt: 1 }} />
                    }}
                  />
                </Grid>
                
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Telefon"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    InputProps={{
                      startAdornment: <PhoneIcon sx={{ mr: 1, color: 'text.secondary' }} />
                    }}
                  />
                </Grid>
                
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    InputProps={{
                      startAdornment: <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />
                    }}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Website"
                    value={formData.website}
                    onChange={(e) => handleInputChange('website', e.target.value)}
                    InputProps={{
                      startAdornment: <WebsiteIcon sx={{ mr: 1, color: 'text.secondary' }} />
                    }}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Sağ Taraf - Otel Özellikleri */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                ⭐ Otel Özellikleri
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Toplam Oda Sayısı"
                    type="number"
                    value={formData.room_count}
                    onChange={(e) => handleInputChange('room_count', parseInt(e.target.value) || 0)}
                  />
                </Grid>
                
                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <InputLabel>Yıldız Sayısı</InputLabel>
                    <Select
                      value={formData.star_rating}
                      onChange={(e) => handleInputChange('star_rating', e.target.value)}
                    >
                      {[1, 2, 3, 4, 5].map(star => (
                        <MenuItem key={star} value={star}>
                          <Box display="flex" alignItems="center">
                            {[...Array(star)].map((_, i) => (
                              <StarIcon key={i} sx={{ fontSize: 16, color: '#ffc107' }} />
                            ))}
                            <Typography sx={{ ml: 1 }}>{star} Yıldız</Typography>
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Check-in Saati"
                    type="time"
                    value={formData.check_in_time}
                    onChange={(e) => handleInputChange('check_in_time', e.target.value)}
                  />
                </Grid>
                
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Check-out Saati"
                    type="time"
                    value={formData.check_out_time}
                    onChange={(e) => handleInputChange('check_out_time', e.target.value)}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Otel Açıklaması"
                    multiline
                    rows={3}
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Otel hakkında kısa bir açıklama yazın..."
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Alt Bölüm - Olanaklar ve Oda Tipleri */}
      <Grid container spacing={3} mt={2}>
        {/* Olanaklar */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                🏊 Otel Olanakları
              </Typography>

              <Box display="flex" flexWrap="wrap" gap={1}>
                {amenitiesList.map((amenity) => (
                  <Chip
                    key={amenity}
                    label={amenity}
                    onClick={() => handleAmenityToggle(amenity)}
                    color={formData.amenities.includes(amenity) ? 'primary' : 'default'}
                    variant={formData.amenities.includes(amenity) ? 'filled' : 'outlined'}
                    sx={{ cursor: 'pointer' }}
                  />
                ))}
              </Box>

              <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block' }}>
                Seçili olanaklar: {formData.amenities.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Oda Tipleri */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h6" fontWeight="600">
                  🛏️ Oda Tipleri
                </Typography>
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<AddIcon />}
                  onClick={() => setRoomDialogOpen(true)}
                >
                  Oda Ekle
                </Button>
              </Box>

              <List>
                {formData.room_types.map((room, index) => (
                  <ListItem key={room.id || index} divider>
                    <RoomIcon sx={{ mr: 2, color: 'primary.main' }} />
                    <ListItemText
                      primary={room.name}
                      secondary={`${room.quantity} oda • ₺${room.price}/gece`}
                    />
                    <ListItemSecondaryAction>
                      <IconButton
                        edge="end"
                        onClick={() => handleDeleteRoom(room.id)}
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
                {formData.room_types.length === 0 && (
                  <Typography variant="body2" color="text.secondary" textAlign="center" py={2}>
                    Henüz oda tipi eklenmemiş
                  </Typography>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Kaydet Butonu */}
      <Box mt={4} textAlign="center">
        <Button
          variant="contained"
          size="large"
          startIcon={<SaveIcon />}
          onClick={handleSave}
          disabled={saving}
          sx={{
            px: 4,
            py: 1.5,
            background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #388e3c 0%, #2e7d32 100%)',
            }
          }}
        >
          {saving ? 'Kaydediliyor...' : 'Otel Bilgilerini Kaydet'}
        </Button>
      </Box>

      {/* Oda Ekleme Dialog'u */}
      <Dialog open={roomDialogOpen} onClose={() => setRoomDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Yeni Oda Tipi Ekle</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Oda Tipi Adı"
                value={newRoom.name}
                onChange={(e) => setNewRoom(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Örn: Standart Oda, Deluxe Oda, Suite"
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Oda Sayısı"
                type="number"
                value={newRoom.quantity}
                onChange={(e) => setNewRoom(prev => ({ ...prev, quantity: parseInt(e.target.value) || 0 }))}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Gecelik Fiyat (₺)"
                type="number"
                value={newRoom.price}
                onChange={(e) => setNewRoom(prev => ({ ...prev, price: parseInt(e.target.value) || 0 }))}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRoomDialogOpen(false)}>İptal</Button>
          <Button onClick={handleAddRoom} variant="contained">Ekle</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default HotelSettings;
