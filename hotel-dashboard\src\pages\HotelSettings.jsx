import { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Alert,
  Chip,
  Avatar,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Slider,
  InputAdornment,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tabs,
  Tab
} from '@mui/material';
import {
  Hotel as HotelIcon,
  Save as SaveIcon,
  Edit as EditIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Room as RoomIcon,
  Star as StarIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  Language as WebsiteIcon,
  Business as BusinessIcon,
  ExpandMore as ExpandMoreIcon,
  AccessTime as TimeIcon,
  AttachMoney as MoneyIcon,
  People as PeopleIcon,
  Straighten as SizeIcon,
  Bed as BedIcon,
  Landscape as ViewIcon
} from '@mui/icons-material';
import { useApi } from '../contexts/ApiContext';
import { useAuth } from '../contexts/AuthContext';

const HotelSettings = () => {
  const { getHotelConfig, updateHotelConfig, currentHotelId } = useApi();
  const { user } = useAuth();
  const [hotelConfig, setHotelConfig] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [roomDialogOpen, setRoomDialogOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);

  // Yeni oda için state
  const [newRoom, setNewRoom] = useState({
    name: '',
    capacity: '2',
    price: '800',
    features: '',
    totalRoomsOfType: 10,
    bedType: 'Çift Kişilik',
    viewType: 'Şehir Manzaralı',
    sizeSquareMeters: 25,
    roomAmenities: 'Klima, TV, Wi-Fi',
    minStayNights: 1,
    maxGuests: 2,
    imageUrls: []
  });

  // Unity'den gelen veri yapısına uygun form state
  const [formData, setFormData] = useState({
    hotelId: '',
    hotelName: '',
    hotelCity: '',
    hotelDescription: '',
    hotelAddress: '',
    hotelPhone: '',
    hotelEmail: '',
    hotelCheckInTime: '14:00',
    hotelCheckOutTime: '12:00',
    aiResponseDelaySeconds: 2.5,
    hotelPolicy: '',
    hotelAmenities: '',
    paymentMethods: '',
    nearbyAttractions: '',
    contactHours: '24/7',
    hotelLatitude: '',
    hotelLongitude: '',
    placeId: '',
    gmbAccountId: '',
    gmbLocationId: '',
    roomTypes: []
  });

  // Seçenekler
  const amenitiesList = [
    'WiFi', 'Spa', 'Fitness', 'Restaurant', 'Bar', 'Parking',
    'Pool', 'Concierge', 'Room Service', 'Laundry', 'Airport Shuttle', 'Pet Friendly',
    'Conference Room', 'Business Center', 'Sauna', 'Jacuzzi', 'Garden', 'Terrace'
  ];

  const bedTypes = ['Tek Kişilik', 'Çift Kişilik', 'King Size', 'Queen Size', 'Ranza', 'Yatak Odası + Salon'];
  const viewTypes = ['Şehir Manzaralı', 'Deniz Manzaralı', 'Dağ Manzaralı', 'Bahçe Manzaralı', 'Avlu Manzaralı', 'Manzarasız'];
  const paymentMethodsList = ['Nakit', 'Kredi Kartı', 'Havale', 'PayPal', 'Kripto Para', 'Taksit'];

  useEffect(() => {
    fetchHotelConfig();
  }, [currentHotelId]);

  const fetchHotelConfig = async () => {
    try {
      setLoading(true);
      const config = await getHotelConfig(currentHotelId);
      setHotelConfig(config);

      // Form data'yı Unity veri yapısına uygun şekilde doldur
      setFormData({
        hotelId: config.hotelId || currentHotelId,
        hotelName: config.hotelName || '',
        hotelCity: config.hotelCity || '',
        hotelDescription: config.hotelDescription || '',
        hotelAddress: config.hotelAddress || '',
        hotelPhone: config.hotelPhone || '',
        hotelEmail: config.hotelEmail || '',
        hotelCheckInTime: config.hotelCheckInTime || '14:00',
        hotelCheckOutTime: config.hotelCheckOutTime || '12:00',
        aiResponseDelaySeconds: config.aiResponseDelaySeconds || 2.5,
        hotelPolicy: config.hotelPolicy || '',
        hotelAmenities: config.hotelAmenities || '',
        paymentMethods: config.paymentMethods || '',
        nearbyAttractions: config.nearbyAttractions || '',
        contactHours: config.contactHours || '24/7',
        hotelLatitude: config.hotelLatitude || '',
        hotelLongitude: config.hotelLongitude || '',
        placeId: config.placeId || '',
        gmbAccountId: config.gmbAccountId || '',
        gmbLocationId: config.gmbLocationId || '',
        roomTypes: config.roomTypes || []
      });
    } catch (err) {
      console.error('Hotel config fetch error:', err);
      setError('Otel konfigürasyonu yüklenirken hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAmenityToggle = (amenity) => {
    const currentAmenities = formData.hotelAmenities.split(', ').filter(a => a.trim());
    const updatedAmenities = currentAmenities.includes(amenity)
      ? currentAmenities.filter(a => a !== amenity)
      : [...currentAmenities, amenity];

    setFormData(prev => ({
      ...prev,
      hotelAmenities: updatedAmenities.join(', ')
    }));
  };

  const handlePaymentMethodToggle = (method) => {
    const currentMethods = formData.paymentMethods.split(', ').filter(m => m.trim());
    const updatedMethods = currentMethods.includes(method)
      ? currentMethods.filter(m => m !== method)
      : [...currentMethods, method];

    setFormData(prev => ({
      ...prev,
      paymentMethods: updatedMethods.join(', ')
    }));
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      // Server'daki endpoint'e uygun format
      await updateHotelConfig(formData);
      setSuccess('Otel konfigürasyonu başarıyla güncellendi!');

      // 3 saniye sonra success mesajını temizle
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Hotel config update error:', err);
      setError('Otel konfigürasyonu güncellenirken hata oluştu.');
    } finally {
      setSaving(false);
    }
  };

  const handleAddRoom = () => {
    if (newRoom.name && newRoom.totalRoomsOfType > 0) {
      setFormData(prev => ({
        ...prev,
        roomTypes: [...prev.roomTypes, { ...newRoom }]
      }));
      setNewRoom({
        name: '',
        capacity: '2',
        price: '800',
        features: '',
        totalRoomsOfType: 10,
        bedType: 'Çift Kişilik',
        viewType: 'Şehir Manzaralı',
        sizeSquareMeters: 25,
        roomAmenities: 'Klima, TV, Wi-Fi',
        minStayNights: 1,
        maxGuests: 2,
        imageUrls: []
      });
      setRoomDialogOpen(false);
    }
  };

  const handleDeleteRoom = (index) => {
    setFormData(prev => ({
      ...prev,
      roomTypes: prev.roomTypes.filter((_, i) => i !== index)
    }));
  };

  const handleRoomInputChange = (field, value) => {
    setNewRoom(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Otel konfigürasyonu yükleniyor...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={4}>
        <Box display="flex" alignItems="center" mb={2}>
          <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
            <HotelIcon />
          </Avatar>
          <Box>
            <Typography variant="h4" fontWeight="600">
              Otel Konfigürasyonu
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Otel bilgilerinizi düzenleyin ve servera kaydedin
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Alerts */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      {/* Tab Navigation */}
      <Card sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={(e, newValue) => setTabValue(newValue)}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label="🏨 Temel Bilgiler" />
          <Tab label="⚙️ Otel Ayarları" />
          <Tab label="🤖 AI Ayarları" />
          <Tab label="🛏️ Oda Tipleri" />
          <Tab label="📍 Konum & Entegrasyon" />
        </Tabs>
      </Card>

      {/* Tab Content */}
      {tabValue === 0 && (
        <Grid container spacing={3}>
          {/* Temel Bilgiler */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  🏨 Temel Bilgiler
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Otel Adı"
                      value={formData.hotelName}
                      onChange={(e) => handleInputChange('hotelName', e.target.value)}
                      InputProps={{
                        startAdornment: <BusinessIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Şehir"
                      value={formData.hotelCity}
                      onChange={(e) => handleInputChange('hotelCity', e.target.value)}
                      InputProps={{
                        startAdornment: <LocationIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Adres"
                      multiline
                      rows={2}
                      value={formData.hotelAddress}
                      onChange={(e) => handleInputChange('hotelAddress', e.target.value)}
                      InputProps={{
                        startAdornment: <LocationIcon sx={{ mr: 1, color: 'text.secondary', alignSelf: 'flex-start', mt: 1 }} />
                      }}
                    />
                  </Grid>

                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Telefon"
                      value={formData.hotelPhone}
                      onChange={(e) => handleInputChange('hotelPhone', e.target.value)}
                      InputProps={{
                        startAdornment: <PhoneIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>

                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Email"
                      type="email"
                      value={formData.hotelEmail}
                      onChange={(e) => handleInputChange('hotelEmail', e.target.value)}
                      InputProps={{
                        startAdornment: <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Otel Açıklaması"
                      multiline
                      rows={3}
                      value={formData.hotelDescription}
                      onChange={(e) => handleInputChange('hotelDescription', e.target.value)}
                      placeholder="Otel hakkında detaylı açıklama yazın..."
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* İletişim ve Saatler */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  ⏰ İletişim ve Saatler
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Check-in Saati"
                      type="time"
                      value={formData.hotelCheckInTime}
                      onChange={(e) => handleInputChange('hotelCheckInTime', e.target.value)}
                      InputProps={{
                        startAdornment: <TimeIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>

                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Check-out Saati"
                      type="time"
                      value={formData.hotelCheckOutTime}
                      onChange={(e) => handleInputChange('hotelCheckOutTime', e.target.value)}
                      InputProps={{
                        startAdornment: <TimeIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="İletişim Saatleri"
                      value={formData.contactHours}
                      onChange={(e) => handleInputChange('contactHours', e.target.value)}
                      placeholder="Örn: 24/7, 09:00-22:00"
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Otel Politikası"
                      multiline
                      rows={4}
                      value={formData.hotelPolicy}
                      onChange={(e) => handleInputChange('hotelPolicy', e.target.value)}
                      placeholder="Otel kuralları ve politikaları..."
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Tab 2 - Otel Ayarları */}
      {tabValue === 1 && (
        <Grid container spacing={3}>
          {/* Olanaklar */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  🏊 Otel Olanakları
                </Typography>

                <Box display="flex" flexWrap="wrap" gap={1} mb={3}>
                  {amenitiesList.map((amenity) => (
                    <Chip
                      key={amenity}
                      label={amenity}
                      onClick={() => handleAmenityToggle(amenity)}
                      color={formData.hotelAmenities.includes(amenity) ? 'primary' : 'default'}
                      variant={formData.hotelAmenities.includes(amenity) ? 'filled' : 'outlined'}
                      sx={{ cursor: 'pointer' }}
                    />
                  ))}
                </Box>

                <TextField
                  fullWidth
                  label="Olanaklar (Virgülle Ayrılmış)"
                  value={formData.hotelAmenities}
                  onChange={(e) => handleInputChange('hotelAmenities', e.target.value)}
                  placeholder="WiFi, Spa, Fitness, Restaurant..."
                  multiline
                  rows={2}
                />
              </CardContent>
            </Card>
          </Grid>

          {/* Ödeme Yöntemleri */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  💳 Ödeme Yöntemleri
                </Typography>

                <Box display="flex" flexWrap="wrap" gap={1} mb={3}>
                  {paymentMethodsList.map((method) => (
                    <Chip
                      key={method}
                      label={method}
                      onClick={() => handlePaymentMethodToggle(method)}
                      color={formData.paymentMethods.includes(method) ? 'secondary' : 'default'}
                      variant={formData.paymentMethods.includes(method) ? 'filled' : 'outlined'}
                      sx={{ cursor: 'pointer' }}
                    />
                  ))}
                </Box>

                <TextField
                  fullWidth
                  label="Ödeme Yöntemleri"
                  value={formData.paymentMethods}
                  onChange={(e) => handleInputChange('paymentMethods', e.target.value)}
                  placeholder="Nakit, Kredi Kartı, Havale..."
                  multiline
                  rows={2}
                />
              </CardContent>
            </Card>
          </Grid>

          {/* Yakın Yerler */}
          <Grid item xs={12}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  📍 Yakın Yerler ve Cazibe Merkezleri
                </Typography>

                <TextField
                  fullWidth
                  label="Yakın Yerler"
                  value={formData.nearbyAttractions}
                  onChange={(e) => handleInputChange('nearbyAttractions', e.target.value)}
                  placeholder="Zorlu Center, Vadistanbul, Optimum Outlet..."
                  multiline
                  rows={3}
                />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Tab 3 - AI Ayarları */}
      {tabValue === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  🤖 AI Asistan Ayarları
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" sx={{ mb: 2 }}>
                      AI Yanıt Gecikmesi (Saniye)
                    </Typography>
                    <Slider
                      value={formData.aiResponseDelaySeconds}
                      onChange={(e, value) => handleInputChange('aiResponseDelaySeconds', value)}
                      min={0.5}
                      max={10}
                      step={0.5}
                      marks={[
                        { value: 0.5, label: '0.5s' },
                        { value: 2.5, label: '2.5s' },
                        { value: 5, label: '5s' },
                        { value: 10, label: '10s' }
                      ]}
                      valueLabelDisplay="on"
                    />
                    <Typography variant="caption" color="text.secondary">
                      AI'nın yanıt vermeden önce bekleyeceği süre
                    </Typography>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Mevcut AI Gecikmesi"
                      value={`${formData.aiResponseDelaySeconds} saniye`}
                      disabled
                      InputProps={{
                        startAdornment: <TimeIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Tab 4 - Oda Tipleri */}
      {tabValue === 3 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                  <Typography variant="h6" fontWeight="600">
                    🛏️ Oda Tipleri Yönetimi
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => setRoomDialogOpen(true)}
                  >
                    Yeni Oda Tipi Ekle
                  </Button>
                </Box>

                <Grid container spacing={2}>
                  {formData.roomTypes.map((room, index) => (
                    <Grid item xs={12} md={6} lg={4} key={index}>
                      <Card variant="outlined">
                        <CardContent sx={{ p: 2 }}>
                          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                            <Typography variant="h6" fontWeight="600">
                              {room.name}
                            </Typography>
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleDeleteRoom(index)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Box>

                          <Grid container spacing={1}>
                            <Grid item xs={6}>
                              <Typography variant="caption" color="text.secondary">Kapasite</Typography>
                              <Typography variant="body2">{room.capacity} kişi</Typography>
                            </Grid>
                            <Grid item xs={6}>
                              <Typography variant="caption" color="text.secondary">Fiyat</Typography>
                              <Typography variant="body2">₺{room.price}/gece</Typography>
                            </Grid>
                            <Grid item xs={6}>
                              <Typography variant="caption" color="text.secondary">Oda Sayısı</Typography>
                              <Typography variant="body2">{room.totalRoomsOfType} oda</Typography>
                            </Grid>
                            <Grid item xs={6}>
                              <Typography variant="caption" color="text.secondary">Boyut</Typography>
                              <Typography variant="body2">{room.sizeSquareMeters} m²</Typography>
                            </Grid>
                            <Grid item xs={12}>
                              <Typography variant="caption" color="text.secondary">Yatak Tipi</Typography>
                              <Typography variant="body2">{room.bedType}</Typography>
                            </Grid>
                            <Grid item xs={12}>
                              <Typography variant="caption" color="text.secondary">Manzara</Typography>
                              <Typography variant="body2">{room.viewType}</Typography>
                            </Grid>
                            <Grid item xs={12}>
                              <Typography variant="caption" color="text.secondary">Olanaklar</Typography>
                              <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
                                {room.roomAmenities}
                              </Typography>
                            </Grid>
                          </Grid>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}

                  {formData.roomTypes.length === 0 && (
                    <Grid item xs={12}>
                      <Box textAlign="center" py={4}>
                        <RoomIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                        <Typography variant="h6" color="text.secondary">
                          Henüz oda tipi eklenmemiş
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Yeni oda tipi eklemek için yukarıdaki butona tıklayın
                        </Typography>
                      </Box>
                    </Grid>
                  )}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Tab 5 - Konum & Entegrasyon */}
      {tabValue === 4 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  📍 Konum Bilgileri
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Enlem (Latitude)"
                      value={formData.hotelLatitude}
                      onChange={(e) => handleInputChange('hotelLatitude', e.target.value)}
                      placeholder="41.0082"
                    />
                  </Grid>

                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Boylam (Longitude)"
                      value={formData.hotelLongitude}
                      onChange={(e) => handleInputChange('hotelLongitude', e.target.value)}
                      placeholder="28.9784"
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Google Places ID"
                      value={formData.placeId}
                      onChange={(e) => handleInputChange('placeId', e.target.value)}
                      placeholder="ChIJVVVVVVVVVVVVVVVVVVVVVV"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                  🔗 Google My Business Entegrasyonu
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="GMB Account ID"
                      value={formData.gmbAccountId}
                      onChange={(e) => handleInputChange('gmbAccountId', e.target.value)}
                      placeholder="gmb_account_123"
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="GMB Location ID"
                      value={formData.gmbLocationId}
                      onChange={(e) => handleInputChange('gmbLocationId', e.target.value)}
                      placeholder="gmb_location_456"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Kaydet Butonu */}
      <Box mt={4} textAlign="center">
        <Button
          variant="contained"
          size="large"
          startIcon={<SaveIcon />}
          onClick={handleSave}
          disabled={saving}
          sx={{
            px: 4,
            py: 1.5,
            background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #388e3c 0%, #2e7d32 100%)',
            }
          }}
        >
          {saving ? 'Kaydediliyor...' : 'Otel Konfigürasyonunu Kaydet'}
        </Button>
      </Box>

      {/* Oda Ekleme Dialog'u */}
      <Dialog open={roomDialogOpen} onClose={() => setRoomDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Yeni Oda Tipi Ekle</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Oda Tipi Adı"
                value={newRoom.name}
                onChange={(e) => handleRoomInputChange('name', e.target.value)}
                placeholder="Örn: Standart Oda, Deluxe Oda, Suite"
              />
            </Grid>

            <Grid item xs={6} md={3}>
              <TextField
                fullWidth
                label="Kapasite"
                value={newRoom.capacity}
                onChange={(e) => handleRoomInputChange('capacity', e.target.value)}
                InputProps={{
                  startAdornment: <PeopleIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>

            <Grid item xs={6} md={3}>
              <TextField
                fullWidth
                label="Gecelik Fiyat (₺)"
                value={newRoom.price}
                onChange={(e) => handleRoomInputChange('price', e.target.value)}
                InputProps={{
                  startAdornment: <MoneyIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>

            <Grid item xs={6} md={3}>
              <TextField
                fullWidth
                label="Oda Sayısı"
                type="number"
                value={newRoom.totalRoomsOfType}
                onChange={(e) => handleRoomInputChange('totalRoomsOfType', parseInt(e.target.value) || 0)}
              />
            </Grid>

            <Grid item xs={6} md={3}>
              <TextField
                fullWidth
                label="Boyut (m²)"
                type="number"
                value={newRoom.sizeSquareMeters}
                onChange={(e) => handleRoomInputChange('sizeSquareMeters', parseFloat(e.target.value) || 0)}
                InputProps={{
                  startAdornment: <SizeIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>

            <Grid item xs={6} md={3}>
              <TextField
                fullWidth
                label="Min. Konaklama (Gece)"
                type="number"
                value={newRoom.minStayNights}
                onChange={(e) => handleRoomInputChange('minStayNights', parseInt(e.target.value) || 1)}
              />
            </Grid>

            <Grid item xs={6} md={3}>
              <TextField
                fullWidth
                label="Max. Misafir"
                type="number"
                value={newRoom.maxGuests}
                onChange={(e) => handleRoomInputChange('maxGuests', parseInt(e.target.value) || 2)}
              />
            </Grid>

            <Grid item xs={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>Yatak Tipi</InputLabel>
                <Select
                  value={newRoom.bedType}
                  onChange={(e) => handleRoomInputChange('bedType', e.target.value)}
                >
                  {bedTypes.map(type => (
                    <MenuItem key={type} value={type}>{type}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>Manzara Tipi</InputLabel>
                <Select
                  value={newRoom.viewType}
                  onChange={(e) => handleRoomInputChange('viewType', e.target.value)}
                >
                  {viewTypes.map(type => (
                    <MenuItem key={type} value={type}>{type}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Oda Özellikleri"
                value={newRoom.features}
                onChange={(e) => handleRoomInputChange('features', e.target.value)}
                placeholder="Oda hakkında genel açıklama..."
                multiline
                rows={2}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Oda Olanakları"
                value={newRoom.roomAmenities}
                onChange={(e) => handleRoomInputChange('roomAmenities', e.target.value)}
                placeholder="Klima, TV, Wi-Fi, Minibar..."
                multiline
                rows={2}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRoomDialogOpen(false)}>İptal</Button>
          <Button onClick={handleAddRoom} variant="contained">Oda Tipini Ekle</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default HotelSettings;

      {/* Alt Bölüm - Olanaklar ve Oda Tipleri */}
      <Grid container spacing={3} mt={2}>
        {/* Olanaklar */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                🏊 Otel Olanakları
              </Typography>

              <Box display="flex" flexWrap="wrap" gap={1}>
                {amenitiesList.map((amenity) => (
                  <Chip
                    key={amenity}
                    label={amenity}
                    onClick={() => handleAmenityToggle(amenity)}
                    color={formData.amenities.includes(amenity) ? 'primary' : 'default'}
                    variant={formData.amenities.includes(amenity) ? 'filled' : 'outlined'}
                    sx={{ cursor: 'pointer' }}
                  />
                ))}
              </Box>

              <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block' }}>
                Seçili olanaklar: {formData.amenities.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Oda Tipleri */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h6" fontWeight="600">
                  🛏️ Oda Tipleri
                </Typography>
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<AddIcon />}
                  onClick={() => setRoomDialogOpen(true)}
                >
                  Oda Ekle
                </Button>
              </Box>

              <List>
                {formData.room_types.map((room, index) => (
                  <ListItem key={room.id || index} divider>
                    <RoomIcon sx={{ mr: 2, color: 'primary.main' }} />
                    <ListItemText
                      primary={room.name}
                      secondary={`${room.quantity} oda • ₺${room.price}/gece`}
                    />
                    <ListItemSecondaryAction>
                      <IconButton
                        edge="end"
                        onClick={() => handleDeleteRoom(room.id)}
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
                {formData.room_types.length === 0 && (
                  <Typography variant="body2" color="text.secondary" textAlign="center" py={2}>
                    Henüz oda tipi eklenmemiş
                  </Typography>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Kaydet Butonu */}
      <Box mt={4} textAlign="center">
        <Button
          variant="contained"
          size="large"
          startIcon={<SaveIcon />}
          onClick={handleSave}
          disabled={saving}
          sx={{
            px: 4,
            py: 1.5,
            background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #388e3c 0%, #2e7d32 100%)',
            }
          }}
        >
          {saving ? 'Kaydediliyor...' : 'Otel Bilgilerini Kaydet'}
        </Button>
      </Box>

      {/* Oda Ekleme Dialog'u */}
      <Dialog open={roomDialogOpen} onClose={() => setRoomDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Yeni Oda Tipi Ekle</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Oda Tipi Adı"
                value={newRoom.name}
                onChange={(e) => setNewRoom(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Örn: Standart Oda, Deluxe Oda, Suite"
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Oda Sayısı"
                type="number"
                value={newRoom.quantity}
                onChange={(e) => setNewRoom(prev => ({ ...prev, quantity: parseInt(e.target.value) || 0 }))}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Gecelik Fiyat (₺)"
                type="number"
                value={newRoom.price}
                onChange={(e) => setNewRoom(prev => ({ ...prev, price: parseInt(e.target.value) || 0 }))}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRoomDialogOpen(false)}>İptal</Button>
          <Button onClick={handleAddRoom} variant="contained">Ekle</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default HotelSettings;
