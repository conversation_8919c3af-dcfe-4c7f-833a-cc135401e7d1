# app/ai/executors.py (<PERSON><PERSON>, Stateless ve Loglama Entegreli Hali)

import uuid
import time
import json
import sqlite3
from datetime import datetime

# <PERSON><PERSON><PERSON><PERSON> modülleri ve nesneleri içe aktar
from app import app_logger # Merkezi logger'ımızı import ediyoruz.
from app.database import get_db_connection
from app.utils import validate_and_format_date, send_reservation_email, broadcast_socketio_event
from app.config import STATUS_HUMAN_CONFIRMED, STATUS_AI_PENDING_HUMAN
from app.places_service import get_nearby_places, get_directions_to_destination
# Redis ile ilgili yardımcı fonksiyonları içe aktar
from app.utils import get_session_from_redis, save_session_to_redis

# --- ARAÇ UYGULAYICILARI (EXECUTORS) ---

def execute_get_room_availability_impl(room_type: str, check_in_date_str: str, check_out_date_str: str, session_config: dict, session_id: str):
    """
    Belirtilen tarihler arasında bir oda tipinin müsait olup olmadığını kontrol eder.
    Bu fonksiyon, "stateless" mimariyle %100 uyumludur. Müsaitlik kontrolünün
    sonucunu, oturumun devamı için Redis'e kaydeder.
    """
    app_logger.info(f"FUNC_CALL (get_room_availability): Oda='{room_type}', Giriş='{check_in_date_str}', Çıkış='{check_out_date_str}', Session='{session_id[:8]}'")

    parsed_check_in = validate_and_format_date(check_in_date_str)
    parsed_check_out = validate_and_format_date(check_out_date_str)

    if not parsed_check_in or not parsed_check_out:
        return {"error": "Tarihler geçersiz veya anlaşılamadı.", "message_to_user": "Lütfen tarihleri YYYY-AA-GG formatında (örneğin 2024-12-31) belirtir misiniz?"}

    try:
        check_in_dt = datetime.strptime(parsed_check_in, '%Y-%m-%d').date()
        check_out_dt = datetime.strptime(parsed_check_out, '%Y-%m-%d').date()
        if check_in_dt < datetime.now().date():
            return {"error": "Giriş tarihi geçmişte.", "message_to_user": f"Giriş tarihi ({parsed_check_in}) geçmişte kalmış. Lütfen bugün veya gelecek için bir tarih seçin."}
        if check_out_dt <= check_in_dt:
            return {"error": "Çıkış tarihi, giriş tarihinden önce veya aynı gün.", "message_to_user": f"Çıkış tarihi ({parsed_check_out}), giriş tarihinden ({parsed_check_in}) sonra olmalıdır. Lütfen geçerli bir tarih aralığı belirtin."}
    except ValueError:
        return {"error": "Tarih formatı hatası.", "message_to_user": "Tarih formatında bir sorun var. Lütfen YYYY-AA-GG formatını kullanın."}

    # session_config, o anki istekle birlikte gelen "taze" yapılandırma verisidir.
    hotel_config_key = session_config.get('hotelId')
    if not hotel_config_key:
        return {"available": False, "reason": "Otel kimliği eksik.", "message_to_user": "Sistemsel bir sorun nedeniyle otel kimliğine ulaşılamıyor, bu yüzden müsaitlik kontrolü yapamıyorum."}
    
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT total_quantity FROM room_types_inventory WHERE hotel_config_key = ? AND room_type_name = ?", (hotel_config_key, room_type))
        inv_item = cursor.fetchone()
        
        total_quantity = inv_item['total_quantity'] if inv_item else 0
        if not inv_item:
            room_conf = next((r for r in session_config.get('roomTypes', []) if isinstance(r, dict) and r.get('name', '').lower() == room_type.lower()), None)
            if room_conf: total_quantity = int(room_conf.get('totalRoomsOfType', 0))
            else: return {"available": False, "reason": f"'{room_type}' oda tipi sistemde tanımlı değil.", "message_to_user": f"Maalesef '{room_type}' adında bir oda tipimiz bulunmuyor."}
        
        if total_quantity <= 0: return {"available": False, "reason": "Envanter sıfır.", "message_to_user": f"Maalesef, '{room_type}' için şu anda hiç odamız bulunmuyor."}
        
        conflicting_query = f"""
            SELECT COUNT(id) as count 
            FROM reservations 
            WHERE 
                hotel_id_key = ? AND 
                room_type = ? AND 
                status IN ('{STATUS_HUMAN_CONFIRMED}', '{STATUS_AI_PENDING_HUMAN}') AND 
                check_in_date < ? AND 
                check_out_date > ?
        """
        cursor.execute(conflicting_query, (hotel_config_key, room_type, parsed_check_out, parsed_check_in))

        conflicting_count = cursor.fetchone()['count']

    except sqlite3.Error as e_db:
        app_logger.exception("get_room_availability içinde veritabanı hatası:")
        return {"available": False, "reason": "Veritabanı hatası.", "message_to_user": "Müsaitlik kontrolü sırasında bir veritabanı sorunu oluştu."}
    finally:
        if conn: conn.close()

    available_actual = total_quantity - conflicting_count
    if available_actual > 0:
        room_conf_details = next((r for r in session_config.get('roomTypes', []) if isinstance(r, dict) and r.get('name', '').lower() == room_type.lower()), None)
        price = room_conf_details.get('price', "Fiyat için sorunuz") if room_conf_details else "Fiyat için sorunuz"
        features = room_conf_details.get('features', "Standart özellikler.") if room_conf_details else "Standart özellikler."
        num_nights = (check_out_dt - check_in_dt).days
        
        # === KRİTİK ADIM: Başarılı müsaitlik kontrolü bağlamını Redis'e kaydet ===
        current_session_data = get_session_from_redis(session_id)
        if current_session_data:
            current_session_data['last_availability_check'] = {"room_type": room_type, "check_in_date": parsed_check_in, "check_out_date": parsed_check_out, "price_per_night": price, "features": features, "num_nights": num_nights, "available_room_count_at_check": available_actual}
            current_session_data['last_activity_time'] = time.time()
            save_session_to_redis(session_id, current_session_data)
            app_logger.info(f"Müsaitlik bağlamı Redis'e kaydedildi. Session: {session_id[:8]}")

        return {"available": True, "price_per_night": price, "features": features, "check_in_date": parsed_check_in, "check_out_date": parsed_check_out, "num_nights": num_nights, "available_room_count": available_actual, "message_to_user": f"Harika haber! {room_type} odamız için {parsed_check_in} giriş ve {parsed_check_out} çıkış ({num_nights} gece) tarihlerinde {available_actual} adet müsait yerimiz görünüyor. Gecelik ücreti {price}. Rezervasyonu onaylamak isterseniz, tam adınızı ve telefon numaranızı alabilir miyim?"}
    else:
        return {"available": False, "reason": "Belirtilen tarihlerde oda dolu.", "check_in_date": parsed_check_in, "check_out_date": parsed_check_out, "available_room_count": 0, "message_to_user": f"Maalesef, '{room_type}' için {parsed_check_in} ile {parsed_check_out} tarihleri arasında hiç müsait yerimiz kalmamış. Farklı bir tarih veya oda tipi denemek ister misiniz?"}
    

def execute_get_room_details_impl(room_type: str, session_config: dict, session_id: str = None):
    """
    Bir oda tipinin detaylarını (kapasite, fiyat, özellikler vb.) döndürür.
    Bu fonksiyon, artık 'active_chat_sessions' sözlüğüne erişmez ve
    tamamen "stateless" çalışır. Gerekli tüm bilgiler parametrelerden gelir.
    """
    app_logger.info(f"FUNC_CALL (get_room_details): Oda='{room_type}', Session='{session_id[:8] if session_id else 'N/A'}'")
    
    if not session_config or 'roomTypes' not in session_config:
        return {"error": "Otel konfigürasyonu eksik.", "message_to_user": "Oda bilgileri yüklenemedi."}
    
    otel_rooms_config = session_config.get('roomTypes', [])
    found_room_config = next((r for r in otel_rooms_config if isinstance(r, dict) and r.get('name', '').lower() == room_type.lower()), None)
    
    if not found_room_config:
        available_rooms = [r.get('name') for r in otel_rooms_config if isinstance(r, dict) and r.get('name')]
        return {"error": f"'{room_type}' bulunamadı.", "message_to_user": f"'{room_type}' adında bir odamız yok. Mevcut oda tiplerimiz: {', '.join(available_rooms) if available_rooms else 'Hiç yok'}."}

    # === DÜZELTME: hotel_config_key'i artık RAM'deki sözlükten değil,
    # doğrudan o anki istekle gelen session_config'den alıyoruz.
    hotel_config_key = session_config.get('hotelId')
    
    total_units_from_db_str = found_room_config.get('totalRoomsOfType', '?')
    if hotel_config_key:
        conn_inv = None
        try:
            conn_inv = get_db_connection()
            cursor_inv = conn_inv.cursor()
            cursor_inv.execute("SELECT total_quantity FROM room_types_inventory WHERE hotel_config_key = ? AND room_type_name = ?", (hotel_config_key, found_room_config.get('name')))
            inv_item = cursor_inv.fetchone()
            if inv_item: total_units_from_db_str = str(inv_item['total_quantity'])
        except sqlite3.Error as e_inv_db:
            app_logger.error(f"HATA (Oda Detayı - Envanter DB): {e_inv_db}")
        finally:
            if conn_inv: conn_inv.close()

    image_urls = found_room_config.get('imageUrls', [])
    has_images = bool(image_urls)
    
    tool_response_to_ai = {
        "room_type_name": found_room_config.get('name'), "capacity_description": found_room_config.get('capacity', 'N/A'),
        "price_info": found_room_config.get('price', 'Müsaitlik sorgulayın'), "features_general": found_room_config.get('features', 'Standart özellikler'),
        "total_units_of_this_type": total_units_from_db_str, "bed_type_description": found_room_config.get('bedType', 'N/A'),
        "view_description": found_room_config.get('viewType', 'N/A'), "size_sqm": found_room_config.get('sizeSquareMeters', 0),
        "room_specific_amenities": found_room_config.get('roomAmenities', 'N/A'),
        "minimum_stay_duration_nights": found_room_config.get('minStayNights', 1), "maximum_guests_allowed": found_room_config.get('maxGuests', '?'),
        "has_images": has_images, "image_urls_count": len(image_urls) if has_images else 0, "room_found": True
    }
    
    user_facing_message_suggestion = (f"'{found_room_config.get('name')}' odamız hakkında bazı bilgiler: Kapasite {found_room_config.get('capacity', 'N/A')} kişi, Yatak Tipi: {found_room_config.get('bedType', 'N/A')}, Manzara: {found_room_config.get('viewType', 'N/A')}, Büyüklük: {found_room_config.get('sizeSquareMeters', 'N/A')} m². Olanaklar: {found_room_config.get('roomAmenities', 'N/A')}. "
        + (f"Bu odanın görselleri de mevcut. " if has_images else "Bu oda için kayıtlı görsel bulunmuyor. ")
        + f"Kesin fiyat ve müsaitlik için lütfen konaklama tarihlerinizi belirtin.")
    
    tool_response_to_ai["message_to_user"] = user_facing_message_suggestion
    
    # === DÜZELTME: Artık RAM'de bir sözlüğe veri yazmıyoruz. Bu satır kaldırıldı.
    # if session_id and session_id in active_chat_sessions: active_chat_sessions[session_id]['last_tool_response_data'] = tool_response_to_ai
    
    return tool_response_to_ai


def execute_show_room_images_impl(room_type: str, session_config: dict, session_id: str):
    app_logger.info(f"FUNC_CALL (show_room_images): Oda='{room_type}', Session='{session_id[:8]}'")
    if not session_config or 'roomTypes' not in session_config: return {"success": False, "error": "Otel konfigürasyonu eksik.", "message_to_user": "Oda resimleri yüklenemedi."}
    
    otel_rooms_config = session_config.get('roomTypes', [])
    found_room_config = next((r for r in otel_rooms_config if isinstance(r, dict) and r.get('name', '').lower() == room_type.lower()), None)
    
    if not found_room_config: return {"success": False, "error": f"'{room_type}' bulunamadı.", "message_to_user": f"'{room_type}' adında odamız yok."}
    
    image_urls = found_room_config.get('imageUrls', [])
    app_logger.debug(f"DEBUG execute_show_room_images_impl: Oda '{room_type}' için bulunan resim URL'leri: {image_urls}")
    
    if not image_urls: return {"success": False, "images_found": False, "message_to_user": f"'{room_type}' için kayıtlı görsel bulunmuyor maalesef."}
    
    gallery_items = [{"url": url, "caption": f"{found_room_config.get('name', room_type)} Görsel {i+1}"} for i, url in enumerate(image_urls)]
    return {"success": True, "images_found": True, "message_to_user": f"Elbette, '{found_room_config.get('name', room_type)}' odamızın görselleri aşağıdadır:", "special_render_data": { "type": "gallery", "items": gallery_items } }


def execute_create_reservation_impl(guest_name: str, guest_surname: str, guest_phone: str, num_adults: int,
                                    guest_email: str = None, num_children: int = 0, special_requests: str = None,
                                    session_config: dict = None, session_id: str = None):
    """
    Kullanıcıdan alınan bilgilerle veritabanına yeni bir rezervasyon kaydı oluşturur.
    Bu fonksiyon, "stateless" mimariyle %100 uyumludur. Gerekli bağlamı
    (hangi oda, hangi tarihler) Redis'ten okur.
    """
    app_logger.info(f"FUNC_CALL (create_reservation): Misafir='{guest_name} {guest_surname}', Telefon='{guest_phone}', Session='{session_id[:8]}'")
    
    # --- Başlangıç Kontrolleri (Bu kısım zaten doğruydu) ---
    if not session_id:
        return {"success": False, "message_to_user": "Rezervasyon oluşturmak için oturum bilgisi eksik. Lütfen sohbeti yeniden başlatın."}
    
    session_data = get_session_from_redis(session_id)
    if not session_data:
        return {"success": False, "message_to_user": "Oturum süreniz dolmuş veya geçersiz. Rezervasyona devam etmek için lütfen müsaitlik sorgulayarak yeniden başlayın."}
        
    last_availability = session_data.get('last_availability_check')
    if not last_availability or not all(k in last_availability for k in ['room_type', 'check_in_date', 'check_out_date']):
        app_logger.warning(f"UYARI (create_reservation): Oturumda 'last_availability_check' bulunamadı veya eksik. Session: {session_id[:8]}")
        return {"success": False, "error": "Missing availability context", "message_to_user": "Elbette, rezervasyonu oluşturabilirim. Fakat devam etmeden önce, hangi tarihler ve hangi oda tipi için işlem yapacağımızı teyit etmemiz gerekiyor. Lütfen önce oda müsaitliği sorgulayarak başlayalım."}
        
    # --- Veri Çekme (Bu kısım da doğruydu) ---
    room_type = last_availability['room_type']
    check_in_date = last_availability['check_in_date']
    check_out_date = last_availability['check_out_date']
    price_per_night = last_availability.get('price_per_night', 'Sorunuz')
    num_nights = last_availability.get('num_nights', 0)
    hotel_id_key = session_data.get('hotel_config_key')
    hotel_name_str = session_data.get('hotelName', 'Otelimiz')
    
    if not hotel_id_key:
        app_logger.error(f"KRİTİK: create_reservation'da hotel_id_key Redis'ten alınamadı! Session: {session_id}")
        return {"success": False, "message_to_user": "Sistemsel bir hata nedeniyle rezervasyon oluşturulamadı (Otel Kimliği Eksik)."}

    # --- Veritabanı ve Güvenlik Kontrolü ---
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute('BEGIN')

        # === ANA DÜZELTME ve GÜVENLİK AĞI BURADA ===
        # Önce veritabanından toplam oda sayısını almayı dene
        cursor.execute("SELECT total_quantity FROM room_types_inventory WHERE hotel_config_key = ? AND room_type_name = ?", (hotel_id_key, room_type))
        inv_item = cursor.fetchone()
        
        total_quantity_db = 0
        if inv_item:
            total_quantity_db = inv_item['total_quantity']
        else:
            # EĞER VERİTABANINDA BULUNAMAZSA (örn: henüz senkronize olmadıysa), session_config'e (JSON dosyası) geri dön.
            # Bu, get_room_availability fonksiyonundaki mantıkla %100 aynı davranışı garanti eder.
            app_logger.warning(f"UYARI (CreateReservation-Fallback): Oda '{room_type}' envanter tablosunda bulunamadı. hotel_configs verisine fallback yapılıyor. Session: {session_id[:8]}")
            # session_config'i Redis'ten gelen session_data'dan almamız gerekiyor.
            current_session_config = session_data.get('config_data', {})
            room_conf = next((r for r in current_session_config.get('roomTypes', []) if isinstance(r, dict) and r.get('name', '').lower() == room_type.lower()), None)
            if room_conf:
                total_quantity_db = int(room_conf.get('totalRoomsOfType', 0))
        # ===============================================

        app_logger.info(f"BİLGİ (CreateReservation-Check): Oda '{room_type}' için toplam adet {total_quantity_db} olarak belirlendi.")

        # Artık %100 tutarlı olan sorgumuzla çakışan rezervasyonları sayalım.
        conflicting_query_in_transaction = f"""
            SELECT COUNT(id) as count 
            FROM reservations 
            WHERE 
                hotel_id_key = ? AND 
                room_type = ? AND 
                status IN ('{STATUS_HUMAN_CONFIRMED}', '{STATUS_AI_PENDING_HUMAN}') AND 
                check_in_date < ? AND 
                check_out_date > ?
        """
        cursor.execute(conflicting_query_in_transaction, (hotel_id_key, room_type, check_out_date, check_in_date))
        conflicting_count_db = cursor.fetchone()['count']
        
        app_logger.info(f"BİLGİ (CreateReservation-Check): Oda '{room_type}' için çakışan rezervasyon sayısı: {conflicting_count_db}.")

        # Nihai Güvenlik Kontrolü
        if total_quantity_db - conflicting_count_db <= 0:
            conn.rollback()
            app_logger.warning(f"UYARI (Yarış Durumu - CreateReservation): Rezervasyon engellendi. Oda '{room_type}' dolu. Toplam: {total_quantity_db}, Çakışan: {conflicting_count_db}. Session: {session_id[:8]}")
            return {"success": False, "message_to_user": "Çok üzgünüz, siz rezervasyonu tamamlarken seçtiğiniz oda tipi için son yer de doldu. Lütfen farklı bir tarih veya oda tipi için tekrar müsaitlik sorgular mısınız?"}
        
        # === REZERVASYONU KAYDETME (Bu kısım zaten doğruydu) ===
        total_price_str = "Hesaplanamadı"
        if isinstance(price_per_night, (int, float)) or (isinstance(price_per_night, str) and price_per_night.replace('.', '', 1).isdigit()):
            try:
                total_price = float(price_per_night) * num_nights
                total_price_str = f"{total_price:.2f}"
            except (ValueError, TypeError): pass

        reservation_id_str = f"REZ-{str(uuid.uuid4())[:8].upper()}"
        reservation_time_str = datetime.now().isoformat(timespec='seconds')
        
        db_payload = {
            "reservation_id": reservation_id_str, "room_type": room_type, "check_in_date": check_in_date,
            "check_out_date": check_out_date, "guest_name": guest_name, "guest_surname": guest_surname,
            "guest_email": guest_email, "guest_phone": guest_phone, "num_adults": int(num_adults),
            "num_children": int(num_children), "price_per_night_at_booking": str(price_per_night),
            "num_nights": num_nights, "total_price_at_booking": total_price_str,
            "hotel_name": hotel_name_str, "reservation_time": reservation_time_str,
            "status": STATUS_AI_PENDING_HUMAN, "session_id_at_booking": session_id,
            "last_status_update_time": reservation_time_str, "special_requests": special_requests,
            "hotel_id_key": hotel_id_key
        }
        
        cols, placeholders = ', '.join(db_payload.keys()), ', '.join(['?'] * len(db_payload))
        sql = f"INSERT INTO reservations ({cols}) VALUES ({placeholders})"
        cursor.execute(sql, list(db_payload.values()))
        new_db_id = cursor.lastrowid
        conn.commit()
        
        app_logger.info(f"REZERVASYON DB (ID:{new_db_id}): {reservation_id_str} başarıyla oluşturuldu.")
        
        socketio_data = {"id": new_db_id, "reservation_id": reservation_id_str, "guest_name": guest_name, "guest_surname": guest_surname, "room_type": room_type, "check_in_date": check_in_date, "check_out_date": check_out_date, "status": STATUS_AI_PENDING_HUMAN, "hotel_name": hotel_name_str}
        broadcast_socketio_event(event_type="new_reservation", data=socketio_data)
        
        email_sent = send_reservation_email(guest_email, f"{guest_name} {guest_surname}", db_payload)
        email_status_msg = "Detaylar e-posta adresinize gönderildi." if guest_email and email_sent else ("E-posta adresi belirtilmediği için bilgilendirme yapılamadı." if not guest_email else "E-posta gönderiminde bir sorun oluştu.")
        
        user_message = (f"Harika {guest_name} Bey/Hanım! Rezervasyon talebiniz başarıyla alınmıştır. Onay numaranız: {reservation_id_str}. Ekibimiz, rezervasyonunuzu teyit etmek için en kısa sürede sizinle iletişime geçecektir. {email_status_msg} Başka bir konuda yardımcı olabilir miyim?")
        return {"success": True, "confirmation_number": reservation_id_str, "message_to_user": user_message}
    
    except sqlite3.Error as e:
        if conn: conn.rollback()
        app_logger.exception(f"create_reservation içinde veritabanı hatası: {e}")
        return {"success": False, "message_to_user": f"Rezervasyon sırasında beklenmedik bir veritabanı hatası oluştu."}
    finally:
        if conn:
            conn.close()

def execute_find_nearby_places_impl(place_type: str = None, keyword: str = None, rank_by_distance: bool = False, session_config: dict = None, session_id: str = None):
    # Bu fonksiyon zaten stateless. Sadece loglama ekliyoruz.
    app_logger.info(f"FUNC_CALL (find_nearby_places): Tip='{place_type}', Anahtar Kelime='{keyword}'")
    if not session_config: return {"success": False, "message_to_user": "Otel bilgileri olmadan arama yapamam."}

    lat, lon = session_config.get("hotelLatitude"), session_config.get("hotelLongitude")
    if not lat or not lon: return {"success": False, "message_to_user": "Bu otelin konum bilgileri (enlem/boylam) sistemde kayıtlı değil, bu yüzden çevrede arama yapamıyorum."}

    # ... (type_mapping ve diğer mantık aynı kalabilir) ...
    results = get_nearby_places(latitude=float(lat), longitude=float(lon), place_type=place_type, keyword=keyword, rank_by_distance=rank_by_distance)
    return results

def execute_get_directions_impl(destination: str, travel_mode: str = "driving", session_config: dict = None, session_id: str = None):
    # Bu fonksiyon zaten stateless. Sadece loglama ekliyoruz.
    app_logger.info(f"FUNC_CALL (get_directions): Hedef='{destination}', Mod='{travel_mode}'")
    lat, lon = session_config.get("hotelLatitude"), session_config.get("hotelLongitude")
    if not lat or not lon: return {"success": False, "message_to_user": "Otelin konum bilgileri olmadan yol tarifi veremem."}

    results = get_directions_to_destination(origin_lat=float(lat), origin_lon=float(lon), destination_query=destination, travel_mode=travel_mode)
    return results
        
        
# Araç isimlerini uygulama fonksiyonlarına eşleyen sözlük
tool_executors = {
    "get_room_availability": execute_get_room_availability_impl,
    "get_room_details": execute_get_room_details_impl,
    "show_room_images": execute_show_room_images_impl,
    "create_reservation": execute_create_reservation_impl,
    "find_nearby_places": execute_find_nearby_places_impl,
    "get_directions": execute_get_directions_impl
}