# app/config.py (<PERSON><PERSON>, Loglama Entegreli Hali)

import os
import logging
import google.generativeai as genai

# === LOGGING YAPILANDIRMASI ===
# Bu dosya, uygulama logger'ı oluşturulmadan önce çalıştığı için,
# kendi temel logger'ını kullanır. Gunicorn bu logları yakalayacaktır.
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
# ==============================

# --- Or<PERSON>i<PERSON>kenleri ---
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
SMTP_SERVER = os.getenv("SMTP_SERVER")
SMTP_PORT_STR = os.getenv("SMTP_PORT")
SMTP_SENDER_EMAIL = os.getenv("SMTP_SENDER_EMAIL")
SMTP_SENDER_PASSWORD = os.getenv("SMTP_SENDER_PASSWORD")
FLASK_RUN_HOST = os.getenv("FLASK_RUN_HOST", "0.0.0.0")
FLASK_RUN_PORT = int(os.getenv("FLASK_RUN_PORT", 5000))
FLASK_DEBUG_MODE = os.getenv('FLASK_DEBUG', 'True').lower() in ('true', '1', 't')
FLASK_SECRET_KEY = os.getenv('FLASK_SECRET_KEY', 'your_very_secret_fallback_key_for_socketio!')

# Google OAuth için ortam değişkenleri
GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID")
GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET")
GOOGLE_REDIRECT_URI = os.getenv("GOOGLE_REDIRECT_URI") 

# Google Servis Hesabı için
GOOGLE_APPLICATION_CREDENTIALS = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
if GOOGLE_APPLICATION_CREDENTIALS:
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = GOOGLE_APPLICATION_CREDENTIALS
    if not os.path.exists(GOOGLE_APPLICATION_CREDENTIALS):
        # Düzeltme: `print` yerine `logging.warning`
        logging.warning(f"GOOGLE_APPLICATION_CREDENTIALS olarak belirtilen dosya yolu '{GOOGLE_APPLICATION_CREDENTIALS}' bulunamadı.")

# Facebook OAuth Ayarları
FACEBOOK_APP_ID = os.getenv("FACEBOOK_APP_ID")
FACEBOOK_APP_SECRET = os.getenv("FACEBOOK_APP_SECRET")
FACEBOOK_REDIRECT_URI = os.getenv("FACEBOOK_REDIRECT_URI")

# Veritabanı şifrelemesi için anahtar
ENCRYPTION_KEY = os.getenv("ENCRYPTION_KEY")
if not ENCRYPTION_KEY:
    # Düzeltme: `print` yerine `logging.critical`
    logging.critical("KRİTİK UYARI: ENCRYPTION_KEY .env dosyasında bulunamadı! Hassas veriler güvende olmayacak.")

# --- Uygulama Sabitleri ---
CONFIG_DIR = "hotel_configs"
DATABASE_FILE = "hotel_reservations.db"
MODEL_NAME = "gemini-2.5-flash-preview-05-20" # Önceki model adı gemini-2.5-flash-preview-05-20 idi. Gemini 1.5 daha yeni ve genellikle daha iyidir.
SESSION_TIMEOUT_SECONDS = 30 * 60

# Rezervasyon Statü Kodları
STATUS_AI_PENDING_HUMAN = "AI_PENDING_HUMAN"
STATUS_HUMAN_CONFIRMED = "HUMAN_CONFIRMED"
STATUS_CANCELLED_GUEST = "CANCELLED_GUEST"
STATUS_CANCELLED_HOTEL = "CANCELLED_HOTEL"
STATUS_COMPLETED = "COMPLETED"
STATUS_NO_SHOW = "NO_SHOW"

# Google Maps Platform API Anahtarı
PLACES_API_KEY = os.getenv("PLACES_API_KEY")

# --- Gemini API Yapılandırması ---
if not GEMINI_API_KEY:
    # Düzeltme: `print` yerine `logging.critical`
    logging.critical("KRİTİK HATA: GEMINI_API_KEY .env dosyasında bulunamadı! Uygulama düzgün çalışmayacak.")
else:
    try:
        genai.configure(api_key=GEMINI_API_KEY)
        logging.info("Gemini API anahtarı başarıyla yapılandırıldı.")
    except Exception as e:
        # Düzeltme: `print` yerine `logging.critical`
        logging.critical(f"KRİTİK HATA: Gemini API anahtarı yapılandırılamadı: {e}")

# Redis Yapılandırması
REDIS_HOST = os.getenv('REDIS_HOST', '127.0.0.1')
REDIS_PORT = int(os.getenv('REDIS_PORT', 6379))
SESSION_REDIS_PREFIX = "chat_session:"