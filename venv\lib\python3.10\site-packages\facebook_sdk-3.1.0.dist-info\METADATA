Metadata-Version: 2.1
Name: facebook-sdk
Version: 3.1.0
Summary: This client library is designed to support the Facebook Graph API and the official Facebook JavaScript SDK, which is the canonical way to implement Facebook authentication.
Home-page: https://github.com/mobolic/facebook-sdk
Author: Facebook
Maintainer: <PERSON><PERSON>
Maintainer-email: <EMAIL>
License: Apache
Platform: UNKNOWN
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Requires-Dist: requests

===================
Facebook Python SDK
===================

This client library is designed to support the `Facebook Graph API`_ and the
official `Facebook JavaScript SDK`_, which is the canonical way to implement
Facebook authentication. You can read more about the Graph API by accessing its
`official documentation`_.

.. _Facebook Graph API: https://developers.facebook.com/docs/reference/api/
.. _Facebook JavaScript SDK: https://developers.facebook.com/docs/reference/javascript/
.. _official documentation: https://developers.facebook.com/docs/reference/api/

Licensing
=========

This library uses the `Apache License, version 2.0`_. Please see the library's
individual files for more information.

.. _Apache License, version 2.0: https://www.apache.org/licenses/LICENSE-2.0

Reporting Issues
================

If you have bugs or other issues specifically pertaining to this library, file
them `here`_. Bugs with the Graph API should be filed on `Facebook's
bugtracker`_.

.. _here: https://github.com/mobolic/facebook-sdk/issues
.. _Facebook's bugtracker: https://developers.facebook.com/bugs/


Support & Discussion
====================

Documentation is available at https://facebook-sdk.readthedocs.io/en/latest/.

Have a question? Need help? Visit the library's `Google Group`_.

.. _Google Group: https://groups.google.com/group/pythonforfacebook


