# app/analysis.py (Çoklu Otel Destekli ve Loglama Entegreli Hali)

import sqlite3
import json
from datetime import datetime, timedelta
import google.generativeai as genai

# Gerekli modülleri ve nesneleri içe aktar
from app import app_logger # Merkezi logger'ımızı import ediyoruz.
from app.database import get_db_connection
from app.config import MODEL_NAME

def generate_analysis_prompt(chat_logs: list[dict], hotel_name: str, period_str: str) -> str:
    """Verilen sohbet kayıtları için Gemini'ye gönderilecek analiz prompt'unu oluşturur."""
    formatted_logs = "\n".join([f"- {log['sender']} ({log['timestamp']}): {log['message']}" for log in chat_logs])

    prompt = f"""
    Sen, '{hotel_name}' oteli için bir veri analistisin. Görevin, sana verilen {period_str} müşteri sohbet kayıtlarını analiz ederek yönetim için anlamlı, eyleme geçirilebilir içgörüler çıkarmaktır. Analizini aşağıdaki JSON formatında ve SADECE JSON olarak döndür. Başka hiçbir metin ekleme.

    Sohbet Kayıtları:
    ---
    {formatted_logs}
    ---

    JSON Formatı:
    {{
      "overall_sentiment": {{
        "score": <float, -1.0 (çok negatif) ile 1.0 (çok pozitif) arası bir puan>,
        "summary": "<string, genel duygunun 1-2 cümlelik özeti>"
      }},
      "key_topics": [
        {{
          "topic": "<string, en sık konuşulan ana konu (örn: 'Oda Fiyatları', 'Rezervasyon Süreci', 'Oda Özellikleri')>",
          "mentions": <int, bu konudan kaç kez bahsedildiği>,
          "sentiment": "<string, 'Positive', 'Negative', 'Neutral'>"
        }}
      ],
      "frequently_asked_questions": [
        {{
          "question": "<string, sıkça sorulan bir soru>",
          "count": <int, bu sorunun kaç kez sorulduğu>
        }}
      ],
      "actionable_insights": {{
        "positives": [
          "<string, müşterilerin en çok memnun kaldığı ve devam ettirilmesi gereken bir nokta>"
        ],
        "improvement_areas": [
          "<string, müşterilerin zorlandığı veya şikayet ettiği ve iyileştirilmesi gereken bir nokta>"
        ]
      }},
      "executive_summary": "<string, tüm analizin yönetici için 2-3 cümlelik kısa ve net özeti>"
    }}
    """
    return prompt

def run_weekly_analysis():
    """
    Son 7 güne ait sohbetleri OTEL BAZINDA analiz eder ve sonucu veritabanına kaydeder.
    Bu fonksiyon, bir zamanlayıcı tarafından periyodik olarak çalıştırılmak üzere tasarlanmıştır.
    """
    app_logger.info("Başladı: Haftalık analiz görevi.")
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # 1. Analiz edilecek tüm otelleri (hotel_id_key) veritabanından al.
        cursor.execute("SELECT DISTINCT hotel_id_key, hotel_name FROM hotels WHERE hotel_id_key IS NOT NULL")
        hotels_to_analyze = cursor.fetchall()
        
        if not hotels_to_analyze:
            app_logger.info("Analiz edilecek kayıtlı otel bulunamadı. Görev sonlandırılıyor.")
            return

        # 2. Her otel için döngü başlat.
        for hotel in hotels_to_analyze:
            hotel_id_key = hotel['hotel_id_key']
            hotel_name = hotel['hotel_name']
            app_logger.info(f"Analiz ediliyor: Otel '{hotel_name}' (ID: {hotel_id_key})")
            
            # Analiz edilecek tarih aralığını belirle
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)

            # Sadece bu otele ait sohbet geçmişini veritabanından çek.
            # session_id'den hotel_config_key'i almak için bir JOIN işlemi gerekebilir,
            # şimdilik chat_history'de source'a güvenelim veya basit tutalım.
            # NOT: Daha sağlam bir yapı için chat_history tablosuna hotel_id_key sütunu eklenebilir.
            # Şimdilik, session'ların hangi otele ait olduğunu bilmediğimiz için geçici olarak
            # tüm sohbetleri alıp, config'den otel adını kullanıyoruz.
            # TODO: Bu kısım, chat_history tablosu güncellendiğinde iyileştirilecek.
            
            # Geçici çözüm: Tüm sohbetleri alıp, prompt'ta otel adını kullan
            cursor.execute(
                "SELECT sender, message, timestamp FROM chat_history WHERE timestamp >= ? AND timestamp <= ?",
                (start_date.isoformat(), end_date.isoformat())
            )
            chat_logs_raw = cursor.fetchall()
            
            if not chat_logs_raw:
                app_logger.info(f"Otel '{hotel_name}' için analiz edilecek yeni sohbet kaydı bulunamadı.")
                continue # Bir sonraki otele geç

            chat_logs = [dict(row) for row in chat_logs_raw]
            period_str = f"{start_date.strftime('%Y-%m-%d')} - {end_date.strftime('%Y-%m-%d')} dönemi"
            analysis_prompt = generate_analysis_prompt(chat_logs, hotel_name, period_str)

            try:
                model = genai.GenerativeModel(MODEL_NAME)
                response = model.generate_content(analysis_prompt)
                
                response_text = response.text.strip().replace("```json", "").replace("```", "").strip()
                analysis_data = json.loads(response_text)

                cursor.execute(
                    """
                    INSERT INTO dashboard_analysis_reports 
                    (report_type, start_date, end_date, generated_at, report_data_json, report_summary_text, hotel_config_key)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                    """,
                    (
                        'WEEKLY',
                        start_date.isoformat(),
                        end_date.isoformat(),
                        datetime.now().isoformat(),
                        json.dumps(analysis_data, ensure_ascii=False),
                        analysis_data.get("executive_summary", "Özet alınamadı."),
                        hotel_id_key # Hangi otele ait olduğunu kaydet
                    )
                )
                conn.commit()
                app_logger.info(f"Başarılı: '{hotel_name}' için haftalık analiz raporu veritabanına kaydedildi.")

            except json.JSONDecodeError:
                app_logger.error(f"AI yanıtı (Otel: {hotel_name}) JSON olarak parse edilemedi. AI Ham Yanıtı: {response.text}")
                continue # Hata olursa diğer otellerin analizini engelleme
            except Exception:
                app_logger.exception(f"Otel '{hotel_name}' için analiz sırasında beklenmedik bir hata oluştu:")
                continue # Hata olursa diğer otellerin analizini engelleme

    except sqlite3.Error:
        app_logger.exception("Haftalık analiz görevi sırasında veritabanı hatası oluştu:")
    finally:
        if conn:
            conn.close()
        app_logger.info("Tamamlandı: Haftalık analiz görevi.")