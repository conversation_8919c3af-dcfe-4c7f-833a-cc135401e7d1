import { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Alert,
  Avatar
} from '@mui/material';
import {
  Hotel as HotelIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import { useApi } from '../contexts/ApiContext';

const HotelSettingsSimple = () => {
  const { getHotelConfig, updateHotelConfig, currentHotelId } = useApi();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  
  const [formData, setFormData] = useState({
    hotelName: '',
    hotelCity: '',
    hotelAddress: '',
    hotelPhone: '',
    hotelEmail: '',
    hotelDescription: ''
  });

  useEffect(() => {
    fetchHotelConfig();
  }, [currentHotelId]);

  const fetchHotelConfig = async () => {
    try {
      setLoading(true);
      const config = await getHotelConfig(currentHotelId);
      
      setFormData({
        hotelName: config.hotelName || '',
        hotelCity: config.hotelCity || '',
        hotelAddress: config.hotelAddress || '',
        hotelPhone: config.hotelPhone || '',
        hotelEmail: config.hotelEmail || '',
        hotelDescription: config.hotelDescription || ''
      });
    } catch (err) {
      console.error('Hotel config fetch error:', err);
      setError('Otel konfigürasyonu yüklenirken hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);
      
      const configData = {
        hotelId: currentHotelId,
        ...formData
      };
      
      await updateHotelConfig(configData);
      setSuccess('Otel konfigürasyonu başarıyla güncellendi!');
      
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Hotel config update error:', err);
      setError('Otel konfigürasyonu güncellenirken hata oluştu.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Otel konfigürasyonu yükleniyor...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={4}>
        <Box display="flex" alignItems="center" mb={2}>
          <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
            <HotelIcon />
          </Avatar>
          <Box>
            <Typography variant="h4" fontWeight="600">
              Otel Ayarları (Basit)
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Temel otel bilgilerini düzenleyin
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Alerts */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      {/* Form */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                🏨 Temel Bilgiler
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Otel Adı"
                    value={formData.hotelName}
                    onChange={(e) => handleInputChange('hotelName', e.target.value)}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Şehir"
                    value={formData.hotelCity}
                    onChange={(e) => handleInputChange('hotelCity', e.target.value)}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Adres"
                    multiline
                    rows={2}
                    value={formData.hotelAddress}
                    onChange={(e) => handleInputChange('hotelAddress', e.target.value)}
                  />
                </Grid>
                
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Telefon"
                    value={formData.hotelPhone}
                    onChange={(e) => handleInputChange('hotelPhone', e.target.value)}
                  />
                </Grid>
                
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Email"
                    type="email"
                    value={formData.hotelEmail}
                    onChange={(e) => handleInputChange('hotelEmail', e.target.value)}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Otel Açıklaması"
                    multiline
                    rows={3}
                    value={formData.hotelDescription}
                    onChange={(e) => handleInputChange('hotelDescription', e.target.value)}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 3 }}>
                📊 Mevcut Veriler
              </Typography>
              
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                <strong>Hotel ID:</strong> {currentHotelId}
              </Typography>
              
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                <strong>Otel Adı:</strong> {formData.hotelName || 'Belirtilmemiş'}
              </Typography>
              
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                <strong>Şehir:</strong> {formData.hotelCity || 'Belirtilmemiş'}
              </Typography>
              
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                <strong>Telefon:</strong> {formData.hotelPhone || 'Belirtilmemiş'}
              </Typography>
              
              <Typography variant="body2" color="text.secondary">
                <strong>Email:</strong> {formData.hotelEmail || 'Belirtilmemiş'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Kaydet Butonu */}
      <Box mt={4} textAlign="center">
        <Button
          variant="contained"
          size="large"
          startIcon={<SaveIcon />}
          onClick={handleSave}
          disabled={saving}
          sx={{
            px: 4,
            py: 1.5,
            background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #388e3c 0%, #2e7d32 100%)',
            }
          }}
        >
          {saving ? 'Kaydediliyor...' : 'Kaydet'}
        </Button>
      </Box>
    </Box>
  );
};

export default HotelSettingsSimple;
